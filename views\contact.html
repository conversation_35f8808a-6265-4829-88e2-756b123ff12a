<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us - Caribbean Advantage | Channel 99-9</title>
    <meta name="description" content="Get in touch with Caribbean Advantage TV Channel 99-9. Contact us for inquiries, support, or collaboration opportunities.">

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Styles -->
    <link rel="stylesheet" href="/css/styles.css">

    <style>
        .contact-hero {
            background: linear-gradient(135deg, var(--steel-blue) 0%, var(--ocean-blue) 50%, var(--mint-accent) 100%);
            position: relative;
            overflow: hidden;
        }

        .contact-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="contact" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="2" fill="white" opacity="0.1"/><circle cx="5" cy="5" r="1" fill="white" opacity="0.05"/><circle cx="25" cy="25" r="1" fill="white" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23contact)"/></svg>');
            pointer-events: none;
        }

        .contact-form {
            background: var(--white);
            border-radius: var(--radius-xl);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-xl);
            border: 1px solid var(--medium-gray);
        }

        .form-group {
            margin-bottom: var(--spacing-lg);
        }

        .form-label {
            display: block;
            font-weight: 600;
            color: var(--deep-ocean);
            margin-bottom: var(--spacing-xs);
            font-size: 1rem;
        }

        .form-input {
            width: 100%;
            padding: var(--spacing-md);
            border: 2px solid var(--medium-gray);
            border-radius: var(--radius-md);
            font-size: 1rem;
            font-family: var(--font-primary);
            transition: all var(--transition-normal);
            background: var(--white);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--ocean-blue);
            box-shadow: 0 0 0 3px rgba(0, 119, 190, 0.1);
        }

        .form-textarea {
            min-height: 120px;
            resize: vertical;
        }

        .contact-info-card {
            background: linear-gradient(135deg, var(--white) 0%, var(--powder-blue) 100%);
            border-radius: var(--radius-xl);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--sky-blue);
            height: fit-content;
        }

        .contact-item {
            display: flex;
            align-items: flex-start;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
            padding: var(--spacing-md);
            background: var(--white);
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-sm);
            transition: all var(--transition-fast);
        }

        .contact-item:hover {
            transform: translateX(8px);
            box-shadow: var(--shadow-md);
        }

        .contact-item:last-child {
            margin-bottom: 0;
        }

        .contact-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--ocean-blue), var(--steel-blue));
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            flex-shrink: 0;
        }

        .contact-details h4 {
            color: var(--deep-ocean);
            font-weight: 600;
            margin-bottom: var(--spacing-xs);
        }

        .contact-details p {
            color: var(--slate-gray);
            margin: 0;
            line-height: 1.4;
        }

        .social-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-lg);
            margin-top: var(--spacing-xl);
        }

        .social-card {
            background: var(--white);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            text-align: center;
            box-shadow: var(--shadow-md);
            border: 2px solid transparent;
            transition: all var(--transition-normal);
            text-decoration: none;
            color: inherit;
        }

        .social-card:hover {
            transform: translateY(-6px);
            border-color: var(--ocean-blue);
            box-shadow: var(--shadow-lg);
            text-decoration: none;
            color: inherit;
        }

        .social-icon {
            width: 60px;
            height: 60px;
            margin: 0 auto var(--spacing-md);
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8rem;
            color: white;
        }

        .social-facebook .social-icon {
            background: linear-gradient(135deg, #1877F2, #42A5F5);
        }

        .social-twitter .social-icon {
            background: linear-gradient(135deg, #1DA1F2, #42A5F5);
        }

        .social-instagram .social-icon {
            background: linear-gradient(135deg, #E4405F, #FCAF45);
        }

        .social-youtube .social-icon {
            background: linear-gradient(135deg, #FF0000, #FF6B6B);
        }

        .social-title {
            font-weight: 600;
            color: var(--deep-ocean);
            margin-bottom: var(--spacing-xs);
        }

        .social-handle {
            color: var(--slate-gray);
            font-size: 0.9rem;
        }

        .map-container {
            background: var(--white);
            border-radius: var(--radius-xl);
            padding: var(--spacing-lg);
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--medium-gray);
        }

        .map-placeholder {
            width: 100%;
            height: 300px;
            background: linear-gradient(135deg, var(--light-gray), var(--powder-blue));
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            color: var(--slate-gray);
            border: 2px dashed var(--medium-gray);
        }

        .map-placeholder .icon {
            font-size: 3rem;
            margin-bottom: var(--spacing-sm);
        }

        .about-section {
            background: linear-gradient(135deg, var(--deep-ocean), var(--ocean-blue));
            color: white;
            border-radius: var(--radius-xl);
            padding: var(--spacing-xl);
            position: relative;
            overflow: hidden;
        }

        .about-section::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 300px;
            height: 300px;
            background: radial-gradient(circle, rgba(78, 205, 196, 0.2) 0%, transparent 70%);
            transform: translate(50%, -50%);
        }

        .about-content {
            position: relative;
            z-index: 2;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: var(--spacing-lg);
            margin-top: var(--spacing-xl);
        }

        .stat-item {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 800;
            color: var(--mint-accent);
            margin-bottom: var(--spacing-xs);
        }

        .stat-label {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
        }

        .form-success {
            background: var(--success-green);
            color: white;
            padding: var(--spacing-md);
            border-radius: var(--radius-md);
            margin-bottom: var(--spacing-lg);
            display: none;
        }

        .form-error {
            background: var(--coral-accent);
            color: white;
            padding: var(--spacing-md);
            border-radius: var(--radius-md);
            margin-bottom: var(--spacing-lg);
            display: none;
        }
    </style>
</head>
<body>
    <!-- Modern Navigation -->
    <nav class="navbar" id="navbar">
        <div class="container">
            <div class="nav-container">
                <a href="/" class="nav-logo">
                    <div class="nav-logo-icon">CA</div>
                    <span>Caribbean Advantage</span>
                </a>

                <ul class="nav-menu" id="nav-menu">
                    <li><a href="/" class="nav-link">Home</a></li>
                    <li><a href="/live" class="nav-link">Live TV</a></li>
                    <li><a href="/gamezone" class="nav-link">GameZone</a></li>
                    <li><a href="/radio" class="nav-link">Radio</a></li>
                    <li><a href="/events" class="nav-link">Events</a></li>
                    <li><a href="/contact" class="nav-link active">Contact</a></li>
                </ul>

                <button class="nav-toggle" id="nav-toggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </div>
    </nav>

    <!-- Contact Hero -->
    <section class="hero contact-hero">
        <div class="container">
            <div class="hero-content">
                <h1>Get in Touch</h1>
                <p>Connect with Caribbean Advantage TV Channel 99-9. We'd love to hear from you about our programming, partnerships, or any questions you may have.</p>

                <div class="d-flex justify-center" style="gap: var(--spacing-md); flex-wrap: wrap;">
                    <a href="#contact-form" class="btn btn-primary">
                        📧 Send Message
                    </a>
                    <a href="#contact-info" class="btn btn-secondary">
                        📞 Contact Info
                    </a>
                    <a href="#social" class="btn btn-ghost">
                        🌐 Follow Us
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Form & Info Section -->
    <section class="section" id="contact-form">
        <div class="container">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-xl); align-items: start;">
                <!-- Contact Form -->
                <div class="contact-form">
                    <h2 style="color: var(--deep-ocean); margin-bottom: var(--spacing-lg);">Send us a Message</h2>

                    <div class="form-success" id="form-success">
                        ✅ Thank you! Your message has been sent successfully. We'll get back to you soon.
                    </div>

                    <div class="form-error" id="form-error">
                        ❌ Sorry, there was an error sending your message. Please try again.
                    </div>

                    <form id="contact-form-element">
                        <div class="form-group">
                            <label for="name" class="form-label">Full Name *</label>
                            <input type="text" id="name" name="name" class="form-input" required>
                        </div>

                        <div class="form-group">
                            <label for="email" class="form-label">Email Address *</label>
                            <input type="email" id="email" name="email" class="form-input" required>
                        </div>

                        <div class="form-group">
                            <label for="phone" class="form-label">Phone Number</label>
                            <input type="tel" id="phone" name="phone" class="form-input">
                        </div>

                        <div class="form-group">
                            <label for="subject" class="form-label">Subject *</label>
                            <select id="subject" name="subject" class="form-input" required>
                                <option value="">Select a subject</option>
                                <option value="general">General Inquiry</option>
                                <option value="programming">Programming Feedback</option>
                                <option value="advertising">Advertising Opportunities</option>
                                <option value="partnership">Partnership Proposal</option>
                                <option value="technical">Technical Support</option>
                                <option value="events">Event Collaboration</option>
                                <option value="other">Other</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="message" class="form-label">Message *</label>
                            <textarea id="message" name="message" class="form-input form-textarea" required placeholder="Tell us how we can help you..."></textarea>
                        </div>

                        <button type="submit" class="btn btn-primary w-full justify-center">
                            📧 Send Message
                        </button>
                    </form>
                </div>

                <!-- Contact Information -->
                <div class="contact-info-card" id="contact-info">
                    <h3 style="color: var(--deep-ocean); margin-bottom: var(--spacing-lg);">Contact Information</h3>

                    <div class="contact-item">
                        <div class="contact-icon">📍</div>
                        <div class="contact-details">
                            <h4>Studio Location</h4>
                            <p>Caribbean Advantage TV<br>
                            North Coast Puerto Rico<br>
                            Channel 99-9 Broadcasting Center</p>
                        </div>
                    </div>

                    <div class="contact-item">
                        <div class="contact-icon">📞</div>
                        <div class="contact-details">
                            <h4>Phone</h4>
                            <p>Main: (*************<br>
                            Studio: (*************<br>
                            Emergency: (*************</p>
                        </div>
                    </div>

                    <div class="contact-item">
                        <div class="contact-icon">📧</div>
                        <div class="contact-details">
                            <h4>Email</h4>
                            <p>General: <EMAIL><br>
                            Programming: <EMAIL><br>
                            Advertising: <EMAIL></p>
                        </div>
                    </div>

                    <div class="contact-item">
                        <div class="contact-icon">🕒</div>
                        <div class="contact-details">
                            <h4>Business Hours</h4>
                            <p>Monday - Friday: 8:00 AM - 6:00 PM<br>
                            Saturday: 9:00 AM - 4:00 PM<br>
                            Sunday: Closed (Emergency only)</p>
                        </div>
                    </div>

                    <div class="contact-item">
                        <div class="contact-icon">📻</div>
                        <div class="contact-details">
                            <h4>Broadcasting</h4>
                            <p>TV: Channel 99-9<br>
                            Radio: 99.9 FM<br>
                            Online: 24/7 Live Streaming</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section class="section">
        <div class="container">
            <div class="about-section">
                <div class="about-content">
                    <h2 style="font-size: 2.5rem; margin-bottom: var(--spacing-md);">About Caribbean Advantage TV</h2>
                    <p style="font-size: 1.2rem; margin-bottom: var(--spacing-lg); color: rgba(255,255,255,0.9);">
                        Caribbean Advantage TV Channel 99-9 is the premier destination for authentic Caribbean entertainment,
                        broadcasting from the beautiful North Coast of Puerto Rico. We're committed to showcasing the rich
                        culture, vibrant music, and diverse stories of the Caribbean community.
                    </p>

                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-number">24/7</div>
                            <div class="stat-label">Broadcasting</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">50K+</div>
                            <div class="stat-label">Daily Viewers</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">15+</div>
                            <div class="stat-label">Years Experience</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">100%</div>
                            <div class="stat-label">Caribbean Content</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Social Media Section -->
    <section class="section" style="background: var(--light-gray);" id="social">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Follow Us on Social Media</h2>
                <p class="section-subtitle">Stay connected with Caribbean Advantage TV across all platforms for the latest updates and content.</p>
            </div>

            <div class="social-links">
                <a href="#" class="social-card social-facebook">
                    <div class="social-icon">📘</div>
                    <h4 class="social-title">Facebook</h4>
                    <p class="social-handle">@CaribbeanAdvantageTV</p>
                    <p style="color: var(--slate-gray); font-size: 0.9rem; margin-top: var(--spacing-xs);">Follow for daily updates and live streams</p>
                </a>

                <a href="#" class="social-card social-twitter">
                    <div class="social-icon">🐦</div>
                    <h4 class="social-title">Twitter</h4>
                    <p class="social-handle">@CaribbeanAdvTV</p>
                    <p style="color: var(--slate-gray); font-size: 0.9rem; margin-top: var(--spacing-xs);">Real-time news and program updates</p>
                </a>

                <a href="#" class="social-card social-instagram">
                    <div class="social-icon">📷</div>
                    <h4 class="social-title">Instagram</h4>
                    <p class="social-handle">@caribbeanadvantage</p>
                    <p style="color: var(--slate-gray); font-size: 0.9rem; margin-top: var(--spacing-xs);">Behind-the-scenes and visual content</p>
                </a>

                <a href="#" class="social-card social-youtube">
                    <div class="social-icon">📺</div>
                    <h4 class="social-title">YouTube</h4>
                    <p class="social-handle">Caribbean Advantage TV</p>
                    <p style="color: var(--slate-gray); font-size: 0.9rem; margin-top: var(--spacing-xs);">Full episodes and exclusive content</p>
                </a>
            </div>
        </div>
    </section>

    <!-- Map Section -->
    <section class="section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Find Our Studio</h2>
                <p class="section-subtitle">Visit us at our broadcasting center located on the beautiful North Coast of Puerto Rico.</p>
            </div>

            <div class="map-container">
                <div class="map-placeholder">
                    <div class="icon">🗺️</div>
                    <h3 style="margin-bottom: var(--spacing-sm);">Interactive Map</h3>
                    <p>Caribbean Advantage TV Studio</p>
                    <p>North Coast Puerto Rico</p>
                    <p style="margin-top: var(--spacing-md); font-size: 0.9rem;">
                        <strong>Directions:</strong> Located in the heart of North Coast Puerto Rico,
                        easily accessible from major highways and public transportation.
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>Caribbean Advantage TV</h4>
                    <p>Modern Caribbean entertainment on Channel 99-9. Broadcasting live from North Coast Puerto Rico.</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <p><a href="/live">Live TV</a></p>
                    <p><a href="/radio">Radio</a></p>
                    <p><a href="/gamezone">GameZone</a></p>
                    <p><a href="/events">Events</a></p>
                </div>
                <div class="footer-section">
                    <h4>Contact</h4>
                    <p>Channel 99-9</p>
                    <p>North Coast Puerto Rico</p>
                    <p><EMAIL></p>
                </div>
                <div class="footer-section">
                    <h4>Follow Us</h4>
                    <p><a href="#">Facebook</a></p>
                    <p><a href="#">Twitter</a></p>
                    <p><a href="#">Instagram</a></p>
                    <p><a href="#">YouTube</a></p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 Caribbean Advantage TV. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="/js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Navigation functionality
            const navbar = document.getElementById('navbar');
            const navToggle = document.getElementById('nav-toggle');
            const navMenu = document.getElementById('nav-menu');

            window.addEventListener('scroll', function() {
                if (window.scrollY > 50) {
                    navbar.classList.add('scrolled');
                } else {
                    navbar.classList.remove('scrolled');
                }
            });

            navToggle.addEventListener('click', function() {
                navToggle.classList.toggle('active');
                navMenu.classList.toggle('active');
            });

            // Contact form functionality
            const contactForm = document.getElementById('contact-form-element');
            const successMessage = document.getElementById('form-success');
            const errorMessage = document.getElementById('form-error');

            contactForm.addEventListener('submit', function(e) {
                e.preventDefault();

                // Hide previous messages
                successMessage.style.display = 'none';
                errorMessage.style.display = 'none';

                // Get form data
                const formData = new FormData(contactForm);
                const data = Object.fromEntries(formData);

                // Simulate form submission
                setTimeout(() => {
                    if (data.name && data.email && data.subject && data.message) {
                        successMessage.style.display = 'block';
                        contactForm.reset();

                        // Scroll to success message
                        successMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    } else {
                        errorMessage.style.display = 'block';
                        errorMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                }, 1000);
            });
        });
    </script>
</body>
</html>
