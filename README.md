# Caribbean Advantage - Channel 99-9 📺

Your premier destination for Gaming, Online Radio, and Live TV covering North Coast Puerto Rico.

## 🌟 Features

- **Live TV Streaming** - Channel 99-9 integrated with external player at `http://caribbeanadvantage.com/CA-tv.html`
- **Gaming Zone** - Live gaming streams, tournaments, and gaming content
- **Online Radio** - 24/7 Caribbean music, local artists, and live radio shows
- **Local Artist Platform** - Supporting Puerto Rican talent and showcasing local artists
- **Events & Showcases** - Gaming tournaments, music events, and community celebrations
- **Responsive Design** - Works perfectly on all devices
- **Modern UI/UX** - Sleek design with animations and gradients
- **Professional Animations** - Loading screens, particle effects, scroll animations
- **SEO Optimized** - Proper meta tags and structure

## 🚀 Quick Deploy to Heroku

### Option 1: One-Click Deploy
[![Deploy](https://www.herokucdn.com/deploy/button.svg)](https://heroku.com/deploy)

### Option 2: Manual Deployment

#### Prerequisites
- [Node.js](https://nodejs.org/) (v18 or higher)
- [Git](https://git-scm.com/)
- [Heroku CLI](https://devcenter.heroku.com/articles/heroku-cli)
- Heroku account

#### Step 1: Clone and Setup
```bash
# Clone the repository
git clone <your-repo-url>
cd caribbean-advantage-tv

# Install dependencies
npm install
```

#### Step 2: Test Locally
```bash
# Run the development server
npm run dev

# Or run production server
npm start

# Visit http://localhost:3000
```

#### Step 3: Deploy to Heroku
```bash
# Login to Heroku
heroku login

# Create a new Heroku app
heroku create your-app-name

# Set environment variables (optional)
heroku config:set NODE_ENV=production

# Deploy to Heroku
git add .
git commit -m "Initial deployment"
git push heroku main

# Open your app
heroku open
```

## 📱 Pages

- **Home** (`/`) - Landing page with Channel 99-9 info and featured content
- **Online TV** (`/live`) - Live streaming player for Channel 99-9
- **GameZone** (`/gamezone`) - Gaming content, tournaments, and live streams
- **Online Radio** (`/radio`) - 24/7 radio streaming and show schedules
- **Events** (`/events`) - Gaming tournaments, music showcases, and community events
- **Contact** (`/contact`) - Contact form, artist submissions, and information

## 📁 Project Structure

```
caribbean-advantage-tv/
├── 📄 server.js                    # Express.js server configuration
├── 📄 package.json                 # Node.js dependencies and scripts
├── 📄 Procfile                     # Heroku process definition
├── 📄 app.json                     # Heroku app configuration
├── 📄 .gitignore                   # Git ignore rules
├── 📄 .env.example                 # Environment variables template
├── 📄 README.md                    # Project documentation
├── 📄 DEPLOYMENT.md                # Deployment instructions
├── 📄 PROJECT_STRUCTURE.md         # Detailed structure guide
├── 📄 upload-to-github.ps1         # GitHub upload script
│
├── 📁 views/                       # HTML templates
│   ├── 📄 index.html               # Homepage
│   ├── 📄 live.html                # Live TV player
│   ├── 📄 gamezone.html            # Gaming content
│   ├── 📄 radio.html               # Online radio
│   ├── 📄 events.html              # Events and showcases
│   ├── 📄 contact.html             # Contact form
│   └── 📄 404.html                 # Error page
│
└── 📁 public/                      # Static assets
    ├── 📁 css/
    │   └── 📄 styles.css            # Custom CSS styles
    ├── 📁 js/
    │   ├── 📄 main.js               # Main JavaScript
    │   └── 📄 live-player.js        # Live player functionality
    └── 📄 favicon.ico               # Website favicon
```

## 🔧 Configuration

### Environment Variables
Set these in your Heroku dashboard or via CLI:

```bash
# Production environment
heroku config:set NODE_ENV=production

# Optional: Custom port (Heroku sets this automatically)
heroku config:set PORT=3000
```

### External Player Integration
The live TV player integrates with:
- **URL**: `http://caribbeanadvantage.com/CA-tv.html`
- **Features**: Fullscreen, refresh, new tab options
- **Responsive**: Adapts to all screen sizes

## 🎮 Live TV Features

- **External Player Integration** - Seamlessly embeds the external TV player
- **Fullscreen Support** - Toggle fullscreen mode
- **Error Handling** - Graceful error messages and retry options
- **Loading States** - Professional loading animations
- **Keyboard Shortcuts**:
  - `Ctrl/Cmd + F` - Toggle fullscreen
  - `Ctrl/Cmd + R` - Refresh player
- **Real-time Info** - Live viewer count and stream status

## 🛠️ Development

### Local Development
```bash
# Install dependencies
npm install

# Start development server with auto-reload
npm run dev

# Start production server
npm start
```

### Adding New Pages
1. Create new HTML file in root directory
2. Add route in `server.js`:
```javascript
app.get('/newpage', (req, res) => {
    res.sendFile(path.join(__dirname, 'newpage.html'));
});
```
3. Update navigation in all HTML files

### Customizing the Player
Edit `live.html` to modify:
- Player URL in the iframe `src` attribute
- Player controls and features
- Sidebar content and styling

## 🎨 Styling

The website uses:
- **Tailwind CSS** - Utility-first CSS framework
- **Custom CSS** - Advanced animations and effects
- **Google Fonts** - Poppins font family
- **Color Scheme**:
  - Primary Blue: `#003d6b`
  - Dark Blue: `#001f3f`
  - Light Blue: `#0066cc`
  - Yellow: `#ffc107`
  - Orange: `#ff9800`
  - Green: `#4caf50`

## 📱 Responsive Design

- **Mobile First** - Optimized for mobile devices
- **Tablet Support** - Perfect tablet experience
- **Desktop Enhanced** - Full desktop features
- **Touch Friendly** - Large touch targets

## 🔒 Security

- **Helmet.js** - Security headers
- **CORS** - Cross-origin resource sharing
- **CSP** - Content Security Policy
- **HTTPS Ready** - SSL/TLS support

## 📊 Performance

- **Compression** - Gzip compression enabled
- **Caching** - Static file caching
- **Optimized Assets** - Minified and compressed
- **CDN Ready** - Works with CDNs

## 🐛 Troubleshooting

### Common Issues

1. **Player not loading**:
   - Check if `http://caribbeanadvantage.com/CA-tv.html` is accessible
   - Verify CORS settings
   - Try refreshing the player

2. **Heroku deployment fails**:
   - Ensure Node.js version is specified in `package.json`
   - Check build logs: `heroku logs --tail`
   - Verify all dependencies are in `package.json`

3. **Mobile issues**:
   - Test on actual devices
   - Check viewport meta tag
   - Verify touch interactions

### Support
For issues and support:
- Check the browser console for errors
- Review Heroku logs: `heroku logs --tail`
- Test locally first: `npm start`

## 📄 License

MIT License - feel free to use and modify as needed.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

---

**Caribbean Advantage TV** - Bringing Caribbean culture to the world through gaming, anime, and cartoons! 🌴📺
