// Caribbean Advantage TV - Internationalization (i18n) System
// Multi-language support with location-based detection

class CaribbeanAdvantageI18n {
    constructor() {
        this.currentLanguage = 'en';
        this.supportedLanguages = ['en', 'es', 'fr', 'sw']; // English, Spanish, French, Swahili (African)
        this.translations = {};
        this.init();
    }

    async init() {
        console.log('🌍 Initializing Caribbean Advantage Multi-Language System...');
        
        // Load all translations
        await this.loadTranslations();
        
        // Detect user's preferred language
        this.detectLanguage();
        
        // Apply initial language
        this.applyLanguage(this.currentLanguage);
        
        // Setup language switcher
        this.setupLanguageSwitcher();
        
        console.log(`✅ Language system ready - Current: ${this.currentLanguage}`);
    }

    async loadTranslations() {
        // English (Default)
        this.translations.en = {
            // Navigation
            'nav.home': 'Home',
            'nav.online-tv': 'Online TV',
            'nav.gamezone': 'GameZone',
            'nav.radio': 'Online Radio',
            'nav.events': 'Events',
            'nav.contact': 'Contact',
            'nav.watch-live': '📺 Watch Live',
            'nav.channel': 'Channel 99-9',
            'nav.location': 'North Coast PR',
            
            // Homepage
            'home.title': 'Caribbean Advantage - Channel 99-9 | Gaming, Radio & Live TV',
            'home.description': 'Caribbean Advantage TV on Channel 99-9 covering North Coast Puerto Rico. Gaming, Online Radio, Live TV streaming, and local artist platform.',
            'home.hero.title': 'Caribbean Advantage',
            'home.hero.subtitle': 'Channel 99-9',
            'home.hero.description': 'Your premier destination for Gaming, Online Radio, and Live TV covering North Coast Puerto Rico. Supporting local artists and bringing you the best Caribbean entertainment 24/7.',
            'home.hero.watch-now': '🎬 Watch Now',
            'home.hero.online-radio': '📻 Online Radio',
            'home.content.title': 'Our Content',
            'home.content.description': 'Experience gaming, online radio, live TV, and support for local Puerto Rican artists on Channel 99-9.',
            'home.gamezone.title': 'GameZone',
            'home.gamezone.description': 'Live gaming streams, tournaments, and gaming content from Puerto Rico.',
            'home.radio.title': 'Online Radio',
            'home.radio.description': '24/7 Caribbean music, local artists, and live radio shows.',
            'home.tv.title': 'Channel 99-9',
            'home.tv.description': 'Live TV streaming covering North Coast Puerto Rico.',
            'home.artists.title': 'Local Artists',
            'home.artists.description': 'Supporting and showcasing talented Puerto Rican artists.',
            
            // Common
            'common.live': 'LIVE',
            'common.watch-live': 'Watch Live Now',
            'common.learn-more': 'Learn More',
            'common.contact-us': 'Contact Us',
            'common.submit': 'Submit',
            'common.loading': 'Loading...',
            'common.error': 'Error',
            'common.success': 'Success',
            
            // Footer
            'footer.description': 'Channel 99-9 covering North Coast Puerto Rico. Gaming, Online Radio, Live TV, and supporting local artists.',
            'footer.copyright': '© 2023 Caribbean Advantage. All rights reserved.',
            'footer.quick-links': 'Quick Links',
            'footer.contact': 'Contact',
            
            // Language Switcher
            'lang.english': 'English',
            'lang.spanish': 'Español',
            'lang.french': 'Français',
            'lang.african': 'Kiswahili'
        };

        // Spanish
        this.translations.es = {
            // Navigation
            'nav.home': 'Inicio',
            'nav.online-tv': 'TV en Línea',
            'nav.gamezone': 'Zona de Juegos',
            'nav.radio': 'Radio en Línea',
            'nav.events': 'Eventos',
            'nav.contact': 'Contacto',
            'nav.watch-live': '📺 Ver en Vivo',
            'nav.channel': 'Canal 99-9',
            'nav.location': 'Costa Norte PR',
            
            // Homepage
            'home.title': 'Caribbean Advantage - Canal 99-9 | Juegos, Radio y TV en Vivo',
            'home.description': 'Caribbean Advantage TV en Canal 99-9 cubriendo la Costa Norte de Puerto Rico. Juegos, Radio en Línea, transmisión de TV en vivo y plataforma de artistas locales.',
            'home.hero.title': 'Caribbean Advantage',
            'home.hero.subtitle': 'Canal 99-9',
            'home.hero.description': 'Tu destino principal para Juegos, Radio en Línea y TV en Vivo cubriendo la Costa Norte de Puerto Rico. Apoyando artistas locales y trayéndote el mejor entretenimiento caribeño 24/7.',
            'home.hero.watch-now': '🎬 Ver Ahora',
            'home.hero.online-radio': '📻 Radio en Línea',
            'home.content.title': 'Nuestro Contenido',
            'home.content.description': 'Experimenta juegos, radio en línea, TV en vivo y apoyo para artistas puertorriqueños locales en el Canal 99-9.',
            'home.gamezone.title': 'Zona de Juegos',
            'home.gamezone.description': 'Transmisiones de juegos en vivo, torneos y contenido de juegos desde Puerto Rico.',
            'home.radio.title': 'Radio en Línea',
            'home.radio.description': 'Música caribeña 24/7, artistas locales y programas de radio en vivo.',
            'home.tv.title': 'Canal 99-9',
            'home.tv.description': 'Transmisión de TV en vivo cubriendo la Costa Norte de Puerto Rico.',
            'home.artists.title': 'Artistas Locales',
            'home.artists.description': 'Apoyando y mostrando talentosos artistas puertorriqueños.',
            
            // Common
            'common.live': 'EN VIVO',
            'common.watch-live': 'Ver en Vivo Ahora',
            'common.learn-more': 'Saber Más',
            'common.contact-us': 'Contáctanos',
            'common.submit': 'Enviar',
            'common.loading': 'Cargando...',
            'common.error': 'Error',
            'common.success': 'Éxito',
            
            // Footer
            'footer.description': 'Canal 99-9 cubriendo la Costa Norte de Puerto Rico. Juegos, Radio en Línea, TV en Vivo y apoyo a artistas locales.',
            'footer.copyright': '© 2023 Caribbean Advantage. Todos los derechos reservados.',
            'footer.quick-links': 'Enlaces Rápidos',
            'footer.contact': 'Contacto',
            
            // Language Switcher
            'lang.english': 'English',
            'lang.spanish': 'Español',
            'lang.french': 'Français',
            'lang.african': 'Kiswahili'
        };

        // French
        this.translations.fr = {
            // Navigation
            'nav.home': 'Accueil',
            'nav.online-tv': 'TV en Ligne',
            'nav.gamezone': 'Zone de Jeu',
            'nav.radio': 'Radio en Ligne',
            'nav.events': 'Événements',
            'nav.contact': 'Contact',
            'nav.watch-live': '📺 Regarder en Direct',
            'nav.channel': 'Chaîne 99-9',
            'nav.location': 'Côte Nord PR',
            
            // Homepage
            'home.title': 'Caribbean Advantage - Chaîne 99-9 | Jeux, Radio et TV en Direct',
            'home.description': 'Caribbean Advantage TV sur la Chaîne 99-9 couvrant la Côte Nord de Porto Rico. Jeux, Radio en Ligne, diffusion TV en direct et plateforme d\'artistes locaux.',
            'home.hero.title': 'Caribbean Advantage',
            'home.hero.subtitle': 'Chaîne 99-9',
            'home.hero.description': 'Votre destination principale pour les Jeux, la Radio en Ligne et la TV en Direct couvrant la Côte Nord de Porto Rico. Soutenant les artistes locaux et vous apportant le meilleur divertissement caribéen 24h/24 et 7j/7.',
            'home.hero.watch-now': '🎬 Regarder Maintenant',
            'home.hero.online-radio': '📻 Radio en Ligne',
            'home.content.title': 'Notre Contenu',
            'home.content.description': 'Découvrez les jeux, la radio en ligne, la TV en direct et le soutien aux artistes portoricains locaux sur la Chaîne 99-9.',
            'home.gamezone.title': 'Zone de Jeu',
            'home.gamezone.description': 'Diffusions de jeux en direct, tournois et contenu de jeu depuis Porto Rico.',
            'home.radio.title': 'Radio en Ligne',
            'home.radio.description': 'Musique caribéenne 24h/24, artistes locaux et émissions de radio en direct.',
            'home.tv.title': 'Chaîne 99-9',
            'home.tv.description': 'Diffusion TV en direct couvrant la Côte Nord de Porto Rico.',
            'home.artists.title': 'Artistes Locaux',
            'home.artists.description': 'Soutenir et mettre en valeur les artistes portoricains talentueux.',
            
            // Common
            'common.live': 'EN DIRECT',
            'common.watch-live': 'Regarder en Direct Maintenant',
            'common.learn-more': 'En Savoir Plus',
            'common.contact-us': 'Nous Contacter',
            'common.submit': 'Soumettre',
            'common.loading': 'Chargement...',
            'common.error': 'Erreur',
            'common.success': 'Succès',
            
            // Footer
            'footer.description': 'Chaîne 99-9 couvrant la Côte Nord de Porto Rico. Jeux, Radio en Ligne, TV en Direct et soutien aux artistes locaux.',
            'footer.copyright': '© 2023 Caribbean Advantage. Tous droits réservés.',
            'footer.quick-links': 'Liens Rapides',
            'footer.contact': 'Contact',
            
            // Language Switcher
            'lang.english': 'English',
            'lang.spanish': 'Español',
            'lang.french': 'Français',
            'lang.african': 'Kiswahili'
        };

        // Swahili (African)
        this.translations.sw = {
            // Navigation
            'nav.home': 'Nyumbani',
            'nav.online-tv': 'TV ya Mtandaoni',
            'nav.gamezone': 'Eneo la Michezo',
            'nav.radio': 'Redio ya Mtandaoni',
            'nav.events': 'Matukio',
            'nav.contact': 'Mawasiliano',
            'nav.watch-live': '📺 Tazama Moja kwa Moja',
            'nav.channel': 'Kituo 99-9',
            'nav.location': 'Pwani ya Kaskazini PR',
            
            // Homepage
            'home.title': 'Caribbean Advantage - Kituo 99-9 | Michezo, Redio na TV ya Moja kwa Moja',
            'home.description': 'Caribbean Advantage TV kwenye Kituo 99-9 kinachofunika Pwani ya Kaskazini ya Puerto Rico. Michezo, Redio ya Mtandaoni, utangazaji wa TV wa moja kwa moja na jukwaa la wasanii wa ndani.',
            'home.hero.title': 'Caribbean Advantage',
            'home.hero.subtitle': 'Kituo 99-9',
            'home.hero.description': 'Makao yako makuu ya Michezo, Redio ya Mtandaoni na TV ya Moja kwa Moja yanayofunika Pwani ya Kaskazini ya Puerto Rico. Kuunga mkono wasanii wa ndani na kukuletea burudani bora ya Caribbean masaa 24/7.',
            'home.hero.watch-now': '🎬 Tazama Sasa',
            'home.hero.online-radio': '📻 Redio ya Mtandaoni',
            'home.content.title': 'Maudhui Yetu',
            'home.content.description': 'Furahia michezo, redio ya mtandaoni, TV ya moja kwa moja na msaada kwa wasanii wa Puerto Rico wa ndani kwenye Kituo 99-9.',
            'home.gamezone.title': 'Eneo la Michezo',
            'home.gamezone.description': 'Mtiririko wa michezo wa moja kwa moja, mashindano na maudhui ya michezo kutoka Puerto Rico.',
            'home.radio.title': 'Redio ya Mtandaoni',
            'home.radio.description': 'Muziki wa Caribbean masaa 24/7, wasanii wa ndani na vipindi vya redio vya moja kwa moja.',
            'home.tv.title': 'Kituo 99-9',
            'home.tv.description': 'Utangazaji wa TV wa moja kwa moja unaofunika Pwani ya Kaskazini ya Puerto Rico.',
            'home.artists.title': 'Wasanii wa Ndani',
            'home.artists.description': 'Kuunga mkono na kuonyesha wasanii wenye kipaji wa Puerto Rico.',
            
            // Common
            'common.live': 'MOJA KWA MOJA',
            'common.watch-live': 'Tazama Moja kwa Moja Sasa',
            'common.learn-more': 'Jifunze Zaidi',
            'common.contact-us': 'Wasiliana Nasi',
            'common.submit': 'Wasilisha',
            'common.loading': 'Inapakia...',
            'common.error': 'Hitilafu',
            'common.success': 'Mafanikio',
            
            // Footer
            'footer.description': 'Kituo 99-9 kinachofunika Pwani ya Kaskazini ya Puerto Rico. Michezo, Redio ya Mtandaoni, TV ya Moja kwa Moja na kuunga mkono wasanii wa ndani.',
            'footer.copyright': '© 2023 Caribbean Advantage. Haki zote zimehifadhiwa.',
            'footer.quick-links': 'Viungo vya Haraka',
            'footer.contact': 'Mawasiliano',
            
            // Language Switcher
            'lang.english': 'English',
            'lang.spanish': 'Español',
            'lang.french': 'Français',
            'lang.african': 'Kiswahili'
        };
    }

    detectLanguage() {
        // Try to get saved language preference
        const savedLang = localStorage.getItem('ca-tv-language');
        if (savedLang && this.supportedLanguages.includes(savedLang)) {
            this.currentLanguage = savedLang;
            return;
        }

        // Try to detect from browser language
        const browserLang = navigator.language || navigator.userLanguage;
        const langCode = browserLang.split('-')[0].toLowerCase();
        
        // Map browser languages to our supported languages
        const langMap = {
            'en': 'en',
            'es': 'es',
            'fr': 'fr',
            'sw': 'sw',
            'ar': 'sw', // Arabic -> Swahili (African representative)
            'am': 'sw', // Amharic -> Swahili
            'ha': 'sw', // Hausa -> Swahili
            'yo': 'sw', // Yoruba -> Swahili
            'ig': 'sw', // Igbo -> Swahili
            'zu': 'sw', // Zulu -> Swahili
            'af': 'sw'  // Afrikaans -> Swahili
        };

        if (langMap[langCode]) {
            this.currentLanguage = langMap[langCode];
        }

        // Try to detect location-based language (simplified)
        this.detectLocationBasedLanguage();
    }

    async detectLocationBasedLanguage() {
        try {
            // Try to get user's location for language detection
            if ('geolocation' in navigator) {
                navigator.geolocation.getCurrentPosition(
                    (position) => {
                        const { latitude, longitude } = position.coords;
                        
                        // Simple location-based language detection
                        // Caribbean/Latin America -> Spanish
                        if (latitude >= 10 && latitude <= 25 && longitude >= -85 && longitude <= -60) {
                            this.currentLanguage = 'es';
                        }
                        // France/French territories -> French
                        else if (latitude >= 41 && latitude <= 51 && longitude >= -5 && longitude <= 10) {
                            this.currentLanguage = 'fr';
                        }
                        // Africa -> Swahili
                        else if (latitude >= -35 && latitude <= 37 && longitude >= -20 && longitude <= 55) {
                            this.currentLanguage = 'sw';
                        }
                        
                        this.applyLanguage(this.currentLanguage);
                    },
                    (error) => {
                        console.log('Location detection failed, using browser language');
                    }
                );
            }
        } catch (error) {
            console.log('Geolocation not available');
        }
    }

    setupLanguageSwitcher() {
        // Create language switcher HTML
        const languageSwitcher = `
            <div class="language-switcher relative">
                <button id="language-btn" class="flex items-center space-x-2 text-white hover:text-ca-yellow transition-colors">
                    <span class="text-lg">🌍</span>
                    <span id="current-lang">${this.getLanguageName(this.currentLanguage)}</span>
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div id="language-dropdown" class="absolute right-0 top-full mt-2 bg-white rounded-lg shadow-lg border border-gray-200 min-w-[150px] z-50 hidden">
                    <div class="py-2">
                        <button class="lang-option w-full text-left px-4 py-2 hover:bg-gray-100 flex items-center space-x-2" data-lang="en">
                            <span>🇺🇸</span>
                            <span data-i18n="lang.english">English</span>
                        </button>
                        <button class="lang-option w-full text-left px-4 py-2 hover:bg-gray-100 flex items-center space-x-2" data-lang="es">
                            <span>🇪🇸</span>
                            <span data-i18n="lang.spanish">Español</span>
                        </button>
                        <button class="lang-option w-full text-left px-4 py-2 hover:bg-gray-100 flex items-center space-x-2" data-lang="fr">
                            <span>🇫🇷</span>
                            <span data-i18n="lang.french">Français</span>
                        </button>
                        <button class="lang-option w-full text-left px-4 py-2 hover:bg-gray-100 flex items-center space-x-2" data-lang="sw">
                            <span>🌍</span>
                            <span data-i18n="lang.african">Kiswahili</span>
                        </button>
                    </div>
                </div>
            </div>
        `;

        // Insert language switcher next to "Watch Live" button
        const watchLiveBtn = document.querySelector('.hidden.md\\:flex.items-center.space-x-4');
        if (watchLiveBtn) {
            watchLiveBtn.insertAdjacentHTML('beforeend', languageSwitcher);
        }

        // Setup event listeners
        this.setupLanguageSwitcherEvents();
    }

    setupLanguageSwitcherEvents() {
        const languageBtn = document.getElementById('language-btn');
        const languageDropdown = document.getElementById('language-dropdown');
        const langOptions = document.querySelectorAll('.lang-option');

        if (languageBtn && languageDropdown) {
            // Toggle dropdown
            languageBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                languageDropdown.classList.toggle('hidden');
            });

            // Close dropdown when clicking outside
            document.addEventListener('click', () => {
                languageDropdown.classList.add('hidden');
            });

            // Language selection
            langOptions.forEach(option => {
                option.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const selectedLang = option.getAttribute('data-lang');
                    this.changeLanguage(selectedLang);
                    languageDropdown.classList.add('hidden');
                });
            });
        }
    }

    changeLanguage(langCode) {
        if (this.supportedLanguages.includes(langCode)) {
            this.currentLanguage = langCode;
            localStorage.setItem('ca-tv-language', langCode);
            this.applyLanguage(langCode);
            
            // Update current language display
            const currentLangElement = document.getElementById('current-lang');
            if (currentLangElement) {
                currentLangElement.textContent = this.getLanguageName(langCode);
            }
            
            // Update page title and meta description
            this.updatePageMeta();
            
            console.log(`🌍 Language changed to: ${this.getLanguageName(langCode)}`);
        }
    }

    applyLanguage(langCode) {
        const translations = this.translations[langCode] || this.translations.en;
        
        // Apply translations to all elements with data-i18n attribute
        document.querySelectorAll('[data-i18n]').forEach(element => {
            const key = element.getAttribute('data-i18n');
            if (translations[key]) {
                if (element.tagName === 'INPUT' && element.type === 'submit') {
                    element.value = translations[key];
                } else if (element.hasAttribute('placeholder')) {
                    element.placeholder = translations[key];
                } else {
                    element.textContent = translations[key];
                }
            }
        });

        // Update HTML lang attribute
        document.documentElement.lang = langCode;
    }

    updatePageMeta() {
        const translations = this.translations[this.currentLanguage] || this.translations.en;
        
        // Update title
        if (translations['home.title']) {
            document.title = translations['home.title'];
        }
        
        // Update meta description
        const metaDescription = document.querySelector('meta[name="description"]');
        if (metaDescription && translations['home.description']) {
            metaDescription.content = translations['home.description'];
        }
    }

    getLanguageName(langCode) {
        const names = {
            'en': 'English',
            'es': 'Español',
            'fr': 'Français',
            'sw': 'Kiswahili'
        };
        return names[langCode] || 'English';
    }

    // Public method to get translation
    t(key) {
        const translations = this.translations[this.currentLanguage] || this.translations.en;
        return translations[key] || key;
    }
}

// Initialize i18n system
window.CaribbeanAdvantageI18n = new CaribbeanAdvantageI18n();

// Export for use in other scripts
window.i18n = window.CaribbeanAdvantageI18n;
