'use client'

import { motion, AnimatePresence } from 'framer-motion'
import { useState, useEffect } from 'react'
import { Play, Users, X, Volume2 } from 'lucide-react'
import { useLanguage } from './LanguageProvider'

export function LiveStatusBar() {
  const [isVisible, setIsVisible] = useState(false)
  const [viewerCount, setViewerCount] = useState(1247)
  const { t } = useLanguage()

  useEffect(() => {
    // Show the bar after 3 seconds
    const timer = setTimeout(() => {
      setIsVisible(true)
    }, 3000)

    // Simulate viewer count changes
    const viewerTimer = setInterval(() => {
      setViewerCount(prev => prev + Math.floor(Math.random() * 10) - 5)
    }, 5000)

    return () => {
      clearTimeout(timer)
      clearInterval(viewerTimer)
    }
  }, [])

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ y: 100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: 100, opacity: 0 }}
          transition={{ duration: 0.5, ease: "easeOut" }}
          className="fixed bottom-6 left-6 right-6 z-40"
        >
          <div className="max-w-4xl mx-auto">
            <motion.div
              className="glass-effect rounded-2xl p-4 shadow-2xl border border-white/20"
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  {/* Live Indicator */}
                  <motion.div
                    className="flex items-center space-x-2"
                    animate={{ scale: [1, 1.05, 1] }}
                    transition={{ duration: 2, repeat: Infinity }}
                  >
                    <div className="relative">
                      <div className="w-3 h-3 bg-red-500 rounded-full" />
                      <motion.div
                        className="absolute inset-0 bg-red-500 rounded-full"
                        animate={{ scale: [1, 1.5, 1], opacity: [1, 0, 1] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      />
                    </div>
                    <span className="text-white font-semibold text-sm">
                      {t('live.status')}
                    </span>
                  </motion.div>

                  {/* Channel Info */}
                  <div className="hidden md:block">
                    <h4 className="text-white font-semibold text-sm">
                      {t('live.channel')}
                    </h4>
                    <p className="text-blue-200 text-xs">
                      {t('live.location')}
                    </p>
                  </div>

                  {/* Viewer Count */}
                  <motion.div
                    className="flex items-center space-x-2 glass-effect px-3 py-1 rounded-lg"
                    key={viewerCount}
                    initial={{ scale: 1.1 }}
                    animate={{ scale: 1 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Users className="w-4 h-4 text-blue-400" />
                    <span className="text-white text-sm font-medium">
                      {viewerCount.toLocaleString()} {t('live.viewers')}
                    </span>
                  </motion.div>
                </div>

                {/* Controls */}
                <div className="flex items-center space-x-3">
                  {/* Play Button */}
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    className="bg-red-600 hover:bg-red-700 text-white p-2 rounded-lg transition-colors"
                  >
                    <Play className="w-4 h-4" />
                  </motion.button>

                  {/* Volume Button */}
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    className="glass-effect text-white p-2 rounded-lg hover:bg-white/20 transition-colors"
                  >
                    <Volume2 className="w-4 h-4" />
                  </motion.button>

                  {/* Close Button */}
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={() => setIsVisible(false)}
                    className="glass-effect text-white p-2 rounded-lg hover:bg-white/20 transition-colors"
                  >
                    <X className="w-4 h-4" />
                  </motion.button>
                </div>
              </div>

              {/* Progress Bar */}
              <motion.div
                className="mt-3 h-1 bg-white/20 rounded-full overflow-hidden"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.5 }}
              >
                <motion.div
                  className="h-full bg-gradient-to-r from-red-500 to-pink-500 rounded-full"
                  initial={{ width: "0%" }}
                  animate={{ width: "100%" }}
                  transition={{ duration: 30, repeat: Infinity, ease: "linear" }}
                />
              </motion.div>
            </motion.div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}
