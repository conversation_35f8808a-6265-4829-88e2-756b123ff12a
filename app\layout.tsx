import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { LanguageProvider } from './components/LanguageProvider'
import { MouseFollower } from './components/MouseFollower'
import { BackgroundAnimation } from './components/BackgroundAnimation'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'Caribbean Advantage TV - Channel 99-9 | Gaming, Radio & Live TV',
  description: 'Your premier destination for Gaming, Online Radio, and Live TV covering North Coast Puerto Rico. Supporting local artists and bringing you the best Caribbean entertainment 24/7.',
  keywords: 'caribbean, tv, radio, gaming, entertainment, puerto rico, channel 99-9',
  authors: [{ name: 'Caribbean Advantage' }],
  viewport: 'width=device-width, initial-scale=1',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="scroll-smooth">
      <body className={`${inter.className} overflow-x-hidden`}>
        <LanguageProvider>
          <BackgroundAnimation />
          <MouseFollower />
          <div className="relative z-10">
            {children}
          </div>
        </LanguageProvider>
      </body>
    </html>
  )
}
