<!-- Standard Header Component for Caribbean Advantage Channel 99-9 -->
<header class="fixed top-0 left-0 right-0 z-50 glassmorphism shadow-lg border-b border-white/20">
    <div class="container mx-auto px-4 py-3">
        <div class="flex justify-between items-center">
            <!-- Logo and Brand -->
            <div class="flex items-center">
                <div class="flex items-center space-x-3">
                    <div class="w-12 h-12 bg-gradient-to-r from-primary-blue to-primary-blue-light rounded-lg flex items-center justify-center shadow-lg">
                        <span class="text-white font-bold text-xl">CA</span>
                    </div>
                    <div class="text-2xl font-bold">
                        <span class="text-primary-blue">Caribbean</span> <span class="text-primary-blue-light">Advantage</span>
                    </div>
                </div>
            </div>

            <!-- Desktop Navigation -->
            <nav class="hidden md:flex space-x-8">
                <a href="/" class="nav-link text-white hover:text-primary-blue-ultra-light transition-colors font-medium" data-i18n="nav.home">Home</a>
                <a href="/live" class="nav-link text-white hover:text-primary-blue-ultra-light transition-colors font-medium" data-i18n="nav.online-tv">Online TV</a>
                <a href="/gamezone" class="nav-link text-white hover:text-primary-blue-ultra-light transition-colors font-medium" data-i18n="nav.gamezone">GameZone</a>
                <a href="/radio" class="nav-link text-white hover:text-primary-blue-ultra-light transition-colors font-medium" data-i18n="nav.radio">Online Radio</a>
            </nav>

            <!-- Right Side Actions -->
            <div class="hidden md:flex items-center space-x-4">
                <!-- Language Switcher -->
                <div class="relative">
                    <button id="language-toggle" class="flex items-center space-x-2 text-white/90 hover:text-white transition-colors">
                        <span class="text-lg">🌍</span>
                        <span id="current-language" class="text-sm font-medium">English</span>
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    <div id="language-dropdown" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 hidden z-50">
                        <a href="#" onclick="changeLanguage('en')" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-t-lg">
                            <span class="mr-3">🇺🇸</span> English
                        </a>
                        <a href="#" onclick="changeLanguage('es')" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            <span class="mr-3">🇪🇸</span> Español
                        </a>
                        <a href="#" onclick="changeLanguage('fr')" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            <span class="mr-3">🇫🇷</span> Français
                        </a>
                        <a href="#" onclick="changeLanguage('sw')" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-b-lg">
                            <span class="mr-3">🌍</span> Kiswahili
                        </a>
                    </div>
                </div>
                
                <!-- Watch Live Button -->
                <a href="/live" class="btn-primary text-white px-6 py-2 rounded-lg font-medium" data-i18n="nav.watch-live">
                    📺 Watch Live
                </a>
            </div>

            <!-- Mobile Menu Button -->
            <div class="md:hidden">
                <button id="menu-toggle" class="text-white focus:outline-none">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div id="mobile-menu" class="md:hidden hidden mt-4 pb-4">
            <div class="flex flex-col space-y-3">
                <a href="/" class="text-white hover:text-primary-blue-ultra-light transition-colors font-medium" data-i18n="nav.home">Home</a>
                <a href="/live" class="text-white hover:text-primary-blue-ultra-light transition-colors font-medium" data-i18n="nav.online-tv">Online TV</a>
                <a href="/gamezone" class="text-white hover:text-primary-blue-ultra-light transition-colors font-medium" data-i18n="nav.gamezone">GameZone</a>
                <a href="/radio" class="text-white hover:text-primary-blue-ultra-light transition-colors font-medium" data-i18n="nav.radio">Online Radio</a>
                <div class="border-t border-white/20 pt-3 mt-3">
                    <a href="/events" class="text-white/80 hover:text-white transition-colors font-medium block mb-2" data-i18n="nav.events">Events</a>
                    <a href="/contact" class="text-white/80 hover:text-white transition-colors font-medium block mb-3" data-i18n="nav.contact">Contact</a>
                    <a href="/live" class="btn-primary text-white px-6 py-2 rounded-lg font-medium block text-center" data-i18n="nav.watch-live">📺 Watch Live</a>
                </div>
            </div>
        </div>
    </div>
</header>
