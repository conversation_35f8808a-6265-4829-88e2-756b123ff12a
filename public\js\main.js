// Caribbean Advantage TV - Main JavaScript

// Page Loading Animation
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Caribbean Advantage Channel 99-9 - Loading...');

    const loader = document.getElementById('page-loader');

    // Hide loader after 2 seconds
    setTimeout(() => {
        if (loader) {
            loader.style.opacity = '0';
            setTimeout(() => {
                loader.style.display = 'none';
            }, 500);
        }
    }, 2000);

    // Initialize all components
    initializeWebsite();
});

// Initialize all website functionality
function initializeWebsite() {
    console.log('🎯 Initializing Caribbean Advantage features...');

    // Wait for i18n system to be ready
    if (window.CaribbeanAdvantageI18n) {
        console.log('🌍 Multi-language system detected');
    }

    // Initialize all components
    createParticles();
    initMobileMenu();
    initScrollAnimations();
    initSmoothScrolling();
    initButtonInteractions();
    initParallaxEffect();
    initFormHandling();
    initFormAnimations();
    initCardHoverEffects();
    initNavigationEffects();
    initLanguageFeatures();

    console.log('✅ Caribbean Advantage Channel 99-9 - Ready!');
}

// Particle Animation
function createParticles() {
    const particlesContainer = document.getElementById('particles');
    if (!particlesContainer) return;

    const particleCount = 50;

    for (let i = 0; i < particleCount; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.width = Math.random() * 4 + 2 + 'px';
        particle.style.height = particle.style.width;
        particle.style.animationDelay = Math.random() * 15 + 's';
        particle.style.animationDuration = (Math.random() * 10 + 10) + 's';
        particlesContainer.appendChild(particle);
    }
}

// Mobile menu toggle
function initMobileMenu() {
    const menuToggle = document.getElementById('menu-toggle');
    const closeMenu = document.getElementById('close-menu');
    const mobileMenu = document.getElementById('mobile-menu');

    if (menuToggle && mobileMenu) {
        menuToggle.addEventListener('click', () => {
            mobileMenu.classList.add('active');
        });
    }

    if (closeMenu && mobileMenu) {
        closeMenu.addEventListener('click', () => {
            mobileMenu.classList.remove('active');
        });
    }
}

// Counter Animation
function animateCounters() {
    const counters = document.querySelectorAll('.counter');

    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('data-target'));
        const increment = target / 100;
        let current = 0;

        const updateCounter = () => {
            if (current < target) {
                current += increment;
                if (target >= 1000000) {
                    counter.textContent = (Math.ceil(current) / 1000000).toFixed(1) + 'M+';
                } else {
                    counter.textContent = Math.ceil(current) + '+';
                }
                setTimeout(updateCounter, 20);
            } else {
                if (target >= 1000000) {
                    counter.textContent = (target / 1000000).toFixed(1) + 'M+';
                } else {
                    counter.textContent = target + '+';
                }
            }
        };

        updateCounter();
    });
}

// Enhanced Scroll animations
function initScrollAnimations() {
    const scrollElements = document.querySelectorAll('.scroll-animation');

    const elementInView = (el, dividend = 1) => {
        const elementTop = el.getBoundingClientRect().top;
        return (
            elementTop <= (window.innerHeight || document.documentElement.clientHeight) / dividend
        );
    };

    const displayScrollElement = (element) => {
        element.classList.add('active');

        // Trigger counter animation if element has counters
        const counters = element.querySelectorAll('.counter');
        if (counters.length > 0) {
            animateCounters();
        }
    };

    const handleScrollAnimation = () => {
        scrollElements.forEach((el) => {
            if (elementInView(el, 1.25)) {
                displayScrollElement(el);
            }
        });
    };

    window.addEventListener('scroll', () => {
        handleScrollAnimation();
    });

    // Initialize on page load
    handleScrollAnimation();
}

// Smooth scrolling for anchor links
function initSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();

            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);

            if (targetElement) {
                window.scrollTo({
                    top: targetElement.offsetTop - 80,
                    behavior: 'smooth'
                });

                // Close mobile menu if open
                const mobileMenu = document.getElementById('mobile-menu');
                if (mobileMenu && mobileMenu.classList.contains('active')) {
                    mobileMenu.classList.remove('active');
                }
            }
        });
    });
}



// Parallax Effect for Hero Section
function initParallaxEffect() {
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const parallax = document.querySelector('.hero-bg');
        const speed = scrolled * 0.5;

        if (parallax) {
            parallax.style.transform = `translateY(${speed}px)`;
        }
    });
}



// Form input animations
function initFormAnimations() {
    document.querySelectorAll('.form-input').forEach(input => {
        input.addEventListener('focus', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 10px 25px rgba(0, 61, 107, 0.1)';
        });

        input.addEventListener('blur', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = 'none';
        });
    });
}

// Card hover effects
function initCardHoverEffects() {
    document.querySelectorAll('.card-hover').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-15px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
}

// Schedule item hover effects
function initScheduleItemEffects() {
    document.querySelectorAll('.schedule-item').forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.03) translateX(5px)';
        });

        item.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1) translateX(0)';
        });
    });
}

// News card hover effects
function initNewsCardEffects() {
    document.querySelectorAll('.news-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
}

// Navigation effects
function initNavigationEffects() {
    // Add active page highlighting
    const currentPage = window.location.pathname;
    const navLinks = document.querySelectorAll('nav a');

    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href === currentPage || (currentPage === '/' && href === '/')) {
            link.classList.add('text-ca-yellow', 'font-bold');
            link.classList.remove('text-white');
        }
    });

    // Enhanced navigation hover effects
    navLinks.forEach(link => {
        link.addEventListener('mouseenter', function() {
            if (!this.classList.contains('text-ca-yellow')) {
                this.style.transform = 'translateY(-2px)';
                this.style.textShadow = '0 4px 8px rgba(255, 193, 7, 0.3)';
            }
        });

        link.addEventListener('mouseleave', function() {
            if (!this.classList.contains('text-ca-yellow')) {
                this.style.transform = 'translateY(0)';
                this.style.textShadow = 'none';
            }
        });
    });
}

// Enhanced button interactions with modern effects
function initButtonInteractions() {
    document.querySelectorAll('.btn-primary, .card-hover, .program-tag').forEach(element => {
        element.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px) scale(1.05)';
            this.style.boxShadow = '0 15px 35px rgba(0, 61, 107, 0.4)';
        });

        element.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = '';
        });

        // Add click effect
        element.addEventListener('click', function() {
            this.style.transform = 'translateY(-1px) scale(0.98)';
            setTimeout(() => {
                this.style.transform = 'translateY(-3px) scale(1.05)';
            }, 150);
        });
    });
}

// Modern form handling with better UX
function initFormHandling() {
    const forms = document.querySelectorAll('form');

    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            const button = this.querySelector('button[type="submit"]');
            if (button) {
                const originalText = button.innerHTML;

                // Show loading state
                button.innerHTML = '⏳ Sending...';
                button.disabled = true;
                button.style.opacity = '0.7';

                // Simulate form submission
                setTimeout(() => {
                    button.innerHTML = '✅ Message Sent!';
                    button.style.background = 'linear-gradient(135deg, #4caf50, #45a049)';

                    // Show success animation
                    this.style.transform = 'scale(1.02)';
                    this.style.boxShadow = '0 10px 30px rgba(76, 175, 80, 0.3)';

                    // Reset after 3 seconds
                    setTimeout(() => {
                        button.innerHTML = originalText;
                        button.disabled = false;
                        button.style.opacity = '1';
                        button.style.background = '';
                        this.style.transform = 'scale(1)';
                        this.style.boxShadow = '';
                        this.reset();
                    }, 3000);
                }, 1500);
            }
        });
    });
}

// Language features integration
function initLanguageFeatures() {
    // Add language-specific enhancements
    if (window.i18n) {
        // Listen for language changes
        document.addEventListener('languageChanged', function(e) {
            console.log(`🌍 Language changed to: ${e.detail.language}`);

            // Update any dynamic content that needs language-specific formatting
            updateDynamicContent(e.detail.language);

            // Update page direction for RTL languages (future enhancement)
            updatePageDirection(e.detail.language);
        });

        // Add keyboard shortcut for language switcher
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + L to open language switcher
            if ((e.ctrlKey || e.metaKey) && e.key === 'l') {
                e.preventDefault();
                const languageBtn = document.getElementById('language-btn');
                if (languageBtn) {
                    languageBtn.click();
                }
            }
        });

        console.log('🌍 Language features initialized');
    }
}

// Update dynamic content based on language
function updateDynamicContent(language) {
    // Update any time/date displays
    const timeElements = document.querySelectorAll('[data-time]');
    timeElements.forEach(element => {
        const timestamp = element.getAttribute('data-time');
        if (timestamp) {
            const date = new Date(timestamp);
            const options = {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            };

            let locale = 'en-US';
            switch(language) {
                case 'es': locale = 'es-ES'; break;
                case 'fr': locale = 'fr-FR'; break;
                case 'sw': locale = 'sw-KE'; break;
            }

            element.textContent = date.toLocaleDateString(locale, options);
        }
    });

    // Update any number formatting
    const numberElements = document.querySelectorAll('[data-number]');
    numberElements.forEach(element => {
        const number = parseFloat(element.getAttribute('data-number'));
        if (!isNaN(number)) {
            let locale = 'en-US';
            switch(language) {
                case 'es': locale = 'es-ES'; break;
                case 'fr': locale = 'fr-FR'; break;
                case 'sw': locale = 'sw-KE'; break;
            }

            element.textContent = number.toLocaleString(locale);
        }
    });
}

// Update page direction for RTL languages (future enhancement)
function updatePageDirection(language) {
    // Currently all supported languages are LTR
    // This function is prepared for future RTL language support
    const isRTL = false; // No RTL languages currently supported

    if (isRTL) {
        document.documentElement.dir = 'rtl';
        document.body.classList.add('rtl');
    } else {
        document.documentElement.dir = 'ltr';
        document.body.classList.remove('rtl');
    }
}

// Enhanced mobile menu with language support
function initMobileMenuWithLanguage() {
    const menuToggle = document.getElementById('menu-toggle');
    const mobileMenu = document.getElementById('mobile-menu');

    if (menuToggle && mobileMenu) {
        menuToggle.addEventListener('click', function() {
            mobileMenu.classList.toggle('hidden');

            // Update ARIA labels based on current language
            if (window.i18n) {
                const isOpen = !mobileMenu.classList.contains('hidden');
                const label = isOpen ?
                    window.i18n.t('nav.close-menu') || 'Close menu' :
                    window.i18n.t('nav.open-menu') || 'Open menu';
                menuToggle.setAttribute('aria-label', label);
            }
        });
    }
}

// Export functions for use in other scripts
window.CaribbeanAdvantageTV = {
    createParticles,
    animateCounters,
    initMobileMenu,
    initScrollAnimations,
    initSmoothScrolling,
    initButtonInteractions,
    initParallaxEffect,
    initFormHandling,
    initFormAnimations,
    initCardHoverEffects,
    initScheduleItemEffects,
    initNewsCardEffects
};
