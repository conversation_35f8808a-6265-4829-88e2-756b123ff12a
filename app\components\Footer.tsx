'use client'

import { motion } from 'framer-motion'
import Link from 'next/link'
import { Mail, Phone, MapPin, Facebook, Twitter, Instagram, Youtube } from 'lucide-react'
import { useLanguage } from './LanguageProvider'

export function Footer() {
  const { t } = useLanguage()

  const socialLinks = [
    { icon: Facebook, href: '#', color: 'hover:text-blue-500' },
    { icon: Twitter, href: '#', color: 'hover:text-sky-400' },
    { icon: Instagram, href: '#', color: 'hover:text-pink-500' },
    { icon: Youtube, href: '#', color: 'hover:text-red-500' },
  ]

  const quickLinks = [
    { href: '/', label: t('nav.home') },
    { href: '/live', label: t('nav.live') },
    { href: '/gamezone', label: t('nav.gamezone') },
    { href: '/radio', label: t('nav.radio') },
  ]

  return (
    <footer className="relative py-20 overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <motion.div
          className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500"
          initial={{ scaleX: 0 }}
          whileInView={{ scaleX: 1 }}
          transition={{ duration: 2 }}
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
      </div>

      <div className="container mx-auto px-6 relative z-10">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12 mb-12">
          {/* Brand Section */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="lg:col-span-2"
          >
            <div className="flex items-center space-x-3 mb-6">
              <motion.div
                className="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-400 rounded-xl flex items-center justify-center"
                whileHover={{ scale: 1.1, rotate: 360 }}
                transition={{ duration: 0.6 }}
              >
                <span className="text-white font-bold text-xl">CA</span>
              </motion.div>
              <div>
                <h3 className="text-2xl font-bold text-white">Caribbean Advantage</h3>
                <p className="text-blue-300 text-sm">Channel 99-9</p>
              </div>
            </div>
            
            <p className="text-blue-200 leading-relaxed mb-6 max-w-md">
              {t('footer.description')}
            </p>

            {/* Social Links */}
            <div className="flex items-center space-x-4">
              {socialLinks.map((social, index) => (
                <motion.a
                  key={index}
                  href={social.href}
                  className={`glass-effect p-3 rounded-lg text-white transition-all duration-300 ${social.color}`}
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.9 }}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <social.icon className="w-5 h-5" />
                </motion.a>
              ))}
            </div>
          </motion.div>

          {/* Quick Links */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <h4 className="text-xl font-bold text-white mb-6">{t('footer.quickLinks')}</h4>
            <ul className="space-y-3">
              {quickLinks.map((link, index) => (
                <motion.li
                  key={link.href}
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <Link
                    href={link.href}
                    className="text-blue-200 hover:text-white transition-colors duration-300 flex items-center space-x-2 group"
                  >
                    <motion.div
                      className="w-1 h-1 bg-blue-400 rounded-full group-hover:scale-150 transition-transform"
                    />
                    <span>{link.label}</span>
                  </Link>
                </motion.li>
              ))}
            </ul>
          </motion.div>

          {/* Contact Info */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <h4 className="text-xl font-bold text-white mb-6">{t('footer.contact')}</h4>
            <div className="space-y-4">
              <motion.div
                className="flex items-center space-x-3 text-blue-200"
                whileHover={{ x: 5 }}
                transition={{ duration: 0.2 }}
              >
                <MapPin className="w-5 h-5 text-blue-400" />
                <span className="text-sm">{t('live.location')}</span>
              </motion.div>
              
              <motion.div
                className="flex items-center space-x-3 text-blue-200"
                whileHover={{ x: 5 }}
                transition={{ duration: 0.2 }}
              >
                <Phone className="w-5 h-5 text-blue-400" />
                <span className="text-sm">+****************</span>
              </motion.div>
              
              <motion.div
                className="flex items-center space-x-3 text-blue-200"
                whileHover={{ x: 5 }}
                transition={{ duration: 0.2 }}
              >
                <Mail className="w-5 h-5 text-blue-400" />
                <span className="text-sm"><EMAIL></span>
              </motion.div>
            </div>

            {/* Events & Contact Links */}
            <div className="mt-8 space-y-3">
              <Link
                href="/events"
                className="block text-blue-200 hover:text-white transition-colors duration-300"
              >
                {t('nav.events')}
              </Link>
              <Link
                href="/contact"
                className="block text-blue-200 hover:text-white transition-colors duration-300"
              >
                {t('nav.contact')}
              </Link>
            </div>
          </motion.div>
        </div>

        {/* Bottom Section */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="border-t border-white/20 pt-8"
        >
          <div className="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0">
            <p className="text-blue-300 text-sm text-center md:text-left">
              {t('footer.copyright')}
            </p>
            
            <motion.div
              className="flex items-center space-x-2 text-blue-300 text-sm"
              whileHover={{ scale: 1.05 }}
            >
              <span>Made with</span>
              <motion.span
                className="text-red-500"
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 1, repeat: Infinity }}
              >
                ❤️
              </motion.span>
              <span>in Puerto Rico</span>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </footer>
  )
}
