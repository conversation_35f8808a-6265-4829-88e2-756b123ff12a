@echo off
echo ========================================
echo Caribbean Advantage TV - GitHub Upload
echo ========================================
echo.

REM Check if Git is installed
git --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Git is not installed or not in PATH
    echo Please install Git from: https://git-scm.com/downloads
    pause
    exit /b 1
)

echo ✅ Git is installed
echo.

REM Navigate to parent directory
cd /d "%~dp0.."

REM Clone the repository
echo 📥 Cloning repository...
if exist "caribbeanadvantage" (
    echo Repository folder already exists, removing...
    rmdir /s /q "caribbeanadvantage"
)

git clone https://github.com/joelgriiyo/caribbeanadvantage.git
if errorlevel 1 (
    echo ERROR: Failed to clone repository
    echo Make sure you have access to the repository
    pause
    exit /b 1
)

echo ✅ Repository cloned successfully
echo.

REM Copy files to repository
echo 📁 Copying files to repository...
cd caribbeanadvantage

REM Copy all files from Caribbean Advantage TV folder
xcopy "..\CAribbean Advantage TV\*" "." /E /Y /I /Q

echo ✅ Files copied successfully
echo.

REM Add all files to git
echo 📦 Adding files to Git...
git add .

REM Commit changes
echo 💾 Committing changes...
git commit -m "Add Caribbean Advantage TV website with Heroku deployment

- Complete responsive website with enhanced design
- Live TV player integration (http://caribbeanadvantage.com/CA-tv.html)
- Heroku deployment configuration with Express server
- All pages connected: Home, Shows, Schedule, About, Contact, News, Live
- Professional animations, loading screens, and particle effects
- Mobile-responsive design with glassmorphism effects
- SEO optimized with proper meta tags
- Error handling and 404 page
- Ready for production deployment"

if errorlevel 1 (
    echo ERROR: Failed to commit changes
    pause
    exit /b 1
)

echo ✅ Changes committed successfully
echo.

REM Push to GitHub
echo 🚀 Pushing to GitHub...
git push origin main

if errorlevel 1 (
    echo ERROR: Failed to push to GitHub
    echo You may need to authenticate or check your permissions
    pause
    exit /b 1
)

echo ✅ Successfully uploaded to GitHub!
echo.

echo ========================================
echo 🎉 UPLOAD COMPLETE!
echo ========================================
echo.
echo Your Caribbean Advantage TV website has been uploaded to:
echo https://github.com/joelgriiyo/caribbeanadvantage
echo.
echo Next steps:
echo 1. Visit your GitHub repository to verify files
echo 2. Deploy to Heroku using the deployment guide
echo 3. Test the live TV player integration
echo.
echo Repository URL: https://github.com/joelgriiyo/caribbeanadvantage
echo.

pause
