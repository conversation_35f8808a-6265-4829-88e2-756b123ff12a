'use client'

import { motion } from 'framer-motion'
import { Navigation } from '../components/Navigation'
import { Footer } from '../components/Footer'
import { FloatingElements } from '../components/FloatingElements'
import { Radio, Play, Pause, Volume2, Heart, Share2, Music, Mic } from 'lucide-react'
import { useState } from 'react'
import { useLanguage } from '../components/LanguageProvider'

export default function RadioPage() {
  const { t } = useLanguage()
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentShow, setCurrentShow] = useState('Caribbean Vibes')

  const shows = [
    {
      time: '6:00 AM',
      title: 'Morning Caribbean',
      host: 'DJ Sunrise',
      description: 'Start your day with the best Caribbean music',
      genre: 'Mixed'
    },
    {
      time: '10:00 AM',
      title: 'Salsa Sessions',
      host: '<PERSON>',
      description: 'The hottest salsa tracks from across the Caribbean',
      genre: 'Salsa'
    },
    {
      time: '2:00 PM',
      title: 'Reggaeton Power',
      host: 'DJ <PERSON><PERSON>',
      description: 'Non-stop reggaeton hits and new releases',
      genre: 'Reggaeton'
    },
    {
      time: '6:00 PM',
      title: 'Caribbean Vibes',
      host: '<PERSON>',
      description: 'Smooth Caribbean sounds for your evening',
      genre: 'Chill'
    },
    {
      time: '9:00 PM',
      title: 'Bachata Nights',
      host: '<PERSON> Santos',
      description: 'Romantic bachata for your night',
      genre: 'Bachata'
    },
    {
      time: '12:00 AM',
      title: 'Late Night Mix',
      host: 'DJ Nocturno',
      description: 'Eclectic mix for night owls',
      genre: 'Mixed'
    }
  ]

  return (
    <main className="relative min-h-screen">
      <Navigation />
      <FloatingElements />
      
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1 }}
        className="relative z-10 pt-24"
      >
        {/* Hero Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <motion.div
                className="inline-flex items-center space-x-2 glass-effect px-6 py-3 rounded-full mb-6"
                whileHover={{ scale: 1.05 }}
              >
                <Radio className="w-5 h-5 text-green-400" />
                <span className="text-sm font-semibold text-white">24/7 Caribbean Radio</span>
              </motion.div>
              
              <h1 className="text-5xl md:text-7xl font-black mb-6 gradient-text">
                {t('nav.radio')}
              </h1>
              <p className="text-xl text-blue-200 max-w-3xl mx-auto">
                Listen to the best Caribbean music, local DJs, and cultural programming from Puerto Rico
              </p>
            </motion.div>

            {/* Live Radio Player */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="max-w-4xl mx-auto mb-16"
            >
              <div className="glass-card overflow-hidden">
                {/* Now Playing Header */}
                <div className="bg-gradient-to-r from-green-600/20 to-emerald-600/20 p-6 border-b border-white/20">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <motion.div
                        className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center"
                        animate={{ rotate: isPlaying ? 360 : 0 }}
                        transition={{ duration: 4, repeat: isPlaying ? Infinity : 0, ease: "linear" }}
                      >
                        <Radio className="w-8 h-8 text-white" />
                      </motion.div>
                      <div>
                        <h3 className="text-2xl font-bold text-white">Now Playing</h3>
                        <p className="text-green-300">{currentShow}</p>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-red-500 rounded-full animate-pulse" />
                      <span className="text-white font-semibold">LIVE</span>
                    </div>
                  </div>
                </div>

                {/* Player Controls */}
                <div className="p-8">
                  <div className="text-center mb-8">
                    <h4 className="text-3xl font-bold text-white mb-2">Caribbean Vibes</h4>
                    <p className="text-blue-200 text-lg">with Carlos Mendez</p>
                    <p className="text-blue-300 text-sm mt-2">Playing: "Despacito" by Luis Fonsi</p>
                  </div>

                  {/* Waveform Visualization */}
                  <div className="flex items-center justify-center space-x-1 mb-8">
                    {[...Array(20)].map((_, i) => (
                      <motion.div
                        key={i}
                        className="w-1 bg-gradient-to-t from-green-500 to-emerald-400 rounded-full"
                        animate={{
                          height: isPlaying ? [10, 30, 15, 40, 20, 35, 25] : [10],
                        }}
                        transition={{
                          duration: 1,
                          repeat: isPlaying ? Infinity : 0,
                          delay: i * 0.1,
                        }}
                        style={{ height: '20px' }}
                      />
                    ))}
                  </div>

                  {/* Control Buttons */}
                  <div className="flex items-center justify-center space-x-6 mb-8">
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      className="glass-effect p-3 rounded-full text-white hover:bg-white/20 transition-colors"
                    >
                      <Heart className="w-6 h-6" />
                    </motion.button>

                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={() => setIsPlaying(!isPlaying)}
                      className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 p-4 rounded-full text-white transition-all duration-300"
                    >
                      {isPlaying ? (
                        <Pause className="w-8 h-8" />
                      ) : (
                        <Play className="w-8 h-8" />
                      )}
                    </motion.button>

                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      className="glass-effect p-3 rounded-full text-white hover:bg-white/20 transition-colors"
                    >
                      <Volume2 className="w-6 h-6" />
                    </motion.button>

                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      className="glass-effect p-3 rounded-full text-white hover:bg-white/20 transition-colors"
                    >
                      <Share2 className="w-6 h-6" />
                    </motion.button>
                  </div>

                  {/* Volume Slider */}
                  <div className="flex items-center space-x-4">
                    <Volume2 className="w-5 h-5 text-blue-400" />
                    <div className="flex-1 h-2 bg-white/20 rounded-full overflow-hidden">
                      <motion.div
                        className="h-full bg-gradient-to-r from-green-500 to-emerald-500 rounded-full"
                        initial={{ width: "70%" }}
                        whileHover={{ width: "75%" }}
                      />
                    </div>
                    <span className="text-white text-sm">70%</span>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Program Schedule */}
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
            >
              <h2 className="text-3xl font-bold text-white mb-8 text-center">Today's Schedule</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {shows.map((show, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 + 0.8 }}
                    className={`glass-card hover:glow-effect transition-all duration-300 cursor-pointer ${
                      show.title === currentShow ? 'ring-2 ring-green-500' : ''
                    }`}
                    whileHover={{ scale: 1.02, y: -5 }}
                    onClick={() => setCurrentShow(show.title)}
                  >
                    <div className="flex items-center justify-between mb-4">
                      <div className="text-green-400 font-bold text-lg">{show.time}</div>
                      <div className="flex items-center space-x-2">
                        {show.title === currentShow && (
                          <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />
                        )}
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          show.genre === 'Salsa' ? 'bg-red-600/30 text-red-300' :
                          show.genre === 'Reggaeton' ? 'bg-yellow-600/30 text-yellow-300' :
                          show.genre === 'Bachata' ? 'bg-pink-600/30 text-pink-300' :
                          'bg-blue-600/30 text-blue-300'
                        }`}>
                          {show.genre}
                        </span>
                      </div>
                    </div>
                    
                    <h3 className="text-xl font-bold text-white mb-2">{show.title}</h3>
                    <div className="flex items-center space-x-2 mb-3">
                      <Mic className="w-4 h-4 text-blue-400" />
                      <span className="text-blue-300 text-sm">{show.host}</span>
                    </div>
                    <p className="text-blue-200 text-sm">{show.description}</p>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </div>
        </section>

        <Footer />
      </motion.div>
    </main>
  )
}
