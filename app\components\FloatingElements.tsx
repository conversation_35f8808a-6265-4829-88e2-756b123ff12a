'use client'

import { motion } from 'framer-motion'

export function FloatingElements() {
  const elements = [
    { emoji: '🌊', size: 'text-4xl', delay: 0 },
    { emoji: '🏝️', size: 'text-3xl', delay: 2 },
    { emoji: '🎵', size: 'text-2xl', delay: 4 },
    { emoji: '📺', size: 'text-3xl', delay: 6 },
    { emoji: '🎮', size: 'text-2xl', delay: 8 },
    { emoji: '🎪', size: 'text-3xl', delay: 10 },
    { emoji: '🌴', size: 'text-4xl', delay: 12 },
    { emoji: '🎭', size: 'text-2xl', delay: 14 },
  ]

  return (
    <div className="fixed inset-0 pointer-events-none z-0 overflow-hidden">
      {elements.map((element, index) => (
        <motion.div
          key={index}
          className={`absolute ${element.size} opacity-20 select-none`}
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
          }}
          animate={{
            y: [0, -30, 0],
            x: [0, 15, -15, 0],
            rotate: [0, 10, -10, 0],
            scale: [1, 1.1, 0.9, 1],
          }}
          transition={{
            duration: 8 + Math.random() * 4,
            repeat: Infinity,
            ease: "easeInOut",
            delay: element.delay,
          }}
        >
          {element.emoji}
        </motion.div>
      ))}
    </div>
  )
}
