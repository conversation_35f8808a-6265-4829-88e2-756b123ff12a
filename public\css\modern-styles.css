/* Caribbean Advantage TV - Modern Vivid Styles */

:root {
    --primary-blue: #1E90FF;
    --primary-blue-dark: #1873CC;
    --primary-blue-light: #4FA8FF;
    --primary-blue-ultra-light: #87CEEB;
    --light-grey: #F8F9FA;
    --medium-grey: #E9ECEF;
    --dark-grey: #6C757D;
    --white: #FFFFFF;
    --black: #212529;
    --gradient-primary: linear-gradient(135deg, #1E90FF 0%, #87CEEB 100%);
    --gradient-secondary: linear-gradient(135deg, #F8F9FA 0%, #E9ECEF 100%);
    --gradient-dark: linear-gradient(135deg, #1873CC 0%, #1E90FF 100%);
    --gradient-vivid: linear-gradient(135deg, #1E90FF 0%, #4FA8FF 50%, #87CEEB 100%);
    --shadow-light: 0 4px 20px rgba(30, 144, 255, 0.15);
    --shadow-medium: 0 8px 30px rgba(30, 144, 255, 0.25);
    --shadow-heavy: 0 15px 50px rgba(30, 144, 255, 0.35);
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: var(--light-grey);
    color: var(--black);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Loading Animation */
.page-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.5s ease-out;
}

.loader-content {
    text-align: center;
    color: var(--white);
}

.loader-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.2);
    border-left: 4px solid var(--white);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Modern Gradient Text */
.gradient-text {
    background: var(--gradient-vivid);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    background-size: 200% 200%;
    animation: gradient-shift 4s ease infinite;
    font-weight: 700;
}

@keyframes gradient-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* Modern Card Hover Effects */
.card-hover {
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    background: var(--white);
    border-radius: 20px;
    border: 1px solid var(--medium-grey);
}

.card-hover::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(30, 144, 255, 0.1), transparent);
    transition: left 0.6s ease;
}

.card-hover:hover::before {
    left: 100%;
}

.card-hover:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: var(--shadow-heavy);
    border-color: var(--primary-blue-light);
}

/* Modern Glassmorphism */
.glassmorphism {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: var(--shadow-medium);
    border-radius: 20px;
}

/* Modern Navigation */
.nav-link {
    position: relative;
    overflow: hidden;
    padding: 8px 16px;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(30, 144, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.nav-link:hover::before {
    left: 100%;
}

.nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 3px;
    bottom: 0;
    left: 50%;
    background: var(--gradient-primary);
    transition: all 0.3s ease;
    transform: translateX(-50%);
    border-radius: 2px;
}

.nav-link:hover::after {
    width: 100%;
}

.nav-link:hover {
    background: rgba(30, 144, 255, 0.1);
    color: var(--primary-blue-dark);
}

/* Modern Hero Background */
.hero-bg {
    background: 
        var(--gradient-primary),
        radial-gradient(circle at 20% 80%, rgba(30, 144, 255, 0.2) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(135, 206, 235, 0.2) 0%, transparent 50%);
    background-size: cover, 400px 400px, 300px 300px;
    background-position: center, 0% 0%, 100% 100%;
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    animation: shimmer 8s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { transform: translateX(-100%); }
    50% { transform: translateX(100%); }
}

/* Modern Button Styles */
.btn-primary {
    background: var(--gradient-primary);
    border: none;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    border-radius: 12px;
    padding: 12px 24px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    box-shadow: var(--shadow-light);
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-heavy);
    background: var(--gradient-dark);
}

/* Modern Program Tags */
.program-tag {
    background: var(--gradient-primary);
    border-radius: 20px;
    padding: 6px 16px;
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--white);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-light);
    display: inline-block;
}

.program-tag::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.program-tag:hover::before {
    left: 100%;
}

/* Modern Scroll Animations */
.scroll-animation {
    opacity: 0;
    transform: translateY(40px);
    transition: opacity 1s cubic-bezier(0.175, 0.885, 0.32, 1.275), transform 1s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.scroll-animation.active {
    opacity: 1;
    transform: translateY(0);
}

.scroll-animation:nth-child(even) {
    transform: translateY(40px) translateX(-30px);
}

.scroll-animation:nth-child(even).active {
    transform: translateY(0) translateX(0);
}

/* Modern Float Animation */
@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    25% { transform: translateY(-20px) rotate(2deg); }
    50% { transform: translateY(-15px) rotate(0deg); }
    75% { transform: translateY(-25px) rotate(-2deg); }
}

.float-animation {
    animation: float 8s ease-in-out infinite;
}

/* Modern Mobile Menu */
.mobile-menu {
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    transform: translateX(-100%);
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.95);
    border-radius: 0 20px 20px 0;
}

.mobile-menu.active {
    transform: translateX(0);
}

/* Modern Particle Animation */
.particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
}

.particle {
    position: absolute;
    background: var(--primary-blue-light);
    border-radius: 50%;
    animation: particle-float 20s infinite linear;
    opacity: 0.6;
}

@keyframes particle-float {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 0.6;
    }
    90% {
        opacity: 0.6;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

/* Modern Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--light-grey);
}

::-webkit-scrollbar-thumb {
    background: var(--gradient-primary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gradient-dark);
}
