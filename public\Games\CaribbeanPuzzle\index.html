<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Caribbean Island Puzzle - Channel 99-9</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1a365d, #2c5282, #38b2ac);
            font-family: 'Arial', sans-serif;
            color: white;
            text-align: center;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .game-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        h1 {
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
            font-size: 2.5rem;
        }

        .puzzle-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            max-width: 300px;
            margin: 20px auto;
            background: rgba(0, 0, 0, 0.2);
            padding: 20px;
            border-radius: 15px;
        }

        .puzzle-piece {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #ff6b6b, #4ecdc4);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            user-select: none;
        }

        .puzzle-piece:hover {
            transform: scale(1.1);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        }

        .puzzle-piece.empty {
            background: rgba(255, 255, 255, 0.1);
            border: 2px dashed rgba(255, 255, 255, 0.3);
        }

        .controls {
            margin-top: 20px;
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            background: linear-gradient(135deg, #38b2ac, #2c5282);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .score {
            margin: 20px 0;
            font-size: 1.2rem;
            font-weight: 600;
        }

        .win-message {
            background: linear-gradient(135deg, #48bb78, #38a169);
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            display: none;
            animation: celebration 0.6s ease-in-out;
        }

        @keyframes celebration {
            0% { transform: scale(0.8); opacity: 0; }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); opacity: 1; }
        }

        .instructions {
            background: rgba(0, 0, 0, 0.2);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-size: 0.9rem;
            max-width: 400px;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <h1>🏝️ Caribbean Island Puzzle</h1>
        
        <div class="instructions">
            Arrange the Caribbean islands in the correct order! Click on pieces next to the empty space to move them.
        </div>

        <div class="score">
            Moves: <span id="moves">0</span> | Time: <span id="time">00:00</span>
        </div>

        <div class="puzzle-grid" id="puzzleGrid">
            <!-- Puzzle pieces will be generated by JavaScript -->
        </div>

        <div class="win-message" id="winMessage">
            🎉 Congratulations! You solved the Caribbean puzzle! 🎉
        </div>

        <div class="controls">
            <button class="btn" onclick="shufflePuzzle()">🔄 New Game</button>
            <button class="btn" onclick="solvePuzzle()">💡 Solve</button>
            <button class="btn" onclick="window.parent.postMessage('closeGame', '*')">🏠 Back to GameZone</button>
        </div>
    </div>

    <script>
        let puzzle = [];
        let emptyIndex = 8;
        let moves = 0;
        let startTime = null;
        let timerInterval = null;
        let isGameWon = false;

        const islands = ['🏝️', '🌴', '🏖️', '🌊', '🐚', '🦀', '🐠', '⛵'];
        const solvedState = [0, 1, 2, 3, 4, 5, 6, 7, 8];

        function initPuzzle() {
            puzzle = [...solvedState];
            emptyIndex = 8;
            moves = 0;
            isGameWon = false;
            startTime = null;
            if (timerInterval) clearInterval(timerInterval);
            
            document.getElementById('moves').textContent = moves;
            document.getElementById('time').textContent = '00:00';
            document.getElementById('winMessage').style.display = 'none';
            
            shufflePuzzle();
        }

        function shufflePuzzle() {
            // Shuffle the puzzle ensuring it's solvable
            for (let i = 0; i < 1000; i++) {
                const neighbors = getNeighbors(emptyIndex);
                const randomNeighbor = neighbors[Math.floor(Math.random() * neighbors.length)];
                swapPieces(emptyIndex, randomNeighbor);
            }
            
            moves = 0;
            document.getElementById('moves').textContent = moves;
            renderPuzzle();
            startTimer();
        }

        function getNeighbors(index) {
            const neighbors = [];
            const row = Math.floor(index / 3);
            const col = index % 3;

            if (row > 0) neighbors.push(index - 3); // Up
            if (row < 2) neighbors.push(index + 3); // Down
            if (col > 0) neighbors.push(index - 1); // Left
            if (col < 2) neighbors.push(index + 1); // Right

            return neighbors;
        }

        function swapPieces(index1, index2) {
            [puzzle[index1], puzzle[index2]] = [puzzle[index2], puzzle[index1]];
            if (puzzle[index1] === 8) emptyIndex = index1;
            if (puzzle[index2] === 8) emptyIndex = index2;
        }

        function movePiece(clickedIndex) {
            if (isGameWon) return;
            
            const neighbors = getNeighbors(emptyIndex);
            if (neighbors.includes(clickedIndex)) {
                swapPieces(emptyIndex, clickedIndex);
                moves++;
                document.getElementById('moves').textContent = moves;
                renderPuzzle();
                
                if (checkWin()) {
                    isGameWon = true;
                    clearInterval(timerInterval);
                    document.getElementById('winMessage').style.display = 'block';
                }
            }
        }

        function checkWin() {
            return puzzle.every((piece, index) => piece === index);
        }

        function renderPuzzle() {
            const grid = document.getElementById('puzzleGrid');
            grid.innerHTML = '';

            for (let i = 0; i < 9; i++) {
                const piece = document.createElement('div');
                piece.className = 'puzzle-piece';
                
                if (puzzle[i] === 8) {
                    piece.className += ' empty';
                    piece.textContent = '';
                } else {
                    piece.textContent = islands[puzzle[i]];
                    piece.onclick = () => movePiece(i);
                }
                
                grid.appendChild(piece);
            }
        }

        function solvePuzzle() {
            puzzle = [...solvedState];
            emptyIndex = 8;
            renderPuzzle();
            isGameWon = true;
            clearInterval(timerInterval);
            document.getElementById('winMessage').style.display = 'block';
        }

        function startTimer() {
            if (startTime === null) {
                startTime = Date.now();
                timerInterval = setInterval(updateTimer, 1000);
            }
        }

        function updateTimer() {
            if (startTime) {
                const elapsed = Math.floor((Date.now() - startTime) / 1000);
                const minutes = Math.floor(elapsed / 60).toString().padStart(2, '0');
                const seconds = (elapsed % 60).toString().padStart(2, '0');
                document.getElementById('time').textContent = `${minutes}:${seconds}`;
            }
        }

        // Initialize the game
        initPuzzle();
    </script>
</body>
</html>
