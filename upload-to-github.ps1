# Caribbean Advantage TV - GitHub Upload Script
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Caribbean Advantage TV - GitHub Upload" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if Git is installed
try {
    $gitVersion = git --version
    Write-Host "✅ Git is installed: $gitVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ ERROR: Git is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Git from: https://git-scm.com/downloads" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""

# Get the script directory and navigate to parent
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$parentDir = Split-Path -Parent $scriptDir
Set-Location $parentDir

Write-Host "📁 Working directory: $parentDir" -ForegroundColor Blue
Write-Host ""

# Remove existing repository folder if it exists
if (Test-Path "caribbeanadvantage") {
    Write-Host "🗑️ Removing existing repository folder..." -ForegroundColor Yellow
    Remove-Item -Recurse -Force "caribbeanadvantage"
}

# Clone the repository
Write-Host "📥 Cloning repository..." -ForegroundColor Blue
try {
    git clone https://github.com/joelgriiyo/caribbeanadvantage.git
    Write-Host "✅ Repository cloned successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ ERROR: Failed to clone repository" -ForegroundColor Red
    Write-Host "Make sure you have access to the repository and internet connection" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""

# Navigate to repository directory
Set-Location "caribbeanadvantage"

# Copy files from Caribbean Advantage TV folder
Write-Host "📁 Copying organized project files to repository..." -ForegroundColor Blue
$sourceDir = "..\CAribbean Advantage TV"

if (Test-Path $sourceDir) {
    # Copy specific files and directories in organized structure
    $filesToCopy = @(
        "server.js",
        "package.json",
        "Procfile",
        "app.json",
        ".gitignore",
        ".env.example",
        "README.md",
        "DEPLOYMENT.md",
        "DEPLOYMENT_SUMMARY.md",
        "PROJECT_STRUCTURE.md",
        "DEPLOYMENT_CHECKLIST.md"
    )

    $directoriesToCopy = @(
        "views",
        "public"
    )

    # Copy individual files
    foreach ($file in $filesToCopy) {
        $sourcePath = Join-Path $sourceDir $file
        if (Test-Path $sourcePath) {
            Copy-Item -Path $sourcePath -Destination "." -Force
            Write-Host "  ✓ Copied $file" -ForegroundColor Green
        } else {
            Write-Host "  ⚠ Warning: $file not found" -ForegroundColor Yellow
        }
    }

    # Copy directories
    foreach ($dir in $directoriesToCopy) {
        $sourcePath = Join-Path $sourceDir $dir
        if (Test-Path $sourcePath) {
            Copy-Item -Path $sourcePath -Destination "." -Recurse -Force
            Write-Host "  ✓ Copied $dir/ directory" -ForegroundColor Green
        } else {
            Write-Host "  ⚠ Warning: $dir directory not found" -ForegroundColor Yellow
        }
    }

    Write-Host "✅ Organized project structure copied successfully" -ForegroundColor Green
} else {
    Write-Host "❌ ERROR: Source directory not found: $sourceDir" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""

# Add all files to git
Write-Host "📦 Adding files to Git..." -ForegroundColor Blue
git add .

# Commit changes
Write-Host "💾 Committing changes..." -ForegroundColor Blue
$commitMessage = @"
🌍 Caribbean Advantage Channel 99-9 - Multi-Language Production Deployment

🌐 Multi-Language Features:
- Complete internationalization (i18n) system with 4 languages
- English, Spanish (Español), French (Français), Swahili (Kiswahili)
- Location-based automatic language detection
- Language switcher in top-right corner next to "Watch Live"
- Persistent language preferences with localStorage
- Real-time language switching without page reload
- Comprehensive translations for all UI elements

✨ Enhanced Features:
- Organized project structure (views/, public/, server.js)
- Live TV player integration with http://caribbeanadvantage.com/CA-tv.html
- Complete responsive website with enhanced design and animations
- Express.js server with security middleware (Helmet, CORS, Compression)
- Professional UI with glassmorphism effects and particle animations
- All pages connected: Home, Online TV, GameZone, Online Radio, Events, Contact
- Mobile-responsive design with smooth animations
- SEO optimized with proper meta tags and structure
- Error handling, 404 page, and loading states
- Environment configuration and deployment scripts

🛠️ Technical Stack:
- Node.js + Express.js server
- Advanced i18n system with geolocation detection
- Tailwind CSS + Custom animations
- Organized MVC-like structure
- Static asset optimization
- Security headers and CORS configuration
- Ready for Heroku deployment with Procfile and app.json

📺 Channel 99-9 Integration:
- External player iframe integration (caribbeanadvantage.com/CA-tv.html)
- Gaming Zone with tournaments and live streams
- Online Radio with 24/7 Caribbean music
- Events and local artist showcases
- Professional contact system with artist submissions
- Fullscreen support and player controls
- Real-time viewer count and stream info
- Keyboard shortcuts and error handling
- Responsive player for all devices

🌍 Global Accessibility:
- Multi-language support for international audience
- Location-based language detection for Caribbean, Latin America, France, Africa
- Cultural adaptation for different regions
- Professional translation system
- Accessibility features and responsive design

🎯 Production Ready:
- Environment variables configuration
- Comprehensive documentation and deployment guides
- Automated deployment scripts
- Performance optimizations
- Professional folder structure
- Multi-language SEO optimization
- International user experience
"@

try {
    git commit -m $commitMessage
    Write-Host "✅ Changes committed successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ ERROR: Failed to commit changes" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""

# Push to GitHub
Write-Host "🚀 Pushing to GitHub..." -ForegroundColor Blue
try {
    git push origin main
    Write-Host "✅ Successfully uploaded to GitHub!" -ForegroundColor Green
} catch {
    Write-Host "❌ ERROR: Failed to push to GitHub" -ForegroundColor Red
    Write-Host "You may need to authenticate or check your permissions" -ForegroundColor Yellow
    Write-Host "Try running: git push origin main" -ForegroundColor Yellow
    Read-Host "Press Enter to continue"
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "🎉 UPLOAD COMPLETE!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Your Caribbean Advantage TV website has been uploaded to:" -ForegroundColor White
Write-Host "https://github.com/joelgriiyo/caribbeanadvantage" -ForegroundColor Blue
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Visit your GitHub repository to verify files" -ForegroundColor White
Write-Host "2. Deploy to Heroku using the deployment guide" -ForegroundColor White
Write-Host "3. Test the live TV player integration" -ForegroundColor White
Write-Host ""
Write-Host "Repository URL: https://github.com/joelgriiyo/caribbeanadvantage" -ForegroundColor Blue
Write-Host ""

Read-Host "Press Enter to exit"
