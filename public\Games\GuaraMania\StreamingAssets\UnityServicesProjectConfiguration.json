{"Keys": ["com.unity.services.core.cloud-environment", "com.unity.services.core.version", "com.unity.services.core.initializer-assembly-qualified-names", "com.unity.services.core.all-package-names"], "Values": [{"m_Value": "production", "m_IsReadOnly": false}, {"m_Value": "1.6.0", "m_IsReadOnly": true}, {"m_Value": "Unity.Services.Core.Registration.CorePackageInitializer, Unity.Services.Core.Registration, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_IsReadOnly": true}, {"m_Value": "com.unity.services.core", "m_IsReadOnly": false}]}