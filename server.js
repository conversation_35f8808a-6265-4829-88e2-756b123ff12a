const express = require('express');
const path = require('path');
const compression = require('compression');
const helmet = require('helmet');
const cors = require('cors');
const morgan = require('morgan');

const app = express();
const PORT = process.env.PORT || 3000;

// Security middleware
app.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com", "https://cdn.tailwindcss.com"],
            scriptSrc: ["'self'", "'unsafe-inline'", "https://cdn.tailwindcss.com"],
            fontSrc: ["'self'", "https://fonts.gstatic.com"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'", "http://caribbeanadvantage.com"],
            frameSrc: ["'self'", "http://caribbeanadvantage.com", "https://caribbeanadvantage.com"],
            mediaSrc: ["'self'", "http://caribbeanadvantage.com", "https://caribbeanadvantage.com"]
        }
    }
}));

// Enable compression
app.use(compression());

// Enable CORS
app.use(cors());

// Logging middleware
app.use(morgan('combined'));

// Serve static files from public directory
app.use(express.static(path.join(__dirname, 'public'), {
    maxAge: '1d', // Cache static files for 1 day
    etag: true
}));

// Set view engine and views directory
app.set('view engine', 'html');
app.set('views', path.join(__dirname, 'views'));

// Custom middleware to serve HTML files
app.engine('html', (filePath, options, callback) => {
    const fs = require('fs');
    fs.readFile(filePath, (err, content) => {
        if (err) return callback(err);
        return callback(null, content.toString());
    });
});

// Routes
app.get('/', (req, res) => {
    res.render('index');
});

app.get('/contact', (req, res) => {
    res.render('contact');
});

app.get('/live', (req, res) => {
    res.render('live');
});

app.get('/watch', (req, res) => {
    res.render('live');
});

app.get('/gamezone', (req, res) => {
    res.render('gamezone');
});

app.get('/radio', (req, res) => {
    res.render('radio');
});

app.get('/events', (req, res) => {
    res.render('events');
});

// API endpoint for health check
app.get('/api/health', (req, res) => {
    res.json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
    });
});

// Handle 404 errors
app.use((req, res) => {
    res.status(404).render('404');
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).send('Something broke!');
});

app.listen(PORT, () => {
    console.log(`🚀 Caribbean Advantage TV server running on port ${PORT}`);
    console.log(`🌐 Visit: http://localhost:${PORT}`);
});

module.exports = app;
