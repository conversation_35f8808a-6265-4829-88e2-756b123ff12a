<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Caribbean Radio - Caribbean Advantage | Channel 99-9</title>
    <meta name="description" content="Listen to Caribbean Radio on Channel 99-9. 24/7 music, talk shows, and local artists broadcasting from North Coast Puerto Rico.">

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Styles -->
    <link rel="stylesheet" href="/css/styles.css">

    <style>
        .radio-hero {
            background: linear-gradient(135deg, var(--success-green) 0%, var(--mint-accent) 50%, var(--ocean-blue) 100%);
            position: relative;
            overflow: hidden;
        }

        .radio-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="waves" width="40" height="20" patternUnits="userSpaceOnUse"><path d="M0 10 Q10 0 20 10 T40 10" fill="none" stroke="white" stroke-width="0.5" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23waves)"/></svg>');
            pointer-events: none;
        }

        .radio-player {
            background: linear-gradient(135deg, var(--charcoal), var(--deep-ocean));
            border-radius: var(--radius-xl);
            padding: var(--spacing-xl);
            color: white;
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-xl);
        }

        .radio-player::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 20%, rgba(78, 205, 196, 0.2) 0%, transparent 50%);
            pointer-events: none;
        }

        .player-display {
            text-align: center;
            margin-bottom: var(--spacing-xl);
        }

        .album-art {
            width: 200px;
            height: 200px;
            background: linear-gradient(135deg, var(--mint-accent), var(--success-green));
            border-radius: var(--radius-lg);
            margin: 0 auto var(--spacing-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 4rem;
            box-shadow: var(--shadow-lg);
            animation: rotate 20s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .now-playing-info h3 {
            font-size: 1.5rem;
            margin-bottom: var(--spacing-xs);
            color: white;
        }

        .now-playing-info p {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: var(--spacing-sm);
        }

        .live-badge {
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
            background: var(--coral-accent);
            color: white;
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-sm);
            font-weight: 600;
            font-size: 0.9rem;
            animation: pulse 2s infinite;
        }

        .player-controls {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-lg);
        }

        .control-button {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            transition: all var(--transition-normal);
        }

        .control-button:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: white;
            transform: scale(1.1);
        }

        .control-button.play {
            width: 80px;
            height: 80px;
            font-size: 2rem;
            background: var(--mint-accent);
            border-color: var(--mint-accent);
        }

        .volume-control {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            justify-content: center;
        }

        .volume-slider {
            width: 150px;
            height: 6px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
            outline: none;
            appearance: none;
        }

        .volume-slider::-webkit-slider-thumb {
            appearance: none;
            width: 20px;
            height: 20px;
            background: var(--mint-accent);
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
        }

        .show-card {
            background: var(--white);
            border-radius: var(--radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-md);
            transition: all var(--transition-normal);
            border: 1px solid var(--medium-gray);
        }

        .show-card:hover {
            transform: translateY(-6px);
            box-shadow: var(--shadow-xl);
            border-color: var(--mint-accent);
        }

        .show-header {
            height: 120px;
            background: linear-gradient(135deg, var(--success-green), var(--mint-accent));
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            color: white;
            position: relative;
        }

        .show-time {
            position: absolute;
            top: var(--spacing-sm);
            right: var(--spacing-sm);
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-sm);
            font-size: 0.8rem;
            font-weight: 600;
        }

        .show-content {
            padding: var(--spacing-md);
        }

        .show-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--deep-ocean);
            margin-bottom: var(--spacing-xs);
        }

        .show-host {
            color: var(--mint-accent);
            font-weight: 500;
            margin-bottom: var(--spacing-xs);
        }

        .show-description {
            color: var(--slate-gray);
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .dj-card {
            background: linear-gradient(135deg, var(--white) 0%, var(--powder-blue) 100%);
            border-radius: var(--radius-xl);
            padding: var(--spacing-lg);
            text-align: center;
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--sky-blue);
            transition: all var(--transition-normal);
        }

        .dj-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
        }

        .dj-avatar {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, var(--mint-accent), var(--success-green));
            border-radius: 50%;
            margin: 0 auto var(--spacing-md);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            color: white;
            box-shadow: var(--shadow-md);
        }

        .dj-name {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--deep-ocean);
            margin-bottom: var(--spacing-xs);
        }

        .dj-show {
            color: var(--mint-accent);
            font-weight: 500;
            margin-bottom: var(--spacing-sm);
        }

        .dj-bio {
            color: var(--slate-gray);
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .frequency-display {
            background: rgba(255, 255, 255, 0.1);
            border-radius: var(--radius-md);
            padding: var(--spacing-md);
            text-align: center;
            margin-bottom: var(--spacing-lg);
        }

        .frequency-number {
            font-size: 3rem;
            font-weight: 800;
            color: var(--mint-accent);
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .frequency-label {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.1rem;
            margin-top: var(--spacing-xs);
        }

        .playlist-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            padding: var(--spacing-md);
            background: var(--white);
            border-radius: var(--radius-md);
            margin-bottom: var(--spacing-sm);
            box-shadow: var(--shadow-sm);
            transition: all var(--transition-fast);
        }

        .playlist-item:hover {
            transform: translateX(8px);
            box-shadow: var(--shadow-md);
            border-left: 4px solid var(--mint-accent);
        }

        .track-number {
            width: 30px;
            height: 30px;
            background: var(--mint-accent);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .track-info {
            flex: 1;
        }

        .track-title {
            font-weight: 600;
            color: var(--deep-ocean);
            margin-bottom: 2px;
        }

        .track-artist {
            color: var(--slate-gray);
            font-size: 0.9rem;
        }

        .track-duration {
            color: var(--slate-gray);
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <!-- Modern Navigation -->
    <nav class="navbar" id="navbar">
        <div class="container">
            <div class="nav-container">
                <a href="/" class="nav-logo">
                    <div class="nav-logo-icon">CA</div>
                    <span>Caribbean Advantage</span>
                </a>

                <ul class="nav-menu" id="nav-menu">
                    <li><a href="/" class="nav-link">Home</a></li>
                    <li><a href="/live" class="nav-link">Live TV</a></li>
                    <li><a href="/gamezone" class="nav-link">GameZone</a></li>
                    <li><a href="/radio" class="nav-link active">Radio</a></li>
                    <li><a href="/events" class="nav-link">Events</a></li>
                    <li><a href="/contact" class="nav-link">Contact</a></li>
                </ul>

                <button class="nav-toggle" id="nav-toggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </div>
    </nav>

    <!-- Radio Hero -->
    <section class="hero radio-hero">
        <div class="container">
            <div class="hero-content">
                <h1>Caribbean Radio</h1>
                <p>Tune into the sounds of the Caribbean with 24/7 music, talk shows, and local artists broadcasting live from Channel 99-9.</p>

                <div class="d-flex justify-center" style="gap: var(--spacing-md); flex-wrap: wrap;">
                    <a href="#player" class="btn btn-primary">
                        🎵 Listen Live
                    </a>
                    <a href="#schedule" class="btn btn-secondary">
                        📅 Show Schedule
                    </a>
                    <a href="#djs" class="btn btn-ghost">
                        🎤 Meet Our DJs
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Radio Player Section -->
    <section class="section" id="player">
        <div class="container">
            <div class="radio-player">
                <div class="frequency-display">
                    <div class="frequency-number">99.9</div>
                    <div class="frequency-label">FM • Caribbean Advantage Radio</div>
                </div>

                <div class="player-display">
                    <div class="album-art">🎵</div>
                    <div class="now-playing-info">
                        <div class="live-badge">
                            <span style="width: 8px; height: 8px; background: white; border-radius: 50%;"></span>
                            LIVE
                        </div>
                        <h3 id="track-title">Caribbean Vibes Mix</h3>
                        <p id="track-artist">DJ Island Breeze</p>
                        <p style="font-size: 0.9rem;">Now Playing • Caribbean Advantage Radio</p>
                    </div>
                </div>

                <div class="player-controls">
                    <button class="control-button" id="prev-btn">⏮️</button>
                    <button class="control-button play" id="play-pause-btn">▶️</button>
                    <button class="control-button" id="next-btn">⏭️</button>
                </div>

                <div class="volume-control">
                    <span>🔊</span>
                    <input type="range" class="volume-slider" min="0" max="100" value="75" id="volume-slider">
                    <span id="volume-display">75%</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Show Schedule Section -->
    <section class="section" style="background: var(--light-gray);" id="schedule">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Radio Schedule</h2>
                <p class="section-subtitle">Discover our daily programming featuring the best Caribbean music, talk shows, and local content.</p>
            </div>

            <div class="content-grid">
                <div class="show-card">
                    <div class="show-header">
                        🌅
                        <div class="show-time">6:00 AM</div>
                    </div>
                    <div class="show-content">
                        <h3 class="show-title">Morning Caribbean</h3>
                        <p class="show-host">with DJ Sunrise</p>
                        <p class="show-description">Start your day with uplifting Caribbean music, news, and positive vibes to energize your morning.</p>
                    </div>
                </div>

                <div class="show-card">
                    <div class="show-header" style="background: linear-gradient(135deg, var(--warning-orange), var(--coral-accent));">
                        ☀️
                        <div class="show-time">9:00 AM</div>
                    </div>
                    <div class="show-content">
                        <h3 class="show-title">Island Workday</h3>
                        <p class="show-host">with DJ Productivity</p>
                        <p class="show-description">Smooth Caribbean rhythms and motivational music to keep you focused and productive throughout your workday.</p>
                    </div>
                </div>

                <div class="show-card">
                    <div class="show-header" style="background: linear-gradient(135deg, var(--ocean-blue), var(--sky-blue));">
                        🎵
                        <div class="show-time">12:00 PM</div>
                    </div>
                    <div class="show-content">
                        <h3 class="show-title">Lunch Break Beats</h3>
                        <p class="show-host">with DJ Midday</p>
                        <p class="show-description">Perfect lunch companion featuring Caribbean classics and contemporary hits to brighten your midday break.</p>
                    </div>
                </div>

                <div class="show-card">
                    <div class="show-header" style="background: linear-gradient(135deg, var(--coral-accent), var(--mint-accent));">
                        🎤
                        <div class="show-time">3:00 PM</div>
                    </div>
                    <div class="show-content">
                        <h3 class="show-title">Caribbean Talk</h3>
                        <p class="show-host">with Host Maria Santos</p>
                        <p class="show-description">Engaging discussions about Caribbean culture, community events, and interviews with local personalities.</p>
                    </div>
                </div>

                <div class="show-card">
                    <div class="show-header" style="background: linear-gradient(135deg, var(--deep-ocean), var(--steel-blue));">
                        🌆
                        <div class="show-time">6:00 PM</div>
                    </div>
                    <div class="show-content">
                        <h3 class="show-title">Evening Vibes</h3>
                        <p class="show-host">with DJ Sunset</p>
                        <p class="show-description">Unwind with smooth Caribbean sounds, perfect for your evening commute and relaxation time.</p>
                    </div>
                </div>

                <div class="show-card">
                    <div class="show-header" style="background: linear-gradient(135deg, var(--charcoal), var(--deep-ocean));">
                        🌙
                        <div class="show-time">9:00 PM</div>
                    </div>
                    <div class="show-content">
                        <h3 class="show-title">Night Rhythms</h3>
                        <p class="show-host">with DJ Moonlight</p>
                        <p class="show-description">Late-night Caribbean music mix featuring soulful rhythms and romantic melodies for the perfect evening atmosphere.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- DJs Section -->
    <section class="section" id="djs">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Meet Our DJs</h2>
                <p class="section-subtitle">Get to know the voices behind Caribbean Advantage Radio who bring you the best Caribbean entertainment.</p>
            </div>

            <div class="content-grid">
                <div class="dj-card">
                    <div class="dj-avatar">🎤</div>
                    <h3 class="dj-name">DJ Island Breeze</h3>
                    <p class="dj-show">Morning Caribbean & Evening Vibes</p>
                    <p class="dj-bio">Born and raised in Puerto Rico, Island Breeze brings authentic Caribbean energy with 10+ years of radio experience and deep knowledge of local music culture.</p>
                </div>

                <div class="dj-card">
                    <div class="dj-avatar" style="background: linear-gradient(135deg, var(--coral-accent), var(--warning-orange));">🎵</div>
                    <h3 class="dj-name">DJ Tropical Storm</h3>
                    <p class="dj-show">Night Rhythms & Weekend Mix</p>
                    <p class="dj-bio">Specializing in contemporary Caribbean fusion and electronic beats, Tropical Storm creates unique mixes that blend traditional and modern Caribbean sounds.</p>
                </div>

                <div class="dj-card">
                    <div class="dj-avatar" style="background: linear-gradient(135deg, var(--ocean-blue), var(--steel-blue));">🌊</div>
                    <h3 class="dj-name">Maria Santos</h3>
                    <p class="dj-show">Caribbean Talk & Community Hour</p>
                    <p class="dj-bio">Award-winning journalist and community advocate, Maria brings insightful discussions about Caribbean culture, politics, and social issues to our listeners.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Current Playlist Section -->
    <section class="section" style="background: var(--light-gray);">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Current Playlist</h2>
                <p class="section-subtitle">Recently played tracks on Caribbean Advantage Radio.</p>
            </div>

            <div style="max-width: 600px; margin: 0 auto;">
                <div class="playlist-item">
                    <div class="track-number">1</div>
                    <div class="track-info">
                        <div class="track-title">Despacito (Caribbean Remix)</div>
                        <div class="track-artist">Luis Fonsi ft. Daddy Yankee</div>
                    </div>
                    <div class="track-duration">3:47</div>
                </div>

                <div class="playlist-item">
                    <div class="track-number">2</div>
                    <div class="track-info">
                        <div class="track-title">Island in the Sun</div>
                        <div class="track-artist">Weezer</div>
                    </div>
                    <div class="track-duration">3:20</div>
                </div>

                <div class="playlist-item">
                    <div class="track-number">3</div>
                    <div class="track-info">
                        <div class="track-title">Bambaataa</div>
                        <div class="track-artist">Shaggy</div>
                    </div>
                    <div class="track-duration">3:55</div>
                </div>

                <div class="playlist-item">
                    <div class="track-number">4</div>
                    <div class="track-info">
                        <div class="track-title">Could You Be Loved</div>
                        <div class="track-artist">Bob Marley & The Wailers</div>
                    </div>
                    <div class="track-duration">3:57</div>
                </div>

                <div class="playlist-item">
                    <div class="track-number">5</div>
                    <div class="track-info">
                        <div class="track-title">La Vida Es Una Fiesta</div>
                        <div class="track-artist">Manu Chao</div>
                    </div>
                    <div class="track-duration">2:58</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>Caribbean Advantage TV</h4>
                    <p>Modern Caribbean entertainment on Channel 99-9. Broadcasting live from North Coast Puerto Rico.</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <p><a href="/live">Live TV</a></p>
                    <p><a href="/radio">Radio</a></p>
                    <p><a href="/gamezone">GameZone</a></p>
                    <p><a href="/events">Events</a></p>
                </div>
                <div class="footer-section">
                    <h4>Contact</h4>
                    <p>Channel 99-9</p>
                    <p>North Coast Puerto Rico</p>
                    <p><EMAIL></p>
                </div>
                <div class="footer-section">
                    <h4>Follow Us</h4>
                    <p><a href="#">Facebook</a></p>
                    <p><a href="#">Twitter</a></p>
                    <p><a href="#">Instagram</a></p>
                    <p><a href="#">YouTube</a></p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 Caribbean Advantage TV. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="/js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Navigation functionality
            const navbar = document.getElementById('navbar');
            const navToggle = document.getElementById('nav-toggle');
            const navMenu = document.getElementById('nav-menu');

            window.addEventListener('scroll', function() {
                if (window.scrollY > 50) {
                    navbar.classList.add('scrolled');
                } else {
                    navbar.classList.remove('scrolled');
                }
            });

            navToggle.addEventListener('click', function() {
                navToggle.classList.toggle('active');
                navMenu.classList.toggle('active');
            });

            // Radio player functionality
            const playPauseBtn = document.getElementById('play-pause-btn');
            const volumeSlider = document.getElementById('volume-slider');
            const volumeDisplay = document.getElementById('volume-display');
            const trackTitle = document.getElementById('track-title');
            const trackArtist = document.getElementById('track-artist');

            let isPlaying = false;

            playPauseBtn.addEventListener('click', function() {
                isPlaying = !isPlaying;
                this.textContent = isPlaying ? '⏸️' : '▶️';

                if (isPlaying) {
                    // Simulate playing
                    console.log('Radio started playing');
                } else {
                    console.log('Radio paused');
                }
            });

            volumeSlider.addEventListener('input', function() {
                volumeDisplay.textContent = this.value + '%';
            });

            // Simulate track changes
            const tracks = [
                { title: 'Caribbean Vibes Mix', artist: 'DJ Island Breeze' },
                { title: 'Tropical Sunset', artist: 'DJ Tropical Storm' },
                { title: 'Island Rhythms', artist: 'DJ Moonlight' },
                { title: 'Morning Breeze', artist: 'DJ Sunrise' }
            ];

            let currentTrack = 0;

            function changeTrack() {
                currentTrack = (currentTrack + 1) % tracks.length;
                trackTitle.textContent = tracks[currentTrack].title;
                trackArtist.textContent = tracks[currentTrack].artist;
            }

            // Change track every 30 seconds for demo
            setInterval(changeTrack, 30000);
        });
    </script>
</body>
</html>