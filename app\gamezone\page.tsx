'use client'

import { motion } from 'framer-motion'
import { Navigation } from '../components/Navigation'
import { Footer } from '../components/Footer'
import { FloatingElements } from '../components/FloatingElements'
import { Gamepad2, Trophy, Users, Star, Play, Zap } from 'lucide-react'
import { useLanguage } from '../components/LanguageProvider'

export default function GameZone() {
  const { t } = useLanguage()

  const games = [
    {
      title: 'Caribbean Quest',
      description: 'Explore the beautiful Caribbean islands in this adventure game',
      image: '🏝️',
      players: '2.1K',
      rating: '4.8',
      category: 'Adventure'
    },
    {
      title: 'Salsa Dance Challenge',
      description: 'Dance to the rhythm of Caribbean music',
      image: '💃',
      players: '1.8K',
      rating: '4.7',
      category: 'Music'
    },
    {
      title: 'Island Racing',
      description: 'Race through tropical paradise tracks',
      image: '🏎️',
      players: '3.2K',
      rating: '4.9',
      category: 'Racing'
    },
    {
      title: 'Treasure Hunt',
      description: 'Find hidden treasures across the Caribbean',
      image: '💎',
      players: '1.5K',
      rating: '4.6',
      category: 'Puzzle'
    },
    {
      title: 'Beach Volleyball',
      description: 'Competitive beach volleyball tournament',
      image: '🏐',
      players: '2.7K',
      rating: '4.8',
      category: 'Sports'
    },
    {
      title: 'Carnival Celebration',
      description: 'Experience the vibrant Caribbean carnival',
      image: '🎭',
      players: '1.9K',
      rating: '4.7',
      category: 'Simulation'
    }
  ]

  return (
    <main className="relative min-h-screen">
      <Navigation />
      <FloatingElements />
      
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1 }}
        className="relative z-10 pt-24"
      >
        {/* Hero Section */}
        <section className="py-20">
          <div className="container mx-auto px-6">
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-center mb-16"
            >
              <motion.div
                className="inline-flex items-center space-x-2 glass-effect px-6 py-3 rounded-full mb-6"
                whileHover={{ scale: 1.05 }}
              >
                <Gamepad2 className="w-5 h-5 text-purple-400" />
                <span className="text-sm font-semibold text-white">Interactive Gaming</span>
              </motion.div>
              
              <h1 className="text-5xl md:text-7xl font-black mb-6 gradient-text">
                {t('nav.gamezone')}
              </h1>
              <p className="text-xl text-blue-200 max-w-3xl mx-auto">
                Immerse yourself in Caribbean-themed games and interactive experiences
              </p>
            </motion.div>

            {/* Stats */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto mb-16"
            >
              {[
                { icon: Users, label: 'Active Players', value: '12.5K+', color: 'text-blue-400' },
                { icon: Trophy, label: 'Tournaments', value: '45+', color: 'text-yellow-400' },
                { icon: Star, label: 'Average Rating', value: '4.8', color: 'text-green-400' }
              ].map((stat, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.1 + 0.5 }}
                  className="glass-card text-center hover:glow-effect transition-all duration-300"
                  whileHover={{ scale: 1.05, y: -5 }}
                >
                  <stat.icon className={`w-12 h-12 ${stat.color} mx-auto mb-4`} />
                  <div className="text-3xl font-bold text-white mb-2">{stat.value}</div>
                  <div className="text-blue-200">{stat.label}</div>
                </motion.div>
              ))}
            </motion.div>

            {/* Featured Game */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="max-w-6xl mx-auto mb-16"
            >
              <div className="glass-card overflow-hidden relative">
                <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 to-pink-600/20" />
                <div className="relative z-10 p-8 md:p-12">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                    <div>
                      <motion.div
                        className="inline-flex items-center space-x-2 bg-purple-600/30 px-4 py-2 rounded-full mb-4"
                        whileHover={{ scale: 1.05 }}
                      >
                        <Zap className="w-4 h-4 text-yellow-400" />
                        <span className="text-sm font-semibold text-white">Featured Game</span>
                      </motion.div>
                      
                      <h2 className="text-4xl font-bold text-white mb-4">Caribbean Quest</h2>
                      <p className="text-blue-200 text-lg mb-6">
                        Embark on an epic adventure through the Caribbean islands. Discover hidden treasures, 
                        meet local characters, and experience the rich culture of the Caribbean.
                      </p>
                      
                      <div className="flex items-center space-x-6 mb-8">
                        <div className="flex items-center space-x-2">
                          <Users className="w-5 h-5 text-blue-400" />
                          <span className="text-white">2.1K players</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Star className="w-5 h-5 text-yellow-400" />
                          <span className="text-white">4.8 rating</span>
                        </div>
                      </div>
                      
                      <motion.button
                        whileHover={{ scale: 1.05, y: -2 }}
                        whileTap={{ scale: 0.95 }}
                        className="btn-primary flex items-center space-x-3 text-lg"
                      >
                        <Play className="w-6 h-6" />
                        <span>Play Now</span>
                      </motion.button>
                    </div>
                    
                    <div className="relative">
                      <motion.div
                        className="text-8xl md:text-9xl text-center"
                        animate={{ 
                          rotate: [0, 5, -5, 0],
                          scale: [1, 1.1, 1]
                        }}
                        transition={{ 
                          duration: 4, 
                          repeat: Infinity,
                          ease: "easeInOut"
                        }}
                      >
                        🏝️
                      </motion.div>
                      <motion.div
                        className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-green-500/20 rounded-full blur-3xl"
                        animate={{ 
                          scale: [1, 1.2, 1],
                          opacity: [0.3, 0.6, 0.3]
                        }}
                        transition={{ 
                          duration: 3, 
                          repeat: Infinity 
                        }}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Games Grid */}
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.9 }}
            >
              <h2 className="text-3xl font-bold text-white mb-8 text-center">All Games</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {games.map((game, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 + 1.1 }}
                    className="glass-card group hover:glow-effect transition-all duration-300 cursor-pointer"
                    whileHover={{ scale: 1.02, y: -10 }}
                  >
                    <div className="text-center mb-4">
                      <motion.div
                        className="text-6xl mb-4"
                        whileHover={{ scale: 1.2, rotate: 10 }}
                        transition={{ duration: 0.3 }}
                      >
                        {game.image}
                      </motion.div>
                      <span className="inline-block bg-purple-600/30 text-purple-300 px-3 py-1 rounded-full text-sm">
                        {game.category}
                      </span>
                    </div>
                    
                    <h3 className="text-xl font-bold text-white mb-2">{game.title}</h3>
                    <p className="text-blue-200 text-sm mb-4">{game.description}</p>
                    
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center space-x-2">
                        <Users className="w-4 h-4 text-blue-400" />
                        <span className="text-white text-sm">{game.players}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Star className="w-4 h-4 text-yellow-400" />
                        <span className="text-white text-sm">{game.rating}</span>
                      </div>
                    </div>
                    
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="w-full btn-secondary flex items-center justify-center space-x-2"
                    >
                      <Play className="w-4 h-4" />
                      <span>Play Game</span>
                    </motion.button>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </div>
        </section>

        <Footer />
      </motion.div>
    </main>
  )
}
