<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live TV - Caribbean Advantage | Channel 99-9</title>
    <meta name="description" content="Watch live Caribbean TV broadcasts on Channel 99-9. High-quality streaming with local news, entertainment, and cultural programming.">
    
    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <!-- Styles -->
    <link rel="stylesheet" href="/css/styles.css">
    
    <style>
        .live-player {
            background: linear-gradient(135deg, var(--deep-ocean), var(--ocean-blue));
            border-radius: var(--radius-xl);
            padding: var(--spacing-xl);
            position: relative;
            overflow: hidden;
        }
        
        .live-player::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="white" stroke-width="0.5" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            pointer-events: none;
        }
        
        .video-container {
            aspect-ratio: 16/9;
            background: rgba(0, 0, 0, 0.8);
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
            border: 2px solid rgba(255, 255, 255, 0.2);
        }
        
        .live-indicator {
            position: absolute;
            top: var(--spacing-sm);
            left: var(--spacing-sm);
            background: var(--coral-accent);
            color: white;
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-sm);
            font-weight: 600;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        .quality-badge {
            position: absolute;
            top: var(--spacing-sm);
            right: var(--spacing-sm);
            background: rgba(255, 255, 255, 0.9);
            color: var(--deep-ocean);
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-sm);
            font-weight: 600;
            font-size: 0.8rem;
        }
        
        .show-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: var(--spacing-lg);
        }
        
        .show-card {
            background: var(--white);
            border-radius: var(--radius-lg);
            overflow: hidden;
            box-shadow: var(--shadow-md);
            transition: all var(--transition-normal);
            border: 1px solid var(--medium-gray);
        }
        
        .show-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: var(--shadow-xl);
            border-color: var(--ocean-blue);
        }
        
        .show-thumbnail {
            height: 160px;
            background: linear-gradient(135deg, var(--sky-blue), var(--steel-blue));
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            color: white;
            position: relative;
        }
        
        .show-time {
            position: absolute;
            bottom: var(--spacing-xs);
            left: var(--spacing-xs);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 2px var(--spacing-xs);
            border-radius: var(--radius-sm);
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .show-info {
            padding: var(--spacing-md);
        }
        
        .show-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--deep-ocean);
            margin-bottom: var(--spacing-xs);
        }
        
        .show-description {
            color: var(--slate-gray);
            font-size: 0.9rem;
            line-height: 1.4;
        }
        
        .controls-panel {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            margin-top: var(--spacing-lg);
            border: 1px solid var(--medium-gray);
        }
        
        .control-group {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-md);
        }
        
        .control-group:last-child {
            margin-bottom: 0;
        }
        
        .volume-slider {
            flex: 1;
            height: 6px;
            background: var(--light-gray);
            border-radius: 3px;
            outline: none;
            appearance: none;
        }
        
        .volume-slider::-webkit-slider-thumb {
            appearance: none;
            width: 18px;
            height: 18px;
            background: var(--ocean-blue);
            border-radius: 50%;
            cursor: pointer;
        }
        
        .filter-tabs {
            display: flex;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }
        
        .filter-tab {
            padding: var(--spacing-sm) var(--spacing-lg);
            background: var(--white);
            border: 2px solid var(--medium-gray);
            border-radius: var(--radius-md);
            color: var(--charcoal);
            text-decoration: none;
            font-weight: 500;
            transition: all var(--transition-fast);
        }
        
        .filter-tab:hover,
        .filter-tab.active {
            background: var(--ocean-blue);
            color: var(--white);
            border-color: var(--ocean-blue);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <!-- Modern Navigation -->
    <nav class="navbar" id="navbar">
        <div class="container">
            <div class="nav-container">
                <a href="/" class="nav-logo">
                    <div class="nav-logo-icon">CA</div>
                    <span>Caribbean Advantage</span>
                </a>
                
                <ul class="nav-menu" id="nav-menu">
                    <li><a href="/" class="nav-link">Home</a></li>
                    <li><a href="/live" class="nav-link active">Live TV</a></li>
                    <li><a href="/gamezone" class="nav-link">GameZone</a></li>
                    <li><a href="/radio" class="nav-link">Radio</a></li>
                    <li><a href="/events" class="nav-link">Events</a></li>
                    <li><a href="/contact" class="nav-link">Contact</a></li>
                </ul>
                
                <button class="nav-toggle" id="nav-toggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </div>
    </nav>

    <!-- Live TV Hero -->
    <section class="hero" style="min-height: 60vh;">
        <div class="container">
            <div class="hero-content">
                <h1>Live Caribbean Television</h1>
                <p>Experience high-quality live broadcasting from Channel 99-9 with crystal-clear HD streaming and immersive Caribbean content.</p>
            </div>
        </div>
    </section>

    <!-- Live Player Section -->
    <section class="section">
        <div class="container">
            <div class="live-player">
                <div class="video-container">
                    <div class="live-indicator">
                        <span style="width: 8px; height: 8px; background: white; border-radius: 50%; animation: pulse 1s infinite;"></span>
                        LIVE
                    </div>
                    <div class="quality-badge">HD 1080p</div>
                    
                    <!-- Video Player Placeholder -->
                    <div style="font-size: 4rem; color: white;">📺</div>
                    <div style="position: absolute; bottom: 20px; color: white; text-align: center;">
                        <h3>Caribbean Advantage TV</h3>
                        <p>Channel 99-9 • North Coast Puerto Rico</p>
                    </div>
                </div>
                
                <div class="controls-panel">
                    <div class="control-group">
                        <button class="btn btn-primary">▶️ Play</button>
                        <button class="btn btn-secondary">⏸️ Pause</button>
                        <button class="btn btn-ghost">🔊 Mute</button>
                        <input type="range" class="volume-slider" min="0" max="100" value="75">
                        <button class="btn btn-ghost">⛶ Fullscreen</button>
                    </div>
                    
                    <div class="control-group">
                        <span style="color: var(--slate-gray); font-weight: 500;">Quality:</span>
                        <button class="btn btn-ghost">1080p HD</button>
                        <button class="btn btn-ghost">720p</button>
                        <button class="btn btn-ghost">480p</button>
                        <span style="margin-left: auto; color: var(--slate-gray);">👥 1,247 viewers</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- TV Schedule Section -->
    <section class="section" style="background: var(--light-gray);">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">TV Schedule</h2>
                <p class="section-subtitle">Discover what's playing now and coming up next on Channel 99-9.</p>
            </div>
            
            <div class="filter-tabs">
                <a href="#" class="filter-tab active">All Shows</a>
                <a href="#" class="filter-tab">News</a>
                <a href="#" class="filter-tab">Entertainment</a>
                <a href="#" class="filter-tab">Music</a>
                <a href="#" class="filter-tab">Sports</a>
                <a href="#" class="filter-tab">Culture</a>
            </div>
            
            <div class="show-grid">
                <div class="show-card">
                    <div class="show-thumbnail">
                        📰
                        <div class="show-time">8:00 PM</div>
                    </div>
                    <div class="show-info">
                        <h3 class="show-title">Caribbean News Tonight</h3>
                        <p class="show-description">Latest news and current events from across the Caribbean region with in-depth analysis.</p>
                    </div>
                </div>
                
                <div class="show-card">
                    <div class="show-thumbnail" style="background: linear-gradient(135deg, var(--coral-accent), var(--mint-accent));">
                        🎵
                        <div class="show-time">9:00 PM</div>
                    </div>
                    <div class="show-info">
                        <h3 class="show-title">Caribbean Music Showcase</h3>
                        <p class="show-description">Featuring the best local artists and traditional Caribbean music performances.</p>
                    </div>
                </div>
                
                <div class="show-card">
                    <div class="show-thumbnail" style="background: linear-gradient(135deg, var(--success-green), var(--ocean-blue));">
                        🏖️
                        <div class="show-time">10:00 PM</div>
                    </div>
                    <div class="show-info">
                        <h3 class="show-title">Island Life</h3>
                        <p class="show-description">Exploring Caribbean culture, traditions, and the vibrant island lifestyle.</p>
                    </div>
                </div>
                
                <div class="show-card">
                    <div class="show-thumbnail" style="background: linear-gradient(135deg, var(--warning-orange), var(--coral-accent));">
                        ⚽
                        <div class="show-time">11:00 PM</div>
                    </div>
                    <div class="show-info">
                        <h3 class="show-title">Sports Caribbean</h3>
                        <p class="show-description">Coverage of local sports events, highlights, and athlete interviews.</p>
                    </div>
                </div>
                
                <div class="show-card">
                    <div class="show-thumbnail">
                        🎬
                        <div class="show-time">12:00 AM</div>
                    </div>
                    <div class="show-info">
                        <h3 class="show-title">Caribbean Cinema</h3>
                        <p class="show-description">Classic and contemporary films from Caribbean filmmakers and artists.</p>
                    </div>
                </div>
                
                <div class="show-card">
                    <div class="show-thumbnail" style="background: linear-gradient(135deg, var(--mint-accent), var(--sky-blue));">
                        🌅
                        <div class="show-time">6:00 AM</div>
                    </div>
                    <div class="show-info">
                        <h3 class="show-title">Morning Caribbean</h3>
                        <p class="show-description">Start your day with news, weather, and uplifting Caribbean content.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>Caribbean Advantage TV</h4>
                    <p>Modern Caribbean entertainment on Channel 99-9. Broadcasting live from North Coast Puerto Rico.</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <p><a href="/live">Live TV</a></p>
                    <p><a href="/radio">Radio</a></p>
                    <p><a href="/gamezone">GameZone</a></p>
                    <p><a href="/events">Events</a></p>
                </div>
                <div class="footer-section">
                    <h4>Contact</h4>
                    <p>Channel 99-9</p>
                    <p>North Coast Puerto Rico</p>
                    <p><EMAIL></p>
                </div>
                <div class="footer-section">
                    <h4>Follow Us</h4>
                    <p><a href="#">Facebook</a></p>
                    <p><a href="#">Twitter</a></p>
                    <p><a href="#">Instagram</a></p>
                    <p><a href="#">YouTube</a></p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 Caribbean Advantage TV. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="/js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Navigation functionality
            const navbar = document.getElementById('navbar');
            const navToggle = document.getElementById('nav-toggle');
            const navMenu = document.getElementById('nav-menu');
            
            window.addEventListener('scroll', function() {
                if (window.scrollY > 50) {
                    navbar.classList.add('scrolled');
                } else {
                    navbar.classList.remove('scrolled');
                }
            });
            
            navToggle.addEventListener('click', function() {
                navToggle.classList.toggle('active');
                navMenu.classList.toggle('active');
            });
            
            // Filter tabs functionality
            const filterTabs = document.querySelectorAll('.filter-tab');
            filterTabs.forEach(tab => {
                tab.addEventListener('click', function(e) {
                    e.preventDefault();
                    filterTabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                });
            });
        });
    </script>
</body>
</html>
