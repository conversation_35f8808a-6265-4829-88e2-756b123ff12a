<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Watch Live - Caribbean Advantage Channel 99-9</title>
    <meta name="description" content="Watch Caribbean Advantage Channel 99-9 live streaming 24/7. Gaming, Online Radio, and Live TV from North Coast Puerto Rico.">

    <!-- Stylesheets -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/css/styles.css">

    <!-- Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'ca-blue': '#003d6b',
                        'ca-dark-blue': '#001f3f',
                        'ca-light-blue': '#0066cc',
                        'ca-accent-blue': '#4a90e2',
                        'ca-yellow': '#ffc107',
                        'ca-orange': '#ff9800',
                        'ca-green': '#4caf50',
                        'ca-dark': '#1a1a1a',
                        'ca-gray': '#2d3748',
                        'ca-light-gray': '#f7fafc',
                    }
                }
            }
        }
    </script>

    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #003d6b 0%, #0066cc 50%, #4a90e2 100%);
            color: white;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
            min-height: 100vh;
            position: relative;
        }

        /* Animated Background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
            animation: backgroundFloat 20s ease-in-out infinite;
            z-index: -1;
        }

        @keyframes backgroundFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-20px) rotate(1deg); }
            66% { transform: translateY(10px) rotate(-1deg); }
        }

        /* Floating Animation Elements */
        .floating-element {
            position: fixed;
            font-size: 2rem;
            opacity: 0.1;
            animation: float 15s ease-in-out infinite;
            z-index: -1;
            pointer-events: none;
        }

        .floating-element:nth-child(1) { top: 10%; left: 10%; animation-delay: 0s; }
        .floating-element:nth-child(2) { top: 20%; right: 15%; animation-delay: 3s; }
        .floating-element:nth-child(3) { top: 60%; left: 20%; animation-delay: 6s; }
        .floating-element:nth-child(4) { bottom: 20%; right: 25%; animation-delay: 9s; }
        .floating-element:nth-child(5) { top: 40%; left: 50%; animation-delay: 12s; }

        @keyframes float {
            0%, 100% { transform: translateY(0px) translateX(0px); }
            25% { transform: translateY(-20px) translateX(10px); }
            50% { transform: translateY(10px) translateX(-5px); }
            75% { transform: translateY(-15px) translateX(15px); }
        }

        .player-container {
            position: relative;
            width: 100%;
            height: 0;
            padding-bottom: 56.25%; /* 16:9 aspect ratio */
            background: #000;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .player-iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: none;
        }

        .live-indicator {
            animation: pulse 2s infinite;
        }

        .chat-container {
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .fullscreen-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            z-index: 10;
            transition: all 0.3s ease;
        }

        .fullscreen-btn:hover {
            background: rgba(0, 0, 0, 0.9);
            transform: scale(1.05);
        }
    </style>
</head>
<body>
    <script>
        // Language Management
        let currentLanguage = localStorage.getItem('language') || 'en';

        const translations = {
            en: {
                'nav.home': 'Home',
                'nav.tv': 'Online TV',
                'nav.gamezone': 'GameZone',
                'nav.radio': 'Online Radio',
                'common.watch_live': '📺 Watch Live'
            },
            es: {
                'nav.home': 'Inicio',
                'nav.tv': 'TV en Línea',
                'nav.gamezone': 'Zona de Juegos',
                'nav.radio': 'Radio en Línea',
                'common.watch_live': '📺 Ver en Vivo'
            }
        };

        function changeLanguage(lang) {
            currentLanguage = lang;
            localStorage.setItem('language', lang);
            document.getElementById('current-lang').textContent = lang.toUpperCase();

            // Update all translatable elements
            document.querySelectorAll('[data-translate]').forEach(element => {
                const key = element.getAttribute('data-translate');
                if (translations[lang] && translations[lang][key]) {
                    element.textContent = translations[lang][key];
                }
            });
        }

        // Initialize language on page load
        document.addEventListener('DOMContentLoaded', function() {
            changeLanguage(currentLanguage);
        });
    </script>

    <!-- Floating Animation Elements -->
    <div class="floating-element">🌊</div>
    <div class="floating-element">🏝️</div>
    <div class="floating-element">🎵</div>
    <div class="floating-element">📺</div>
    <div class="floating-element">🎮</div>

    <!-- Enhanced Navigation with Blue Theme -->
    <header class="bg-gradient-to-r from-ca-blue/95 to-ca-light-blue/95 backdrop-blur-md shadow-lg fixed w-full top-0 z-50 border-b border-white/20">
        <div class="container mx-auto px-4 py-3">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-r from-ca-light-blue to-ca-accent-blue rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold text-lg">CA</span>
                    </div>
                    <span class="font-bold text-xl text-white">Caribbean Advantage</span>
                </div>

                <nav class="hidden md:flex items-center space-x-8">
                    <a href="/" class="nav-link text-white/90 hover:text-white font-medium transition-colors" data-translate="nav.home">Home</a>
                    <a href="/live" class="nav-link active text-white font-medium hover:text-ca-yellow transition-colors" data-translate="nav.tv">Online TV</a>
                    <a href="/gamezone" class="nav-link text-white/90 hover:text-white font-medium transition-colors" data-translate="nav.gamezone">GameZone</a>
                    <a href="/radio" class="nav-link text-white/90 hover:text-white font-medium transition-colors" data-translate="nav.radio">Online Radio</a>
                </nav>

                <div class="flex items-center space-x-4">
                    <div class="live-indicator bg-red-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                        🔴 LIVE
                    </div>

                    <!-- Language Selector -->
                    <div class="dropdown-container relative">
                        <button class="flex items-center space-x-2 text-white/90 hover:text-white transition-colors">
                            <span class="text-lg">🌐</span>
                            <span id="current-lang" class="font-medium">EN</span>
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div class="dropdown absolute top-full right-0 mt-2 w-40 bg-white rounded-md shadow-lg py-1 z-10 opacity-0 visibility-hidden transform translateY(-10px) transition-all duration-300">
                            <a href="#" onclick="changeLanguage('en')" class="language-option flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-100">
                                🇺🇸 English
                            </a>
                            <a href="#" onclick="changeLanguage('es')" class="language-option flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-100">
                                🇪🇸 Español
                            </a>
                        </div>
                    </div>

                    <button id="mobile-menu-btn" class="md:hidden text-white hover:text-ca-yellow">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div id="mobile-menu" class="hidden md:hidden bg-ca-blue/95 backdrop-blur-md border-t border-white/20">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="/" class="block px-3 py-2 rounded-md text-base font-medium text-white/90 hover:text-white hover:bg-white/10" data-translate="nav.home">Home</a>
                <a href="/live" class="block px-3 py-2 rounded-md text-base font-medium text-white bg-white/10" data-translate="nav.tv">Online TV</a>
                <a href="/gamezone" class="block px-3 py-2 rounded-md text-base font-medium text-white/90 hover:text-white hover:bg-white/10" data-translate="nav.gamezone">GameZone</a>
                <a href="/radio" class="block px-3 py-2 rounded-md text-base font-medium text-white/90 hover:text-white hover:bg-white/10" data-translate="nav.radio">Online Radio</a>
            </div>
        </div>
    </header>

    <style>
        .dropdown-container:hover .dropdown {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
    </style>

    <!-- Main Content -->
    <main class="pt-20 min-h-screen">
        <div class="container mx-auto px-4 py-8">
            <!-- Live Stream Header -->
            <div class="text-center mb-8">
                <h1 class="text-4xl md:text-6xl font-bold mb-4">
                    Watch <span class="gradient-text">Channel 99-9</span>
                </h1>
                <p class="text-xl text-gray-300 mb-6">
                    Caribbean Advantage live streaming from North Coast Puerto Rico!
                </p>
                <div class="flex justify-center items-center space-x-4 mb-6">
                    <div class="live-indicator bg-red-500 text-white px-4 py-2 rounded-full font-bold">
                        🔴 LIVE NOW
                    </div>
                    <div class="bg-ca-blue text-white px-4 py-2 rounded-full font-bold">
                        📺 HD Quality
                    </div>
                    <div class="bg-ca-green text-white px-4 py-2 rounded-full font-bold">
                        🌐 24/7 Streaming
                    </div>
                </div>
            </div>

            <!-- Player Section -->
            <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
                <!-- Main Player -->
                <div class="lg:col-span-3">
                    <div class="player-container relative">
                        <!-- Loading Indicator -->
                        <div id="loading-indicator" class="absolute inset-0 flex items-center justify-center bg-black z-20">
                            <div class="text-center">
                                <div class="loader-spinner mx-auto mb-4"></div>
                                <p class="text-white">Loading Caribbean Advantage TV...</p>
                            </div>
                        </div>

                        <!-- Fullscreen Button -->
                        <button class="fullscreen-btn" onclick="toggleFullscreen()" title="Toggle Fullscreen">
                            ⛶
                        </button>

                        <!-- External Player Iframe -->
                        <iframe
                            id="tv-player"
                            class="player-iframe"
                            src="http://caribbeanadvantage.com/CA-tv.html"
                            allowfullscreen
                            allow="autoplay; fullscreen; picture-in-picture"
                            onload="hideLoading()"
                            onerror="showError()">
                        </iframe>

                        <!-- Error Message -->
                        <div id="error-message" class="absolute inset-0 flex items-center justify-center bg-black text-white text-center p-8" style="display: none;">
                            <div>
                                <h3 class="text-2xl font-bold mb-4">⚠️ Connection Error</h3>
                                <p class="mb-4">Unable to load the live stream. Please try refreshing the page.</p>
                                <button onclick="reloadPlayer()" class="btn-primary text-white px-6 py-3 rounded-lg font-bold">
                                    🔄 Retry
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Player Controls -->
                    <div class="mt-6 flex flex-wrap justify-center gap-4">
                        <button onclick="reloadPlayer()" class="bg-ca-blue hover:bg-ca-dark-blue text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            🔄 Refresh Stream
                        </button>
                        <button onclick="toggleFullscreen()" class="bg-ca-green hover:bg-green-600 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            ⛶ Fullscreen
                        </button>
                        <button onclick="openInNewTab()" class="bg-ca-orange hover:bg-orange-600 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                            🔗 Open in New Tab
                        </button>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="lg:col-span-1">
                    <!-- Now Playing -->
                    <div class="chat-container p-6 mb-6">
                        <h3 class="text-xl font-bold mb-4 text-ca-yellow">📺 Now Playing</h3>
                        <div class="space-y-3">
                            <div class="bg-white/10 p-3 rounded-lg">
                                <p class="font-bold text-sm">Caribbean Gaming Zone</p>
                                <p class="text-xs text-gray-300">6:00 PM - 8:00 PM</p>
                            </div>
                            <div class="bg-white/5 p-3 rounded-lg">
                                <p class="font-bold text-sm">Online Radio Mix</p>
                                <p class="text-xs text-gray-300">8:00 PM - 10:00 PM</p>
                            </div>
                            <div class="bg-white/5 p-3 rounded-lg">
                                <p class="font-bold text-sm">Local Artist Spotlight</p>
                                <p class="text-xs text-gray-300">10:00 PM - 11:00 PM</p>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Links -->
                    <div class="chat-container p-6 mb-6">
                        <h3 class="text-xl font-bold mb-4 text-ca-yellow">🔗 Quick Links</h3>
                        <div class="space-y-3">
                            <a href="/gamezone" class="block bg-ca-blue hover:bg-ca-dark-blue p-3 rounded-lg transition-colors">
                                🎮 GameZone
                            </a>
                            <a href="/radio" class="block bg-ca-orange hover:bg-orange-600 p-3 rounded-lg transition-colors">
                                📻 Online Radio
                            </a>
                            <a href="/events" class="block bg-ca-green hover:bg-green-600 p-3 rounded-lg transition-colors">
                                🎉 Events
                            </a>
                        </div>
                    </div>

                    <!-- Stream Info -->
                    <div class="chat-container p-6">
                        <h3 class="text-xl font-bold mb-4 text-ca-yellow">ℹ️ Stream Info</h3>
                        <div class="space-y-2 text-sm">
                            <div class="flex justify-between">
                                <span>Quality:</span>
                                <span class="text-ca-green">HD 1080p</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Status:</span>
                                <span class="text-red-400">🔴 Live</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Viewers:</span>
                                <span class="text-ca-yellow" id="viewer-count">1,247</span>
                            </div>
                            <div class="flex justify-between">
                                <span>Uptime:</span>
                                <span class="text-ca-blue" id="uptime">24/7</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-ca-dark text-white py-12 mt-16">
        <div class="container mx-auto px-4 text-center">
            <div class="flex items-center justify-center space-x-3 mb-4">
                <div class="w-12 h-12 bg-gradient-to-r from-ca-blue to-ca-orange rounded-lg flex items-center justify-center">
                    <span class="text-white font-bold text-xl">CA</span>
                </div>
                <div class="text-2xl font-bold">
                    <span class="text-ca-blue">Caribbean</span> <span class="text-ca-orange">Advantage</span>
                </div>
            </div>
            <p class="text-gray-300">Channel 99-9 - North Coast Puerto Rico</p>
            <p class="text-gray-400 text-sm mt-4">© 2023 Caribbean Advantage TV. All rights reserved.</p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="/js/main.js"></script>
    <script src="/js/live-player.js"></script>
</body>
</html>
