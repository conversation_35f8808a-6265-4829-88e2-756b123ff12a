<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Watch Live - Caribbean Advantage Channel 99-9</title>
    <meta name="description" content="Watch Caribbean Advantage Channel 99-9 live streaming 24/7. Gaming, Online Radio, and Live TV from North Coast Puerto Rico.">

    <!-- Stylesheets -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/css/styles.css">

    <!-- Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary-blue': '#1E90FF',
                        'primary-blue-dark': '#1873CC',
                        'primary-blue-light': '#4FA8FF',
                        'primary-blue-ultra-light': '#87CEEB',
                        'light-grey': '#F8F9FA',
                        'medium-grey': '#E9ECEF',
                        'dark-grey': '#6C757D',
                        'white': '#FFFFFF',
                        'black': '#212529',
                    }
                }
            }
        }
    </script>

    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #F8F9FA 0%, #E9ECEF 100%);
            color: #1a202c;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }

        /* High contrast text for accessibility */
        .text-contrast-dark {
            color: #1a202c !important;
        }

        .text-contrast-light {
            color: #ffffff !important;
        }

        .text-contrast-medium {
            color: #4a5568 !important;
        }

        .player-container {
            position: relative;
            width: 100%;
            height: 0;
            padding-bottom: 56.25%; /* 16:9 aspect ratio */
            background: linear-gradient(135deg, #1E90FF, #4FA8FF);
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 15px 50px rgba(30, 144, 255, 0.35);
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .player-iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 18px;
        }

        .live-indicator {
            animation: pulse 2s infinite;
            background: linear-gradient(135deg, #ff4757, #ff6b7a);
            box-shadow: 0 4px 20px rgba(255, 71, 87, 0.4);
        }

        .info-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            border: 1px solid rgba(30, 144, 255, 0.2);
            box-shadow: 0 8px 30px rgba(30, 144, 255, 0.15);
        }

        .fullscreen-btn {
            position: absolute;
            top: 15px;
            right: 15px;
            background: linear-gradient(135deg, #1E90FF, #4FA8FF);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 12px;
            cursor: pointer;
            z-index: 10;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(30, 144, 255, 0.3);
        }

        .fullscreen-btn:hover {
            background: linear-gradient(135deg, #1873CC, #1E90FF);
            transform: scale(1.05);
            box-shadow: 0 6px 20px rgba(30, 144, 255, 0.4);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="fixed top-0 left-0 right-0 z-50 glassmorphism shadow-lg border-b border-white/20">
        <div class="container mx-auto px-4 py-3">
            <div class="flex justify-between items-center">
                <div class="flex items-center">
                    <div class="flex items-center space-x-3">
                        <div class="w-12 h-12 bg-gradient-to-r from-primary-blue to-primary-blue-light rounded-lg flex items-center justify-center shadow-lg">
                            <span class="text-white font-bold text-xl">CA</span>
                        </div>
                        <div class="text-2xl font-bold">
                            <span class="text-primary-blue">Caribbean</span> <span class="text-primary-blue-light">Advantage</span>
                        </div>
                    </div>
                </div>

                <nav class="hidden md:flex space-x-8">
                    <a href="/" class="text-white hover:text-primary-blue-ultra-light transition-colors font-medium">Home</a>
                    <a href="/live" class="text-primary-blue-ultra-light font-bold">Online TV</a>
                    <a href="/gamezone" class="text-white hover:text-primary-blue-ultra-light transition-colors font-medium">GameZone</a>
                    <a href="/radio" class="text-white hover:text-primary-blue-ultra-light transition-colors font-medium">Online Radio</a>
                    <a href="/events" class="text-white hover:text-primary-blue-ultra-light transition-colors font-medium">Events</a>
                    <a href="/contact" class="text-white hover:text-primary-blue-ultra-light transition-colors font-medium">Contact</a>
                </nav>

                <div class="hidden md:flex items-center space-x-4">
                    <div class="live-indicator bg-red-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                        🔴 LIVE
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="pt-20 min-h-screen bg-light-grey">
        <div class="container mx-auto px-4 py-8">
            <!-- Live Stream Header -->
            <div class="text-center mb-12">
                <h1 class="text-4xl md:text-6xl font-bold mb-6 text-contrast-dark">
                    Watch <span class="gradient-text">Channel 99-9</span>
                </h1>
                <p class="text-xl text-contrast-medium mb-8 max-w-3xl mx-auto">
                    Caribbean Advantage live streaming from North Coast Puerto Rico! Experience gaming, radio, and local content in stunning HD quality.
                </p>
                <div class="flex flex-wrap justify-center items-center gap-4 mb-8">
                    <div class="live-indicator text-white px-6 py-3 rounded-full font-bold text-lg">
                        🔴 LIVE NOW
                    </div>
                    <div class="bg-gradient-to-r from-primary-blue to-primary-blue-light text-white px-6 py-3 rounded-full font-bold text-lg shadow-lg">
                        📺 HD Quality
                    </div>
                    <div class="bg-gradient-to-r from-primary-blue-light to-primary-blue-ultra-light text-white px-6 py-3 rounded-full font-bold text-lg shadow-lg">
                        🌐 24/7 Streaming
                    </div>
                    <div class="bg-gradient-to-r from-green-500 to-green-600 text-white px-6 py-3 rounded-full font-bold text-lg shadow-lg">
                        🎵 Local Artists
                    </div>
                </div>
            </div>

            <!-- Player Section -->
            <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
                <!-- Main Player -->
                <div class="lg:col-span-3">
                    <div class="player-container relative">
                        <!-- Loading Indicator -->
                        <div id="loading-indicator" class="absolute inset-0 flex items-center justify-center bg-black z-20">
                            <div class="text-center">
                                <div class="loader-spinner mx-auto mb-4"></div>
                                <p class="text-white">Loading Caribbean Advantage TV...</p>
                            </div>
                        </div>

                        <!-- Fullscreen Button -->
                        <button class="fullscreen-btn" onclick="toggleFullscreen()" title="Toggle Fullscreen">
                            ⛶
                        </button>

                        <!-- External Player Iframe -->
                        <iframe
                            id="tv-player"
                            class="player-iframe"
                            src="http://caribbeanadvantage.com/CA-tv.html"
                            allowfullscreen
                            allow="autoplay; fullscreen; picture-in-picture"
                            onload="hideLoading()"
                            onerror="showError()">
                        </iframe>

                        <!-- Error Message -->
                        <div id="error-message" class="absolute inset-0 flex items-center justify-center bg-black text-white text-center p-8" style="display: none;">
                            <div>
                                <h3 class="text-2xl font-bold mb-4">⚠️ Connection Error</h3>
                                <p class="mb-4">Unable to load the live stream. Please try refreshing the page.</p>
                                <button onclick="reloadPlayer()" class="btn-primary text-white px-6 py-3 rounded-lg font-bold">
                                    🔄 Retry
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Player Controls -->
                    <div class="mt-8 flex flex-wrap justify-center gap-4">
                        <button onclick="reloadPlayer()" class="bg-gradient-to-r from-primary-blue to-primary-blue-dark hover:from-primary-blue-dark hover:to-primary-blue text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                            🔄 Refresh Stream
                        </button>
                        <button onclick="toggleFullscreen()" class="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                            ⛶ Fullscreen
                        </button>
                        <button onclick="openInNewTab()" class="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                            🔗 Open in New Tab
                        </button>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="lg:col-span-1">
                    <!-- Now Playing -->
                    <div class="info-card p-6 mb-6">
                        <h3 class="text-xl font-bold mb-4 text-primary-blue">📺 Now Playing</h3>
                        <div class="space-y-3">
                            <div class="bg-gradient-to-r from-primary-blue/10 to-primary-blue-light/10 p-4 rounded-xl border border-primary-blue/20">
                                <p class="font-bold text-sm text-black">Caribbean Gaming Zone</p>
                                <p class="text-xs text-dark-grey">6:00 PM - 8:00 PM</p>
                                <div class="mt-2">
                                    <span class="bg-primary-blue text-white px-2 py-1 rounded-full text-xs">🎮 Gaming</span>
                                </div>
                            </div>
                            <div class="bg-gradient-to-r from-green-500/10 to-green-600/10 p-4 rounded-xl border border-green-500/20">
                                <p class="font-bold text-sm text-black">Online Radio Mix</p>
                                <p class="text-xs text-dark-grey">8:00 PM - 10:00 PM</p>
                                <div class="mt-2">
                                    <span class="bg-green-500 text-white px-2 py-1 rounded-full text-xs">📻 Radio</span>
                                </div>
                            </div>
                            <div class="bg-gradient-to-r from-purple-500/10 to-purple-600/10 p-4 rounded-xl border border-purple-500/20">
                                <p class="font-bold text-sm text-black">Local Artist Spotlight</p>
                                <p class="text-xs text-dark-grey">10:00 PM - 11:00 PM</p>
                                <div class="mt-2">
                                    <span class="bg-purple-500 text-white px-2 py-1 rounded-full text-xs">🎵 Artists</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Links -->
                    <div class="info-card p-6 mb-6">
                        <h3 class="text-xl font-bold mb-4 text-primary-blue">🔗 Quick Links</h3>
                        <div class="space-y-3">
                            <a href="/gamezone" class="block bg-gradient-to-r from-primary-blue to-primary-blue-light hover:from-primary-blue-dark hover:to-primary-blue text-white p-4 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg">
                                🎮 GameZone
                            </a>
                            <a href="/radio" class="block bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white p-4 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg">
                                📻 Online Radio
                            </a>
                            <a href="/events" class="block bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white p-4 rounded-xl transition-all duration-300 transform hover:scale-105 shadow-lg">
                                🎉 Events
                            </a>
                        </div>
                    </div>

                    <!-- Stream Info -->
                    <div class="info-card p-6">
                        <h3 class="text-xl font-bold mb-4 text-primary-blue">ℹ️ Stream Info</h3>
                        <div class="space-y-3 text-sm">
                            <div class="flex justify-between items-center p-3 bg-gradient-to-r from-light-grey to-medium-grey rounded-lg">
                                <span class="font-medium text-black">Quality:</span>
                                <span class="text-green-600 font-bold">HD 1080p</span>
                            </div>
                            <div class="flex justify-between items-center p-3 bg-gradient-to-r from-light-grey to-medium-grey rounded-lg">
                                <span class="font-medium text-black">Status:</span>
                                <span class="text-red-500 font-bold">🔴 Live</span>
                            </div>
                            <div class="flex justify-between items-center p-3 bg-gradient-to-r from-light-grey to-medium-grey rounded-lg">
                                <span class="font-medium text-black">Viewers:</span>
                                <span class="text-primary-blue font-bold" id="viewer-count">1,247</span>
                            </div>
                            <div class="flex justify-between items-center p-3 bg-gradient-to-r from-light-grey to-medium-grey rounded-lg">
                                <span class="font-medium text-black">Uptime:</span>
                                <span class="text-primary-blue-dark font-bold" id="uptime">24/7</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gradient-to-r from-primary-blue-dark to-primary-blue text-white py-12 mt-16">
        <div class="container mx-auto px-4 text-center">
            <div class="flex items-center justify-center space-x-3 mb-4">
                <div class="w-12 h-12 bg-gradient-to-r from-primary-blue-light to-primary-blue-ultra-light rounded-lg flex items-center justify-center shadow-lg">
                    <span class="text-white font-bold text-xl">CA</span>
                </div>
                <div class="text-2xl font-bold">
                    <span class="text-white">Caribbean</span> <span class="text-primary-blue-ultra-light">Advantage</span>
                </div>
            </div>
            <p class="text-primary-blue-ultra-light text-lg">Channel 99-9 - North Coast Puerto Rico</p>
            <p class="text-white/80 text-sm mt-4">© 2023 Caribbean Advantage TV. All rights reserved.</p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="/js/main.js"></script>
    <script src="/js/live-player.js"></script>
</body>
</html>
