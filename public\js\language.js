// Language Selector and Translation System
class LanguageManager {
    constructor() {
        this.currentLanguage = localStorage.getItem('language') || 'en';
        this.translations = {};
        this.init();
    }

    init() {
        this.loadTranslations();
        this.createLanguageSelector();
        this.applyTranslations();
    }

    loadTranslations() {
        this.translations = {
            en: {
                // Navigation
                'nav.home': 'Home',
                'nav.live': 'Live TV',
                'nav.gamezone': 'GameZone',
                'nav.radio': 'Radio',
                'nav.events': 'Events',
                'nav.contact': 'Contact',
                
                // Common
                'common.watch_live': 'Watch Live',
                'common.learn_more': 'Learn More',
                'common.get_started': 'Get Started',
                'common.contact_us': 'Contact Us',
                'common.follow_us': 'Follow Us',
                'common.read_more': 'Read More',
                'common.play_now': 'Play Now',
                'common.listen_live': 'Listen Live',
                'common.join_event': 'Join Event',
                'common.send_message': 'Send Message',
                
                // Homepage
                'home.hero.title': 'Modern Caribbean Entertainment',
                'home.hero.subtitle': 'Experience the vibrant culture and authentic Caribbean content on Channel 99-9, broadcasting live from North Coast Puerto Rico.',
                'home.features.title': 'What We Offer',
                'home.features.subtitle': 'Discover our comprehensive Caribbean entertainment platform.',
                'home.live_tv.title': 'Live Television',
                'home.live_tv.description': 'Watch high-quality live broadcasts featuring Caribbean news, entertainment, and cultural programming.',
                'home.radio.title': 'Caribbean Radio',
                'home.radio.description': '24/7 music streaming with local DJs, talk shows, and the best Caribbean sounds.',
                'home.gamezone.title': 'Interactive GameZone',
                'home.gamezone.description': 'Engage with our community through games, tournaments, and interactive entertainment.',
                
                // Live TV
                'live.hero.title': 'Live Caribbean Television',
                'live.hero.subtitle': 'Experience high-quality live broadcasting from Channel 99-9 with crystal-clear HD streaming and immersive Caribbean content.',
                'live.schedule.title': 'TV Schedule',
                'live.schedule.subtitle': 'Discover what\'s playing now and coming up next on Channel 99-9.',
                
                // GameZone
                'gamezone.hero.title': 'Caribbean GameZone',
                'gamezone.hero.subtitle': 'Enter the ultimate gaming experience with interactive tournaments, challenges, and community competitions on Channel 99-9.',
                'gamezone.tournament.title': 'Active Tournament',
                'gamezone.games.title': 'Featured Games',
                'gamezone.leaderboard.title': 'Top Players',
                
                // Radio
                'radio.hero.title': 'Caribbean Radio',
                'radio.hero.subtitle': 'Tune into the sounds of the Caribbean with 24/7 music, talk shows, and local artists broadcasting live from Channel 99-9.',
                'radio.schedule.title': 'Radio Schedule',
                'radio.djs.title': 'Meet Our DJs',
                'radio.playlist.title': 'Current Playlist',
                
                // Events
                'events.hero.title': 'Caribbean Events',
                'events.hero.subtitle': 'Join our vibrant Caribbean community for exciting events, shows, and cultural celebrations on Channel 99-9.',
                'events.upcoming.title': 'Upcoming Events',
                'events.featured.title': 'Featured Event',
                'events.calendar.title': 'Event Calendar',
                
                // Contact
                'contact.hero.title': 'Get in Touch',
                'contact.hero.subtitle': 'Connect with Caribbean Advantage TV Channel 99-9. We\'d love to hear from you about our programming, partnerships, or any questions you may have.',
                'contact.form.title': 'Send us a Message',
                'contact.info.title': 'Contact Information',
                'contact.social.title': 'Follow Us on Social Media',
                
                // Footer
                'footer.description': 'Modern Caribbean entertainment on Channel 99-9. Broadcasting live from North Coast Puerto Rico.',
                'footer.quick_links': 'Quick Links',
                'footer.contact': 'Contact',
                'footer.follow': 'Follow Us',
                'footer.copyright': '© 2023 Caribbean Advantage TV. All rights reserved.'
            },
            es: {
                // Navigation
                'nav.home': 'Inicio',
                'nav.live': 'TV en Vivo',
                'nav.gamezone': 'Zona de Juegos',
                'nav.radio': 'Radio',
                'nav.events': 'Eventos',
                'nav.contact': 'Contacto',
                
                // Common
                'common.watch_live': 'Ver en Vivo',
                'common.learn_more': 'Saber Más',
                'common.get_started': 'Comenzar',
                'common.contact_us': 'Contáctanos',
                'common.follow_us': 'Síguenos',
                'common.read_more': 'Leer Más',
                'common.play_now': 'Jugar Ahora',
                'common.listen_live': 'Escuchar en Vivo',
                'common.join_event': 'Unirse al Evento',
                'common.send_message': 'Enviar Mensaje',
                
                // Homepage
                'home.hero.title': 'Entretenimiento Caribeño Moderno',
                'home.hero.subtitle': 'Experimenta la cultura vibrante y el contenido caribeño auténtico en el Canal 99-9, transmitiendo en vivo desde la Costa Norte de Puerto Rico.',
                'home.features.title': 'Lo Que Ofrecemos',
                'home.features.subtitle': 'Descubre nuestra plataforma integral de entretenimiento caribeño.',
                'home.live_tv.title': 'Televisión en Vivo',
                'home.live_tv.description': 'Mira transmisiones en vivo de alta calidad con noticias caribeñas, entretenimiento y programación cultural.',
                'home.radio.title': 'Radio Caribeña',
                'home.radio.description': 'Transmisión de música 24/7 con DJs locales, programas de conversación y los mejores sonidos caribeños.',
                'home.gamezone.title': 'Zona de Juegos Interactiva',
                'home.gamezone.description': 'Participa con nuestra comunidad a través de juegos, torneos y entretenimiento interactivo.',
                
                // Live TV
                'live.hero.title': 'Televisión Caribeña en Vivo',
                'live.hero.subtitle': 'Experimenta transmisiones en vivo de alta calidad desde el Canal 99-9 con streaming HD cristalino y contenido caribeño inmersivo.',
                'live.schedule.title': 'Programación de TV',
                'live.schedule.subtitle': 'Descubre qué se está reproduciendo ahora y qué viene después en el Canal 99-9.',
                
                // GameZone
                'gamezone.hero.title': 'Zona de Juegos Caribeña',
                'gamezone.hero.subtitle': 'Entra en la experiencia de juego definitiva con torneos interactivos, desafíos y competencias comunitarias en el Canal 99-9.',
                'gamezone.tournament.title': 'Torneo Activo',
                'gamezone.games.title': 'Juegos Destacados',
                'gamezone.leaderboard.title': 'Mejores Jugadores',
                
                // Radio
                'radio.hero.title': 'Radio Caribeña',
                'radio.hero.subtitle': 'Sintoniza los sonidos del Caribe con música 24/7, programas de conversación y artistas locales transmitiendo en vivo desde el Canal 99-9.',
                'radio.schedule.title': 'Programación de Radio',
                'radio.djs.title': 'Conoce a Nuestros DJs',
                'radio.playlist.title': 'Lista de Reproducción Actual',
                
                // Events
                'events.hero.title': 'Eventos Caribeños',
                'events.hero.subtitle': 'Únete a nuestra vibrante comunidad caribeña para eventos emocionantes, espectáculos y celebraciones culturales en el Canal 99-9.',
                'events.upcoming.title': 'Próximos Eventos',
                'events.featured.title': 'Evento Destacado',
                'events.calendar.title': 'Calendario de Eventos',
                
                // Contact
                'contact.hero.title': 'Ponte en Contacto',
                'contact.hero.subtitle': 'Conéctate con Caribbean Advantage TV Canal 99-9. Nos encantaría saber de ti sobre nuestra programación, asociaciones o cualquier pregunta que puedas tener.',
                'contact.form.title': 'Envíanos un Mensaje',
                'contact.info.title': 'Información de Contacto',
                'contact.social.title': 'Síguenos en Redes Sociales',
                
                // Footer
                'footer.description': 'Entretenimiento caribeño moderno en el Canal 99-9. Transmitiendo en vivo desde la Costa Norte de Puerto Rico.',
                'footer.quick_links': 'Enlaces Rápidos',
                'footer.contact': 'Contacto',
                'footer.follow': 'Síguenos',
                'footer.copyright': '© 2023 Caribbean Advantage TV. Todos los derechos reservados.'
            }
        };
    }

    createLanguageSelector() {
        const navbar = document.querySelector('.nav-menu');
        if (!navbar) return;

        const languageSelector = document.createElement('div');
        languageSelector.className = 'language-selector';
        languageSelector.innerHTML = `
            <div class="language-toggle">
                <span class="globe-icon">🌐</span>
                <span class="current-lang">${this.currentLanguage.toUpperCase()}</span>
            </div>
            <div class="language-dropdown">
                <a href="#" class="language-option ${this.currentLanguage === 'en' ? 'active' : ''}" data-lang="en">
                    🇺🇸 English
                </a>
                <a href="#" class="language-option ${this.currentLanguage === 'es' ? 'active' : ''}" data-lang="es">
                    🇪🇸 Español
                </a>
            </div>
        `;

        navbar.appendChild(languageSelector);

        // Add event listeners
        const languageOptions = languageSelector.querySelectorAll('.language-option');
        languageOptions.forEach(option => {
            option.addEventListener('click', (e) => {
                e.preventDefault();
                const lang = option.getAttribute('data-lang');
                this.changeLanguage(lang);
            });
        });
    }

    changeLanguage(lang) {
        if (lang === this.currentLanguage) return;

        this.currentLanguage = lang;
        localStorage.setItem('language', lang);
        
        // Update active state
        document.querySelectorAll('.language-option').forEach(option => {
            option.classList.toggle('active', option.getAttribute('data-lang') === lang);
        });
        
        // Update current language display
        const currentLangDisplay = document.querySelector('.current-lang');
        if (currentLangDisplay) {
            currentLangDisplay.textContent = lang.toUpperCase();
        }

        this.applyTranslations();
    }

    applyTranslations() {
        const elements = document.querySelectorAll('[data-translate]');
        elements.forEach(element => {
            const key = element.getAttribute('data-translate');
            const translation = this.getTranslation(key);
            if (translation) {
                element.textContent = translation;
            }
        });
    }

    getTranslation(key) {
        const keys = key.split('.');
        let translation = this.translations[this.currentLanguage];
        
        for (const k of keys) {
            if (translation && translation[k]) {
                translation = translation[k];
            } else {
                return this.translations.en[key] || key;
            }
        }
        
        return translation || key;
    }
}

// Initialize language manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.languageManager = new LanguageManager();
});
