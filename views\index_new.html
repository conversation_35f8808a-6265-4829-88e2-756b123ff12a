<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Caribbean Advantage - CAtv Channel 99-9 | Gaming, Radio & Live TV</title>
    <meta name="description" content="Your premier destination for Gaming, Online Radio, and Live TV covering North Coast Puerto Rico. Supporting local artists and bringing you the best Caribbean entertainment 24/7.">

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <style>
        /* Ultra-Professional Blue Design System */
        :root {
            --primary-blue: #0066cc;
            --deep-blue: #003d7a;
            --light-blue: #4da6ff;
            --accent-blue: #1a8cff;
            --dark-navy: #001a33;
            --steel-blue: #2c5aa0;
            --powder-blue: #e6f3ff;
            --mint-blue: #00ccff;
            --coral-blue: #0080ff;
            --gradient-primary: linear-gradient(135deg, #003d7a 0%, #0066cc 50%, #1a8cff 100%);
            --gradient-secondary: linear-gradient(135deg, #001a33 0%, #2c5aa0 50%, #4da6ff 100%);
            --gradient-accent: linear-gradient(135deg, #0066cc 0%, #00ccff 100%);
            --glass-effect: rgba(255, 255, 255, 0.1);
            --glass-border: rgba(255, 255, 255, 0.2);
            --shadow-glow: 0 0 30px rgba(0, 102, 204, 0.3);
            --shadow-strong: 0 20px 60px rgba(0, 26, 51, 0.4);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: #1e293b;
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* Loading Screen */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--gradient-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            transition: opacity 0.8s ease, visibility 0.8s ease;
        }

        .loading-screen.hidden {
            opacity: 0;
            visibility: hidden;
        }

        .loading-content {
            text-align: center;
            color: white;
        }

        .loading-logo {
            font-size: 4rem;
            font-weight: 900;
            margin-bottom: 1rem;
            animation: pulse 2s infinite;
            text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .loading-text {
            font-size: 1.2rem;
            opacity: 0.9;
            animation: fadeInOut 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.05); opacity: 0.8; }
        }

        @keyframes fadeInOut {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }

        @keyframes slideInFromTop {
            from { transform: translateY(-100%); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        @keyframes fadeInUp {
            from { transform: translateY(30px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        @keyframes scaleIn {
            from { transform: scale(0.9); opacity: 0; }
            to { transform: scale(1); opacity: 1; }
        }

        @keyframes glow {
            0%, 100% { box-shadow: 0 4px 15px rgba(0, 204, 255, 0.4); }
            50% { box-shadow: 0 4px 25px rgba(0, 204, 255, 0.8); }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        /* Enhanced Navigation */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(0, 26, 51, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--glass-border);
            z-index: 1000;
            transition: all 0.3s ease;
            padding: 0.75rem 0;
            animation: slideInFromTop 0.8s ease;
        }

        .navbar.scrolled {
            background: rgba(0, 26, 51, 0.98);
            box-shadow: var(--shadow-glow);
            padding: 0.5rem 0;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .nav-logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            text-decoration: none;
            color: white;
            font-weight: 700;
            font-size: 1.25rem;
            transition: all 0.3s ease;
            animation: scaleIn 0.8s ease 0.2s both;
        }

        .nav-logo:hover {
            transform: scale(1.05);
            color: var(--light-blue);
        }

        .nav-logo-icon {
            width: 45px;
            height: 45px;
            background: var(--gradient-accent);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 900;
            font-size: 1.3rem;
            color: white;
            box-shadow: 0 4px 15px rgba(0, 204, 255, 0.4);
            animation: glow 3s ease-in-out infinite;
        }

        .nav-menu {
            display: flex;
            align-items: center;
            gap: 2rem;
            list-style: none;
        }

        .nav-menu li {
            animation: fadeInUp 0.8s ease both;
        }

        .nav-menu li:nth-child(1) { animation-delay: 0.3s; }
        .nav-menu li:nth-child(2) { animation-delay: 0.4s; }
        .nav-menu li:nth-child(3) { animation-delay: 0.5s; }
        .nav-menu li:nth-child(4) { animation-delay: 0.6s; }
        .nav-menu li:nth-child(5) { animation-delay: 0.7s; }
        .nav-menu li:nth-child(6) { animation-delay: 0.8s; }

        .nav-link {
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            font-weight: 500;
            padding: 0.75rem 1.25rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--glass-effect);
            transition: left 0.3s ease;
            z-index: -1;
        }

        .nav-link:hover::before,
        .nav-link.active::before {
            left: 0;
        }

        .nav-link:hover,
        .nav-link.active {
            color: white;
            backdrop-filter: blur(10px);
            border: 1px solid var(--glass-border);
            transform: translateY(-2px);
        }

        .watch-live-btn {
            background: var(--gradient-accent);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(26, 140, 255, 0.4);
            animation: pulse 3s infinite, fadeInUp 0.8s ease 0.9s both;
            position: relative;
            overflow: hidden;
        }

        .watch-live-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.6s ease;
        }

        .watch-live-btn:hover::before {
            left: 100%;
        }

        .watch-live-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(26, 140, 255, 0.6);
        }

        /* Language Selector */
        .language-selector {
            position: relative;
            margin-left: 1rem;
            animation: fadeInUp 0.8s ease 1s both;
        }

        .language-toggle {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: var(--glass-effect);
            backdrop-filter: blur(10px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            padding: 0.5rem 1rem;
            color: white;
            text-decoration: none;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .language-toggle:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 102, 204, 0.3);
        }

        .language-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            background: rgba(0, 26, 51, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 10px;
            border: 1px solid var(--glass-border);
            min-width: 140px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            z-index: 1000;
            margin-top: 0.5rem;
            box-shadow: var(--shadow-strong);
        }

        .language-selector:hover .language-dropdown {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .language-option {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .language-option:hover {
            background: var(--glass-effect);
            color: var(--light-blue);
        }

        .language-option.active {
            background: var(--gradient-accent);
            color: white;
        }

        /* Mobile Navigation */
        .nav-toggle {
            display: none;
            flex-direction: column;
            gap: 4px;
            background: none;
            border: none;
            cursor: pointer;
            padding: 0.5rem;
            animation: fadeInUp 0.8s ease 1.1s both;
        }

        .nav-toggle span {
            width: 25px;
            height: 3px;
            background: white;
            border-radius: 2px;
            transition: all 0.3s ease;
        }

        .nav-toggle.active span:nth-child(1) {
            transform: rotate(45deg) translate(5px, 5px);
        }

        .nav-toggle.active span:nth-child(2) {
            opacity: 0;
        }

        .nav-toggle.active span:nth-child(3) {
            transform: rotate(-45deg) translate(7px, -6px);
        }

        @media (max-width: 768px) {
            .nav-menu {
                position: fixed;
                top: 100%;
                left: 0;
                right: 0;
                background: rgba(0, 26, 51, 0.98);
                backdrop-filter: blur(20px);
                flex-direction: column;
                padding: 2rem;
                gap: 1rem;
                transform: translateY(-100%);
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
                border-top: 1px solid var(--glass-border);
            }

            .nav-menu.active {
                transform: translateY(0);
                opacity: 1;
                visibility: visible;
            }

            .nav-toggle {
                display: flex;
            }

            .watch-live-btn {
                margin-top: 1rem;
            }

            .language-selector {
                margin-left: 0;
                margin-top: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div class="loading-screen" id="loadingScreen">
        <div class="loading-content">
            <div class="loading-logo">CA</div>
            <div class="loading-text" data-translate="loading.text">Loading your entertainment...</div>
        </div>
    </div>

    <!-- Ultra-Professional Navigation -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <a href="/" class="nav-logo">
                <div class="nav-logo-icon">CA</div>
                <span data-translate="nav.brand">Caribbean Advantage</span>
            </a>

            <ul class="nav-menu" id="navMenu">
                <li><a href="/" class="nav-link active" data-translate="nav.home">Home</a></li>
                <li><a href="/live" class="nav-link" data-translate="nav.live">Online TV</a></li>
                <li><a href="/gamezone" class="nav-link" data-translate="nav.gamezone">GameZone</a></li>
                <li><a href="/radio" class="nav-link" data-translate="nav.radio">Online Radio</a></li>
                <li><a href="/events" class="nav-link" data-translate="nav.events">Events</a></li>
                <li><a href="/contact" class="nav-link" data-translate="nav.contact">Contact</a></li>
            </ul>

            <div style="display: flex; align-items: center;">
                <a href="/live" class="watch-live-btn" data-translate="common.watch_live">📺 Watch Live</a>

                <!-- Language Selector -->
                <div class="language-selector">
                    <div class="language-toggle" id="languageToggle">
                        <span>🌐</span>
                        <span id="currentLang">EN</span>
                    </div>
                    <div class="language-dropdown" id="languageDropdown">
                        <a href="#" class="language-option active" data-lang="en">
                            🇺🇸 English
                        </a>
                        <a href="#" class="language-option" data-lang="es">
                            🇪🇸 Español
                        </a>
                    </div>
                </div>

                <button class="nav-toggle" id="navToggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </div>
    </nav>

    <!-- Now Playing Bar -->
    <div class="now-playing-bar" id="nowPlayingBar">
        <div style="max-width: 1200px; margin: 0 auto; padding: 0 2rem;">
            <div style="display: flex; align-items: center; justify-content: space-between; padding: 1rem 0;">
                <div style="display: flex; align-items: center; gap: 1rem;">
                    <div style="display: flex; align-items: center;">
                        <div style="width: 8px; height: 8px; background: white; border-radius: 50%; margin-right: 0.5rem; animation: pulse 2s infinite;"></div>
                        <span style="font-size: 0.8rem; font-weight: 600; text-transform: uppercase;" data-translate="live.now">Live Now</span>
                    </div>
                    <div>
                        <h4 style="font-weight: 600; margin: 0;" data-translate="live.show">Channel 99-9</h4>
                        <p style="font-size: 0.8rem; opacity: 0.8; margin: 0;" data-translate="live.location">North Coast PR</p>
                    </div>
                </div>

                <div style="display: flex; align-items: center; gap: 1rem;">
                    <button style="padding: 0.5rem; border-radius: 50%; background: rgba(255,255,255,0.1); border: none; color: white; cursor: pointer; transition: all 0.3s ease;">
                        ▶️
                    </button>

                    <button id="closeNowPlaying" style="padding: 0.5rem; border-radius: 50%; background: rgba(255,255,255,0.1); border: none; color: white; cursor: pointer; transition: all 0.3s ease;">
                        ✕
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main style="padding-top: 5rem;">
        <!-- Hero Section -->
        <section style="min-height: 80vh; background: var(--gradient-primary); position: relative; overflow: hidden; display: flex; align-items: center;">
            <!-- Animated Background Elements -->
            <div style="position: absolute; top: 20%; left: 10%; font-size: 3rem; opacity: 0.1; animation: float 6s ease-in-out infinite;">🌊</div>
            <div style="position: absolute; top: 60%; right: 15%; font-size: 2.5rem; opacity: 0.1; animation: float 8s ease-in-out infinite 2s;">🏝️</div>
            <div style="position: absolute; top: 30%; right: 25%; font-size: 2rem; opacity: 0.1; animation: float 7s ease-in-out infinite 1s;">🎵</div>
            <div style="position: absolute; bottom: 20%; left: 20%; font-size: 2.5rem; opacity: 0.1; animation: float 9s ease-in-out infinite 3s;">📺</div>

            <div style="max-width: 1200px; margin: 0 auto; padding: 0 2rem; position: relative; z-index: 2;">
                <div style="max-width: 700px; color: white;">
                    <h1 style="font-size: 3.5rem; font-weight: 900; margin-bottom: 1.5rem; line-height: 1.1; text-shadow: 0 4px 20px rgba(0,0,0,0.3); animation: fadeInUp 1s ease 0.5s both;" data-translate="hero.title">
                        Caribbean Advantage<br>
                        <span style="color: var(--mint-blue);">Channel 99-9</span>
                    </h1>
                    <p style="font-size: 1.3rem; margin-bottom: 2rem; opacity: 0.95; line-height: 1.6; animation: fadeInUp 1s ease 0.7s both;" data-translate="hero.subtitle">
                        Your premier destination for <strong>Gaming</strong>, <strong>Online Radio</strong>, and <strong>Live TV</strong> covering North Coast Puerto Rico. Supporting local artists and bringing you the best Caribbean entertainment 24/7.
                    </p>

                    <div style="display: flex; gap: 1rem; flex-wrap: wrap; animation: fadeInUp 1s ease 0.9s both;">
                        <a href="/live" style="background: white; color: var(--deep-blue); padding: 1rem 2rem; border-radius: 30px; text-decoration: none; font-weight: 700; display: flex; align-items: center; gap: 0.5rem; transition: all 0.3s ease; box-shadow: 0 8px 25px rgba(0,0,0,0.2);" onmouseover="this.style.transform='translateY(-3px)'; this.style.boxShadow='0 12px 35px rgba(0,0,0,0.3)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 8px 25px rgba(0,0,0,0.2)'" data-translate="hero.watch_now">
                            🎬 <span data-translate="common.watch_now">Watch Now</span>
                        </a>

                        <a href="/radio" style="background: rgba(255,255,255,0.1); color: white; padding: 1rem 2rem; border-radius: 30px; text-decoration: none; font-weight: 600; display: flex; align-items: center; gap: 0.5rem; transition: all 0.3s ease; backdrop-filter: blur(10px); border: 1px solid rgba(255,255,255,0.2);" onmouseover="this.style.transform='translateY(-3px)'; this.style.background='rgba(255,255,255,0.2)'" onmouseout="this.style.transform='translateY(0)'; this.style.background='rgba(255,255,255,0.1)'" data-translate="hero.online_radio">
                            📻 <span data-translate="nav.radio">Online Radio</span>
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- Live Status Section -->
        <section style="background: var(--gradient-secondary); padding: 3rem 0; position: relative;">
            <div style="max-width: 1200px; margin: 0 auto; padding: 0 2rem;">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem; text-align: center;">
                    <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(20px); border-radius: 20px; padding: 2rem; border: 1px solid rgba(255,255,255,0.2); animation: scaleIn 0.8s ease 0.2s both;">
                        <div style="font-size: 3rem; margin-bottom: 1rem;">📺</div>
                        <h3 style="color: white; font-size: 1.5rem; font-weight: 700; margin-bottom: 0.5rem;" data-translate="status.channel">Channel 99-9</h3>
                        <p style="color: rgba(255,255,255,0.8);" data-translate="status.location">North Coast Puerto Rico</p>
                        <div style="display: flex; align-items: center; justify-content: center; gap: 0.5rem; margin-top: 1rem;">
                            <div style="width: 8px; height: 8px; background: #00ff88; border-radius: 50%; animation: pulse 2s infinite;"></div>
                            <span style="color: #00ff88; font-weight: 600; font-size: 0.9rem;" data-translate="status.live">🔴 LIVE</span>
                        </div>
                    </div>

                    <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(20px); border-radius: 20px; padding: 2rem; border: 1px solid rgba(255,255,255,0.2); animation: scaleIn 0.8s ease 0.4s both;">
                        <div style="font-size: 3rem; margin-bottom: 1rem;">📻</div>
                        <h3 style="color: white; font-size: 1.5rem; font-weight: 700; margin-bottom: 0.5rem;" data-translate="status.radio">Caribbean TV</h3>
                        <p style="color: rgba(255,255,255,0.8);" data-translate="status.quality">HD</p>
                        <div style="margin-top: 1rem;">
                            <span style="color: var(--mint-blue); font-weight: 600;" data-translate="status.features">🎮 Gaming • 📻 Radio • 📺 TV</span>
                        </div>
                    </div>

                    <div style="background: rgba(255,255,255,0.1); backdrop-filter: blur(20px); border-radius: 20px; padding: 2rem; border: 1px solid rgba(255,255,255,0.2); animation: scaleIn 0.8s ease 0.6s both;">
                        <div style="font-size: 3rem; margin-bottom: 1rem;">🎵</div>
                        <h3 style="color: white; font-size: 1.5rem; font-weight: 700; margin-bottom: 0.5rem;" data-translate="status.artists">Supporting Local Artists</h3>
                        <p style="color: rgba(255,255,255,0.8);" data-translate="status.community">Our Content</p>
                        <div style="margin-top: 1rem;">
                            <a href="/events" style="color: var(--mint-blue); text-decoration: none; font-weight: 600;" data-translate="status.learn_more">Learn More →</a>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Our Content Section -->
        <section style="padding: 4rem 0; background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);">
            <div style="max-width: 1200px; margin: 0 auto; padding: 0 2rem;">
                <div style="text-align: center; margin-bottom: 3rem;">
                    <h2 style="font-size: 2.5rem; font-weight: 800; color: var(--deep-blue); margin-bottom: 1rem; animation: fadeInUp 0.8s ease;" data-translate="content.title">Our Content</h2>
                    <p style="font-size: 1.1rem; color: #64748b; max-width: 600px; margin: 0 auto; animation: fadeInUp 0.8s ease 0.2s both;" data-translate="content.subtitle">Experience gaming, online radio, live TV, and support for local Puerto Rican artists on Channel 99-9.</p>
                </div>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 2rem;">
                    <!-- Gaming Card -->
                    <div style="background: white; border-radius: 20px; padding: 2rem; box-shadow: 0 10px 30px rgba(0,0,0,0.1); transition: all 0.3s ease; border: 1px solid #e2e8f0; animation: fadeInUp 0.8s ease 0.3s both;" onmouseover="this.style.transform='translateY(-10px)'; this.style.boxShadow='0 20px 50px rgba(0,102,204,0.2)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 30px rgba(0,0,0,0.1)'">
                        <div style="width: 60px; height: 60px; background: var(--gradient-accent); border-radius: 15px; display: flex; align-items: center; justify-content: center; margin-bottom: 1.5rem; animation: glow 3s ease-in-out infinite;">
                            <span style="font-size: 1.8rem;">🎮</span>
                        </div>
                        <h3 style="font-size: 1.3rem; font-weight: 700; color: var(--deep-blue); margin-bottom: 1rem;" data-translate="content.gaming.title">Gaming</h3>
                        <h4 style="font-size: 1.1rem; font-weight: 600; color: var(--steel-blue); margin-bottom: 0.5rem;" data-translate="content.gaming.subtitle">GameZone</h4>
                        <p style="color: #64748b; margin-bottom: 1.5rem; line-height: 1.6;" data-translate="content.gaming.description">Live gaming streams, tournaments, and gaming content from Puerto Rico.</p>
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <span style="color: var(--accent-blue); font-weight: 600; font-size: 0.9rem;" data-translate="content.gaming.status">🎮 Live Gaming</span>
                            <a href="/gamezone" style="background: var(--gradient-accent); color: white; padding: 0.5rem 1rem; border-radius: 20px; text-decoration: none; font-weight: 600; font-size: 0.9rem; transition: all 0.3s ease;" onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'" data-translate="content.gaming.action">Play Now →</a>
                        </div>
                    </div>

                    <!-- Online Radio Card -->
                    <div style="background: white; border-radius: 20px; padding: 2rem; box-shadow: 0 10px 30px rgba(0,0,0,0.1); transition: all 0.3s ease; border: 1px solid #e2e8f0; animation: fadeInUp 0.8s ease 0.5s both;" onmouseover="this.style.transform='translateY(-10px)'; this.style.boxShadow='0 20px 50px rgba(0,102,204,0.2)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 30px rgba(0,0,0,0.1)'">
                        <div style="width: 60px; height: 60px; background: var(--gradient-accent); border-radius: 15px; display: flex; align-items: center; justify-content: center; margin-bottom: 1.5rem; animation: glow 3s ease-in-out infinite 1s;">
                            <span style="font-size: 1.8rem;">📻</span>
                        </div>
                        <h3 style="font-size: 1.3rem; font-weight: 700; color: var(--deep-blue); margin-bottom: 1rem;" data-translate="content.radio.title">Online Radio</h3>
                        <h4 style="font-size: 1.1rem; font-weight: 600; color: var(--steel-blue); margin-bottom: 0.5rem;" data-translate="content.radio.subtitle">Caribbean Sounds</h4>
                        <p style="color: #64748b; margin-bottom: 1.5rem; line-height: 1.6;" data-translate="content.radio.description">24/7 Caribbean music, local DJs, and cultural programming.</p>
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <span style="color: var(--accent-blue); font-weight: 600; font-size: 0.9rem;" data-translate="content.radio.status">📻 Live Radio</span>
                            <a href="/radio" style="background: var(--gradient-accent); color: white; padding: 0.5rem 1rem; border-radius: 20px; text-decoration: none; font-weight: 600; font-size: 0.9rem; transition: all 0.3s ease;" onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'" data-translate="content.radio.action">Listen Now →</a>
                        </div>
                    </div>

                    <!-- Live TV Card -->
                    <div style="background: white; border-radius: 20px; padding: 2rem; box-shadow: 0 10px 30px rgba(0,0,0,0.1); transition: all 0.3s ease; border: 1px solid #e2e8f0; animation: fadeInUp 0.8s ease 0.7s both;" onmouseover="this.style.transform='translateY(-10px)'; this.style.boxShadow='0 20px 50px rgba(0,102,204,0.2)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 30px rgba(0,0,0,0.1)'">
                        <div style="width: 60px; height: 60px; background: var(--gradient-accent); border-radius: 15px; display: flex; align-items: center; justify-content: center; margin-bottom: 1.5rem; animation: glow 3s ease-in-out infinite 2s;">
                            <span style="font-size: 1.8rem;">📺</span>
                        </div>
                        <h3 style="font-size: 1.3rem; font-weight: 700; color: var(--deep-blue); margin-bottom: 1rem;" data-translate="content.tv.title">Live TV</h3>
                        <h4 style="font-size: 1.1rem; font-weight: 600; color: var(--steel-blue); margin-bottom: 0.5rem;" data-translate="content.tv.subtitle">Channel 99-9</h4>
                        <p style="color: #64748b; margin-bottom: 1.5rem; line-height: 1.6;" data-translate="content.tv.description">Live Caribbean television with news, entertainment, and cultural shows.</p>
                        <div style="display: flex; align-items: center; justify-content: space-between;">
                            <span style="color: var(--accent-blue); font-weight: 600; font-size: 0.9rem;" data-translate="content.tv.status">📺 Live TV</span>
                            <a href="/live" style="background: var(--gradient-accent); color: white; padding: 0.5rem 1rem; border-radius: 20px; text-decoration: none; font-weight: 600; font-size: 0.9rem; transition: all 0.3s ease;" onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'" data-translate="content.tv.action">Watch Live →</a>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Footer -->
        <footer style="background: var(--gradient-primary); color: white; padding: 3rem 0 2rem;">
            <div style="max-width: 1200px; margin: 0 auto; padding: 0 2rem;">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem; margin-bottom: 2rem;">
                    <div>
                        <div style="display: flex; align-items: center; gap: 0.75rem; margin-bottom: 1rem;">
                            <div style="width: 40px; height: 40px; background: var(--gradient-accent); border-radius: 8px; display: flex; align-items: center; justify-content: center; font-weight: 900; font-size: 1.2rem;">CA</div>
                            <span style="font-weight: 700; font-size: 1.2rem;">Caribbean Advantage</span>
                        </div>
                        <p style="opacity: 0.9; line-height: 1.6;" data-translate="footer.description">Your premier destination for Gaming, Online Radio, and Live TV covering North Coast Puerto Rico.</p>
                    </div>

                    <div>
                        <h4 style="font-weight: 700; margin-bottom: 1rem;" data-translate="footer.quick_links">Quick Links</h4>
                        <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                            <a href="/live" style="color: rgba(255,255,255,0.8); text-decoration: none; transition: color 0.3s ease;" onmouseover="this.style.color='white'" onmouseout="this.style.color='rgba(255,255,255,0.8)'" data-translate="nav.live">Live TV</a>
                            <a href="/radio" style="color: rgba(255,255,255,0.8); text-decoration: none; transition: color 0.3s ease;" onmouseover="this.style.color='white'" onmouseout="this.style.color='rgba(255,255,255,0.8)'" data-translate="nav.radio">Radio</a>
                            <a href="/gamezone" style="color: rgba(255,255,255,0.8); text-decoration: none; transition: color 0.3s ease;" onmouseover="this.style.color='white'" onmouseout="this.style.color='rgba(255,255,255,0.8)'" data-translate="nav.gamezone">GameZone</a>
                            <a href="/events" style="color: rgba(255,255,255,0.8); text-decoration: none; transition: color 0.3s ease;" onmouseover="this.style.color='white'" onmouseout="this.style.color='rgba(255,255,255,0.8)'" data-translate="nav.events">Events</a>
                        </div>
                    </div>

                    <div>
                        <h4 style="font-weight: 700; margin-bottom: 1rem;" data-translate="footer.contact">Contact</h4>
                        <div style="display: flex; flex-direction: column; gap: 0.5rem; opacity: 0.9;">
                            <p data-translate="footer.channel">Channel 99-9</p>
                            <p data-translate="footer.location">North Coast Puerto Rico</p>
                            <p><EMAIL></p>
                        </div>
                    </div>

                    <div>
                        <h4 style="font-weight: 700; margin-bottom: 1rem;" data-translate="footer.follow">Follow Us</h4>
                        <div style="display: flex; gap: 1rem;">
                            <a href="#" style="width: 40px; height: 40px; background: rgba(255,255,255,0.1); border-radius: 8px; display: flex; align-items: center; justify-content: center; text-decoration: none; transition: all 0.3s ease;" onmouseover="this.style.background='rgba(255,255,255,0.2)'; this.style.transform='translateY(-2px)'" onmouseout="this.style.background='rgba(255,255,255,0.1)'; this.style.transform='translateY(0)'">📘</a>
                            <a href="#" style="width: 40px; height: 40px; background: rgba(255,255,255,0.1); border-radius: 8px; display: flex; align-items: center; justify-content: center; text-decoration: none; transition: all 0.3s ease;" onmouseover="this.style.background='rgba(255,255,255,0.2)'; this.style.transform='translateY(-2px)'" onmouseout="this.style.background='rgba(255,255,255,0.1)'; this.style.transform='translateY(0)'">🐦</a>
                            <a href="#" style="width: 40px; height: 40px; background: rgba(255,255,255,0.1); border-radius: 8px; display: flex; align-items: center; justify-content: center; text-decoration: none; transition: all 0.3s ease;" onmouseover="this.style.background='rgba(255,255,255,0.2)'; this.style.transform='translateY(-2px)'" onmouseout="this.style.background='rgba(255,255,255,0.1)'; this.style.transform='translateY(0)'">📷</a>
                            <a href="#" style="width: 40px; height: 40px; background: rgba(255,255,255,0.1); border-radius: 8px; display: flex; align-items: center; justify-content: center; text-decoration: none; transition: all 0.3s ease;" onmouseover="this.style.background='rgba(255,255,255,0.2)'; this.style.transform='translateY(-2px)'" onmouseout="this.style.background='rgba(255,255,255,0.1)'; this.style.transform='translateY(0)'">📺</a>
                        </div>
                    </div>
                </div>

                <div style="border-top: 1px solid rgba(255,255,255,0.2); padding-top: 2rem; text-align: center;">
                    <p style="opacity: 0.8;" data-translate="footer.copyright">&copy; 2023 Caribbean Advantage TV. All rights reserved.</p>
                </div>
            </div>
        </footer>
    </main>

    <!-- Now Playing Bar Styles -->
    <style>
        .now-playing-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: var(--gradient-primary);
            color: white;
            z-index: 999;
            transform: translateY(100%);
            transition: transform 0.3s ease;
            border-top: 1px solid rgba(255,255,255,0.2);
        }

        .now-playing-bar.show {
            transform: translateY(0);
        }

        @media (max-width: 768px) {
            .now-playing-bar {
                padding: 0.5rem 0;
            }

            .now-playing-bar > div > div {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }
        }
    </style>

    <!-- Enhanced Language Manager -->
    <script>
        // Enhanced Language Manager with Blue Theme Translations
        class LanguageManager {
            constructor() {
                this.currentLanguage = localStorage.getItem('language') || 'en';
                this.translations = {};
                this.init();
            }

            init() {
                this.loadTranslations();
                this.setupLanguageSelector();
                this.applyTranslations();
            }

            loadTranslations() {
                this.translations = {
                    en: {
                        // Loading
                        'loading.text': 'Loading your entertainment...',

                        // Navigation
                        'nav.brand': 'Caribbean Advantage',
                        'nav.home': 'Home',
                        'nav.live': 'Online TV',
                        'nav.gamezone': 'GameZone',
                        'nav.radio': 'Online Radio',
                        'nav.events': 'Events',
                        'nav.contact': 'Contact',

                        // Common
                        'common.watch_live': '📺 Watch Live',
                        'common.watch_now': 'Watch Now',

                        // Hero
                        'hero.title': 'Caribbean Advantage\nChannel 99-9',
                        'hero.subtitle': 'Your premier destination for Gaming, Online Radio, and Live TV covering North Coast Puerto Rico. Supporting local artists and bringing you the best Caribbean entertainment 24/7.',
                        'hero.watch_now': '🎬 Watch Now',
                        'hero.online_radio': '📻 Online Radio',

                        // Live Status
                        'live.now': 'Live Now',
                        'live.show': 'Channel 99-9',
                        'live.location': 'North Coast PR',

                        // Status Section
                        'status.channel': 'Channel 99-9',
                        'status.location': 'North Coast Puerto Rico',
                        'status.live': '🔴 LIVE',
                        'status.radio': 'Caribbean TV',
                        'status.quality': 'HD',
                        'status.features': '🎮 Gaming • 📻 Radio • 📺 TV',
                        'status.artists': 'Supporting Local Artists',
                        'status.community': 'Our Content',
                        'status.learn_more': 'Learn More →',

                        // Content Section
                        'content.title': 'Our Content',
                        'content.subtitle': 'Experience gaming, online radio, live TV, and support for local Puerto Rican artists on Channel 99-9.',

                        'content.gaming.title': 'Gaming',
                        'content.gaming.subtitle': 'GameZone',
                        'content.gaming.description': 'Live gaming streams, tournaments, and gaming content from Puerto Rico.',
                        'content.gaming.status': '🎮 Live Gaming',
                        'content.gaming.action': 'Play Now →',

                        'content.radio.title': 'Online Radio',
                        'content.radio.subtitle': 'Caribbean Sounds',
                        'content.radio.description': '24/7 Caribbean music, local DJs, and cultural programming.',
                        'content.radio.status': '📻 Live Radio',
                        'content.radio.action': 'Listen Now →',

                        'content.tv.title': 'Live TV',
                        'content.tv.subtitle': 'Channel 99-9',
                        'content.tv.description': 'Live Caribbean television with news, entertainment, and cultural shows.',
                        'content.tv.status': '📺 Live TV',
                        'content.tv.action': 'Watch Live →',

                        // Footer
                        'footer.description': 'Your premier destination for Gaming, Online Radio, and Live TV covering North Coast Puerto Rico.',
                        'footer.quick_links': 'Quick Links',
                        'footer.contact': 'Contact',
                        'footer.channel': 'Channel 99-9',
                        'footer.location': 'North Coast Puerto Rico',
                        'footer.follow': 'Follow Us',
                        'footer.copyright': '© 2023 Caribbean Advantage TV. All rights reserved.'
                    },
                    es: {
                        // Loading
                        'loading.text': 'Cargando tu entretenimiento...',

                        // Navigation
                        'nav.brand': 'Caribbean Advantage',
                        'nav.home': 'Inicio',
                        'nav.live': 'TV en Línea',
                        'nav.gamezone': 'Zona de Juegos',
                        'nav.radio': 'Radio en Línea',
                        'nav.events': 'Eventos',
                        'nav.contact': 'Contacto',

                        // Common
                        'common.watch_live': '📺 Ver en Vivo',
                        'common.watch_now': 'Ver Ahora',

                        // Hero
                        'hero.title': 'Caribbean Advantage\nCanal 99-9',
                        'hero.subtitle': 'Tu destino principal para Juegos, Radio en Línea y TV en Vivo cubriendo la Costa Norte de Puerto Rico. Apoyando artistas locales y trayéndote el mejor entretenimiento caribeño 24/7.',
                        'hero.watch_now': '🎬 Ver Ahora',
                        'hero.online_radio': '📻 Radio en Línea',

                        // Live Status
                        'live.now': 'En Vivo Ahora',
                        'live.show': 'Canal 99-9',
                        'live.location': 'Costa Norte PR',

                        // Status Section
                        'status.channel': 'Canal 99-9',
                        'status.location': 'Costa Norte Puerto Rico',
                        'status.live': '🔴 EN VIVO',
                        'status.radio': 'Caribbean TV',
                        'status.quality': 'HD',
                        'status.features': '🎮 Juegos • 📻 Radio • 📺 TV',
                        'status.artists': 'Apoyando Artistas Locales',
                        'status.community': 'Nuestro Contenido',
                        'status.learn_more': 'Saber Más →',

                        // Content Section
                        'content.title': 'Nuestro Contenido',
                        'content.subtitle': 'Experimenta juegos, radio en línea, TV en vivo y apoyo para artistas puertorriqueños locales en el Canal 99-9.',

                        'content.gaming.title': 'Juegos',
                        'content.gaming.subtitle': 'Zona de Juegos',
                        'content.gaming.description': 'Transmisiones de juegos en vivo, torneos y contenido de juegos desde Puerto Rico.',
                        'content.gaming.status': '🎮 Juegos en Vivo',
                        'content.gaming.action': 'Jugar Ahora →',

                        'content.radio.title': 'Radio en Línea',
                        'content.radio.subtitle': 'Sonidos Caribeños',
                        'content.radio.description': 'Música caribeña 24/7, DJs locales y programación cultural.',
                        'content.radio.status': '📻 Radio en Vivo',
                        'content.radio.action': 'Escuchar Ahora →',

                        'content.tv.title': 'TV en Vivo',
                        'content.tv.subtitle': 'Canal 99-9',
                        'content.tv.description': 'Televisión caribeña en vivo con noticias, entretenimiento y programas culturales.',
                        'content.tv.status': '📺 TV en Vivo',
                        'content.tv.action': 'Ver en Vivo →',

                        // Footer
                        'footer.description': 'Tu destino principal para Juegos, Radio en Línea y TV en Vivo cubriendo la Costa Norte de Puerto Rico.',
                        'footer.quick_links': 'Enlaces Rápidos',
                        'footer.contact': 'Contacto',
                        'footer.channel': 'Canal 99-9',
                        'footer.location': 'Costa Norte Puerto Rico',
                        'footer.follow': 'Síguenos',
                        'footer.copyright': '© 2023 Caribbean Advantage TV. Todos los derechos reservados.'
                    }
                };
            }

            setupLanguageSelector() {
                const languageOptions = document.querySelectorAll('.language-option');
                languageOptions.forEach(option => {
                    option.addEventListener('click', (e) => {
                        e.preventDefault();
                        const lang = option.getAttribute('data-lang');
                        this.changeLanguage(lang);
                    });
                });
            }

            changeLanguage(lang) {
                if (lang === this.currentLanguage) return;

                this.currentLanguage = lang;
                localStorage.setItem('language', lang);

                // Update active state
                document.querySelectorAll('.language-option').forEach(option => {
                    option.classList.toggle('active', option.getAttribute('data-lang') === lang);
                });

                // Update current language display
                const currentLangDisplay = document.getElementById('currentLang');
                if (currentLangDisplay) {
                    currentLangDisplay.textContent = lang.toUpperCase();
                }

                this.applyTranslations();
            }

            applyTranslations() {
                const elements = document.querySelectorAll('[data-translate]');
                elements.forEach(element => {
                    const key = element.getAttribute('data-translate');
                    const translation = this.getTranslation(key);
                    if (translation) {
                        if (key === 'hero.title') {
                            element.innerHTML = translation.replace('\\n', '<br><span style="color: var(--mint-blue);">') + '</span>';
                        } else {
                            element.textContent = translation;
                        }
                    }
                });
            }

            getTranslation(key) {
                return this.translations[this.currentLanguage]?.[key] || this.translations.en?.[key] || key;
            }
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize language manager
            window.languageManager = new LanguageManager();

            // Hide loading screen
            setTimeout(() => {
                document.getElementById('loadingScreen').classList.add('hidden');
            }, 1500);

            // Navigation functionality
            const navbar = document.getElementById('navbar');
            const navToggle = document.getElementById('navToggle');
            const navMenu = document.getElementById('navMenu');

            // Scroll effect
            window.addEventListener('scroll', function() {
                if (window.scrollY > 50) {
                    navbar.classList.add('scrolled');
                } else {
                    navbar.classList.remove('scrolled');
                }
            });

            // Mobile menu toggle
            navToggle.addEventListener('click', function() {
                navToggle.classList.toggle('active');
                navMenu.classList.toggle('active');
            });

            // Show now playing bar after 3 seconds
            setTimeout(() => {
                document.getElementById('nowPlayingBar').classList.add('show');
            }, 3000);

            // Close now playing bar
            document.getElementById('closeNowPlaying').addEventListener('click', function() {
                document.getElementById('nowPlayingBar').classList.remove('show');
            });

            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>