﻿AudioWorkletProcessor.prototype._w1=function(){this._x1=true;this.port.onmessage=(_y1)=>{if(_y1.data==="kill")this._x1=false;};};class _z1 extends AudioWorkletProcessor{static get parameterDescriptors(){return [{name:"bypass",automationRate:"a-rate",defaultValue:0,minValue:0,maxValue:1}];}constructor(){super();this._w1();}process(_A1,_B1,parameters){const input=_A1[0];const bypass=parameters.bypass;for(let c=0;c<input.length;++c){const _C1=input[c];for(let _D1=0;_D1<_C1.length;++_D1){const _E1=(bypass[_D1]!==undefined)?bypass[_D1]:bypass[0];
_B1[_E1][c][_D1]=_C1[_D1];}}return this._x1;}}class _F1 extends AudioWorkletProcessor{static get parameterDescriptors(){return [{name:"gain",automationRate:"a-rate",defaultValue:1,minValue:0}];}constructor(){super();this._w1();}process(_A1,_B1,parameters){const _G1=_A1[0];const _H1=_A1[1];const output=_B1[0];const gain=parameters.gain;for(let c=0;c<_H1.length;++c){const _C1=_H1[c];const _I1=output[c];for(let _D1=0;_D1<_C1.length;++_D1)_I1[_D1]=_C1[_D1];}for(let c=0;c<_G1.length;++c){const _C1=_G1[c];const _I1=output[c];
for(let _D1=0;_D1<_C1.length;++_D1){const _J1=(gain[_D1]!==undefined)?gain[_D1]:gain[0];_I1[_D1]+=_C1[_D1]*_J1;}}return this._x1;}}registerProcessor("audio-bus-input",_z1);registerProcessor("audio-bus-output",_F1);class _K1 extends AudioWorkletProcessor{static get parameterDescriptors(){return [{name:"bypass",automationRate:"a-rate",defaultValue:0,minValue:0,maxValue:1},{name:"gain",automationRate:"a-rate",defaultValue:1.0,minValue:0.0},{name:"factor",automationRate:"a-rate",defaultValue:20,minValue:1,maxValue:100}
,{name:"resolution",automationRate:"a-rate",defaultValue:8,minValue:2,maxValue:16},{name:"mix",automationRate:"a-rate",defaultValue:0.8,minValue:0.0,maxValue:1.0}];}static _L1=[undefined,undefined,2,4,8,16,32,64,128,256,512,1024,2048,4096,8192,16384,32768];constructor(_M1){super();this._w1();const _N1=_M1.outputChannelCount[0];this._O1=new Float32Array(_N1);this._P1=new Uint32Array(_N1);}process(_A1,_B1,parameters){const input=_A1[0];const output=_B1[0];const bypass=parameters.bypass;const gain=parameters.gain;
const factor=parameters.factor;const resolution=parameters.resolution;const mix=parameters.mix;for(let c=0;c<input.length;++c){const _C1=input[c];const _I1=output[c];for(let _D1=0;_D1<_C1.length;++_D1){_I1[_D1]=_C1[_D1];if(this._P1[c]===0)this._O1[c]=_C1[_D1];const _Q1=(factor[_D1]!==undefined)?factor[_D1]:factor[0];++this._P1[c];this._P1[c]%=_Q1;const _E1=(bypass[_D1]!==undefined)?bypass[_D1]:bypass[0];if(_E1>0.0){continue;}let _R1=this._O1[c];const _J1=(gain[_D1]!==undefined)?gain[_D1]:gain[0];_R1*=_J1;_R1=Math.max(Math.min(_R1,
1.0),-1.0);const _S1=(resolution[_D1]!==undefined)?resolution[_D1]:resolution[0];const max=(_R1>0.0)?_K1._L1[_S1]-1:_K1._L1[_S1];_R1=Math.round(_R1*max)/max;const _T1=(mix[_D1]!==undefined)?mix[_D1]:mix[0];_I1[_D1]*=(1.0-_T1);_I1[_D1]+=(_R1*_T1);}}return this._x1;}}registerProcessor("bitcrusher-processor",_K1);class _U1{constructor(_V1=1e-3){this.setTime(_V1);}setTime(_V1){this._W1=Math.exp(-1/(_V1*sampleRate));}process(_X1,_Y1){return _X1+this._W1*(_Y1-_X1);}}class _Z1{constructor(__1,_02){this._12=new _U1(__1);
this._22=new _U1(_02);this._32=__1;this._42=_02;}_52(_V1){if(_V1===this._32)return;this._12.setTime(_V1);this._32=_V1;}_62(_V1){if(_V1===this._42)return;this._22.setTime(_V1);this._42=_V1;}process(_X1,_Y1){if(_X1>_Y1)return this._12.process(_X1,_Y1);else return this._22.process(_X1,_Y1);}}class _72 extends AudioWorkletProcessor{static get parameterDescriptors(){return [{name:"bypass",automationRate:"a-rate",defaultValue:0,minValue:0,maxValue:1},{name:"ingain",automationRate:"a-rate",defaultValue:1,minValue:0}
,{name:"threshold",automationRate:"a-rate",defaultValue:0.125,minValue:1e-3,maxValue:1},{name:"ratio",automationRate:"a-rate",defaultValue:4,minValue:1},{name:"attack",automationRate:"a-rate",defaultValue:0.05,minValue:1e-3,maxValue:1e-1},{name:"release",automationRate:"a-rate",defaultValue:0.25,minValue:1e-2,maxValue:1},{name:"outgain",automationRate:"a-rate",defaultValue:1,minValue:0}];}constructor(_82){super();this._w1();const _12=_72.parameterDescriptors.find(_92=>_92.name==="attack");const _22=_72.parameterDescriptors.find(_92=>_92.name==="release");
this._a2=new _Z1(_12.defaultValue,_22.defaultValue);this._b2=0;}process(_c2,_d2,_e2){const input=_c2[0];const output=_d2[0];const bypass=_e2.bypass;const ingain=_e2.ingain;const outgain=_e2.outgain;const threshold=_e2.threshold;const ratio=_e2.ratio;const attack=_e2.attack;const release=_e2.release;if(input.length===0)return this._x1;for(let _D1=0;_D1<input[0].length;++_D1){let frame=input.map(_f2=>_f2[_D1]);output.forEach((_f2,_g2)=>{_f2[_D1]=frame[_g2];});const _h2=(ingain[_D1]!==undefined)?ingain[_D1]:ingain[0];
frame=frame.map(_i2=>_i2*=_h2);const rect=frame.map(_i2=>Math.abs(_i2));const max=Math.max(...rect);const _j2=_k2(max);const _l2=(threshold[_D1]!==undefined)?threshold[_D1]:threshold[0];const _m2=_k2(_l2);const _n2=Math.max(0,_j2-_m2);const _12=(attack[_D1]!==undefined)?attack[_D1]:attack[0];const _22=(release[_D1]!==undefined)?release[_D1]:release[0];this._a2._52(_12);this._a2._62(_22);this._b2=this._a2.process(_n2,this._b2);const _E1=(bypass[_D1]!==undefined)?bypass[_D1]:bypass[0];if(_E1>0)continue;const _S1=(ratio[_D1]!==undefined)?ratio[_D1]:ratio[0];
const _o2=(this._b2/_S1)-this._b2;const _J1=_p2(_o2);frame=frame.map(_i2=>_i2*=_J1);const _q2=(outgain[_D1]!==undefined)?outgain[_D1]:outgain[0];frame=frame.map(_i2=>_i2*=_q2);output.forEach((_f2,_g2)=>{_f2[_D1]=frame[_g2];});}return this._x1;}}function _k2(_r2){return 20*Math.log10(_r2);}function _p2(_r2){return Math.pow(10,_r2/20);}registerProcessor("compressor-processor",_72);class _s2 extends AudioWorkletProcessor{static _t2=5.0;static get parameterDescriptors(){return [{name:"bypass",automationRate:"a-rate",
defaultValue:0,minValue:0,maxValue:1},{name:"time",automationRate:"a-rate",defaultValue:0.2,minValue:0.0,maxValue:_s2._t2},{name:"feedback",automationRate:"a-rate",defaultValue:0.5,minValue:0.0,maxValue:1.0},{name:"mix",automationRate:"a-rate",defaultValue:0.35,minValue:0.0,maxValue:1.0}];}constructor(_M1){super();this._w1();const _N1=_M1.outputChannelCount[0];const _u2=(_s2._t2*sampleRate)+1;this.buffer=new Array(_N1);this._v2=new Uint32Array(_N1);for(let c=0;c<_N1;++c)this.buffer[c]=new Float32Array(_u2);
}process(_A1,_B1,parameters){const input=_A1[0];const output=_B1[0];const bypass=parameters.bypass;const time=parameters.time;const feedback=parameters.feedback;const mix=parameters.mix;for(let c=0;c<input.length;++c){const _C1=input[c];const _I1=output[c];for(let _D1=0;_D1<_C1.length;++_D1){_I1[_D1]=_C1[_D1];const _l2=(time[_D1]!==undefined)?time[_D1]:time[0];const _w2=this._x2(c,_l2);const _Q1=(feedback[_D1]!==undefined)?feedback[_D1]:feedback[0];const _y2=_C1[_D1]+(_w2*_Q1);this.write(c,_y2);const _E1=(bypass[_D1]!==undefined)?bypass[_D1]:bypass[0];
if(_E1>0.0){continue;}const _T1=(mix[_D1]!==undefined)?mix[_D1]:mix[0];_I1[_D1]*=(1-_T1);_I1[_D1]+=(_w2*_T1);}}return this._x1;}_x2(_z2,_V1){const _A2=_V1*sampleRate;let _B2=(this._v2[_z2]-~~_A2);let _C2=(_B2-1);while(_B2<0)_B2+=this.buffer[_z2].length;while(_C2<0)_C2+=this.buffer[_z2].length;const frac=_A2-~~_A2;const _D2=this.buffer[_z2][_B2];const _E2=this.buffer[_z2][_C2];return _D2+(_E2-_D2)*frac;}write(_z2,_F2){++this._v2[_z2];this._v2[_z2]%=this.buffer[_z2].length;this.buffer[_z2][this._v2[_z2]]=_F2;
}}registerProcessor("delay-processor",_s2);class _G2 extends AudioWorkletProcessor{static get parameterDescriptors(){return [];}constructor(){super();this._w1();}process(_H2,_I2,_J2){const input=_H2[0];const _K2=_I2[0];const _L2=_I2[1];for(let c=0;c<input.length;++c){const _C1=input[c];const _M2=_K2[c];const _N2=_L2[c];for(let _D1=0;_D1<_C1.length;++_D1){_M2[_D1]=_C1[_D1];_N2[_D1]=_C1[_D1];}}return this._x1;}}class _O2 extends AudioWorkletProcessor{static get parameterDescriptors(){return [{name:"bypass",automationRate:"a-rate",
defaultValue:0,minValue:0,maxValue:1}];}constructor(){super();this._w1();}process(_H2,_I2,_J2){const _G1=_H2[0];const _H1=_H2[1];const output=_I2[0];const bypass=_J2.bypass;for(let c=0;c<_H1.length;++c){const _P2=_G1[c];const _Q2=_H1[c];const _I1=output[c];for(let _D1=0;_D1<_P2.length;++_D1){const _E1=(bypass[_D1]!==undefined)?bypass[_D1]:bypass[0];if(_E1>0){_I1[_D1]=_Q2[_D1];}else {_I1[_D1]=_P2[_D1];}}}return this._x1;}}registerProcessor("eq-input",_G2);registerProcessor("eq-output",_O2);class _R2 extends AudioWorkletProcessor{
static get parameterDescriptors(){return [{name:"bypass",automationRate:"a-rate",defaultValue:0,minValue:0,maxValue:1},{name:"gain",automationRate:"a-rate",defaultValue:0.5,minValue:0.0}];}constructor(){super();this._w1();}process(_A1,_B1,parameters){const input=_A1[0];const output=_B1[0];const bypass=parameters.bypass;const gain=parameters.gain;for(let c=0;c<input.length;++c){const _C1=input[c];const _I1=output[c];for(let _D1=0;_D1<_C1.length;++_D1){_I1[_D1]=_C1[_D1];const _E1=(bypass[_D1]!==undefined)?bypass[_D1]:bypass[0];
if(_E1>0.0){continue;}const _J1=(gain[_D1]!==undefined)?gain[_D1]:gain[0];_I1[_D1]*=_J1;}}return this._x1;}}registerProcessor("gain-processor",_R2);class _S2 extends AudioWorkletProcessor{static get parameterDescriptors(){const _T2=Math.min(sampleRate/2.0,20000.0);return [{name:"bypass",automationRate:"a-rate",defaultValue:0,minValue:0,maxValue:1},{name:"freq",automationRate:"a-rate",defaultValue:Math.min(5000.0,_T2),minValue:10.0,maxValue:_T2},{name:"q",automationRate:"a-rate",defaultValue:1.0,minValue:1.0,
maxValue:100.0},{name:"gain",automationRate:"a-rate",defaultValue:1e-2,minValue:1e-6}];}constructor(_M1){super();this._w1();const _N1=_M1.outputChannelCount[0];this._U2=0;this._V2=0;this._W2=0;this._X2=0;this._Y2=0;this._Z2=new Float32Array(_N1);this.__2=new Float32Array(_N1);this._03=new Float32Array(_N1);this._13=new Float32Array(_N1);this._23=-1;this._33=-1;this._43=-1;}process(_A1,_B1,parameters){const input=_A1[0];const output=_B1[0];const bypass=parameters.bypass;const freq=parameters.freq;const q=parameters.q;
const gain=parameters.gain;const _53=(freq.length===1&&q.length===1&&gain.length===1);if(_53)this._63(freq[0],q[0],gain[0]);for(let c=0;c<input.length;++c){const _C1=input[c];const _I1=output[c];for(let _D1=0;_D1<_C1.length;++_D1){if(_53===false){const _Q1=(freq[_D1]!==undefined)?freq[_D1]:freq[0];const _73=(q[_D1]!==undefined)?q[_D1]:q[0];const _J1=(gain[_D1]!==undefined)?gain[_D1]:gain[0];this._63(_Q1,_73,_J1);}const _83=this._W2*_C1[_D1]+this._X2*this._Z2[c]+this._Y2*this.__2[c]-this._U2*this._03[c]-this._V2*this._13[c];
this.__2[c]=this._Z2[c];this._Z2[c]=_C1[_D1];this._13[c]=this._03[c];this._03[c]=_83;const _E1=(bypass[_D1]!==undefined)?bypass[_D1]:bypass[0];_I1[_D1]=(_E1>0)?_C1[_D1]:_83;}}return this._x1;}_63(_93,_a3,_b3){if(_93===this._23&&_a3===this._33&&_b3===this._43)return;const _c3=2*Math.PI*_93/sampleRate;const _d3=Math.cos(_c3);const _e3=Math.sqrt(_b3);const _f3=_e3+1;const _g3=_e3-1;const _h3=_f3*_d3;const _i3=_g3*_d3;const _j3=_f3-_i3;const _k3=_f3+_i3;const alpha=Math.sin(_c3)/(2*_a3);const _l3=(2*Math.sqrt(_e3)*alpha);
const _m3=_j3+_l3;const _U2=2*(_g3-_h3);const _V2=_j3-_l3;const _W2=_e3*(_k3+_l3);const _X2=-2*_e3*(_g3+_h3);const _Y2=_e3*(_k3-_l3);this._U2=_U2/_m3;this._V2=_V2/_m3;this._W2=_W2/_m3;this._X2=_X2/_m3;this._Y2=_Y2/_m3;this._23=_93;this._33=_a3;this._43=_b3;}}registerProcessor("hi-shelf-processor",_S2);class _n3 extends AudioWorkletProcessor{static get parameterDescriptors(){const _o3=Math.min(sampleRate/2.0,20000.0);return [{name:"bypass",automationRate:"a-rate",defaultValue:0,minValue:0,maxValue:1},{name:"cutoff",
automationRate:"a-rate",defaultValue:Math.min(1500.0,_o3),minValue:10.0,maxValue:_o3},{name:"q",automationRate:"a-rate",defaultValue:1.5,minValue:1.0,maxValue:100.0}];}constructor(_M1){super();this._w1();const _N1=_M1.outputChannelCount[0];this._U2=0;this._V2=0;this._W2=0;this._X2=0;this._Y2=0;this._Z2=new Float32Array(_N1);this.__2=new Float32Array(_N1);this._03=new Float32Array(_N1);this._13=new Float32Array(_N1);this._p3=-1;this._33=-1;}process(_A1,_B1,parameters){const input=_A1[0];const output=_B1[0];
const bypass=parameters.bypass;const cutoff=parameters.cutoff;const q=parameters.q;const _53=(cutoff.length===1&&q.length===1);if(_53)this._63(cutoff[0],q[0]);for(let c=0;c<input.length;++c){const _C1=input[c];const _I1=output[c];for(let _D1=0;_D1<_C1.length;++_D1){if(_53===false){const c=(cutoff[_D1]!==undefined)?cutoff[_D1]:cutoff[0];const _73=(q[_D1]!==undefined)?q[_D1]:q[0];this._63(c,_73);}const _83=this._W2*_C1[_D1]+this._X2*this._Z2[c]+this._Y2*this.__2[c]-this._U2*this._03[c]-this._V2*this._13[c];this.__2[c]=this._Z2[c];
this._Z2[c]=_C1[_D1];this._13[c]=this._03[c];this._03[c]=_83;const _E1=(bypass[_D1]!==undefined)?bypass[_D1]:bypass[0];_I1[_D1]=(_E1>0)?_C1[_D1]:_83;}}return this._x1;}_63(_q3,_a3){if(_q3===this._p3&&_a3===this._33)return;const _c3=2*Math.PI*_q3/sampleRate;const alpha=Math.sin(_c3)/(2*_a3);const _d3=Math.cos(_c3);const _m3=1+alpha;const _U2=-2*_d3;const _V2=1-alpha;const _W2=(1+_d3)/2;const _X2=-1-_d3;const _Y2=(1+_d3)/2;this._U2=_U2/_m3;this._V2=_V2/_m3;this._W2=_W2/_m3;this._X2=_X2/_m3;this._Y2=_Y2/_m3;this._p3=_q3;
this._33=_a3;}}registerProcessor("hpf2-processor",_n3);class _r3 extends AudioWorkletProcessor{static get parameterDescriptors(){const _T2=Math.min(sampleRate/2.0,20000.0);return [{name:"bypass",automationRate:"a-rate",defaultValue:0,minValue:0,maxValue:1},{name:"freq",automationRate:"a-rate",defaultValue:Math.min(500.0,_T2),minValue:10.0,maxValue:_T2},{name:"q",automationRate:"a-rate",defaultValue:1.0,minValue:1.0,maxValue:100.0},{name:"gain",automationRate:"a-rate",defaultValue:1e-2,minValue:1e-6}];}
constructor(_M1){super();this._w1();const _N1=_M1.outputChannelCount[0];this._U2=0;this._V2=0;this._W2=0;this._X2=0;this._Y2=0;this._Z2=new Float32Array(_N1);this.__2=new Float32Array(_N1);this._03=new Float32Array(_N1);this._13=new Float32Array(_N1);this._23=-1;this._33=-1;this._43=-1;}process(_A1,_B1,parameters){const input=_A1[0];const output=_B1[0];const bypass=parameters.bypass;const freq=parameters.freq;const q=parameters.q;const gain=parameters.gain;const _53=(freq.length===1&&q.length===1&&gain.length===1);
if(_53)this._63(freq[0],q[0],gain[0]);for(let c=0;c<input.length;++c){const _C1=input[c];const _I1=output[c];for(let _D1=0;_D1<_C1.length;++_D1){if(_53===false){const _Q1=(freq[_D1]!==undefined)?freq[_D1]:freq[0];const _73=(q[_D1]!==undefined)?q[_D1]:q[0];const _J1=(gain[_D1]!==undefined)?gain[_D1]:gain[0];this._63(_Q1,_73,_J1);}const _83=this._W2*_C1[_D1]+this._X2*this._Z2[c]+this._Y2*this.__2[c]-this._U2*this._03[c]-this._V2*this._13[c];this.__2[c]=this._Z2[c];this._Z2[c]=_C1[_D1];this._13[c]=this._03[c];
this._03[c]=_83;const _E1=(bypass[_D1]!==undefined)?bypass[_D1]:bypass[0];_I1[_D1]=(_E1>0)?_C1[_D1]:_83;}}return this._x1;}_63(_93,_a3,_b3){if(_93===this._23&&_a3===this._33&&_b3===this._43)return;const _c3=2*Math.PI*_93/sampleRate;const _d3=Math.cos(_c3);const _e3=Math.sqrt(_b3);const _f3=_e3+1;const _g3=_e3-1;const _h3=_f3*_d3;const _i3=_g3*_d3;const _j3=_f3-_i3;const _k3=_f3+_i3;const alpha=Math.sin(_c3)/(2*_a3);const _l3=(2*Math.sqrt(_e3)*alpha);const _m3=_k3+_l3;const _U2=-2*(_g3+_h3);const _V2=_k3-_l3;const _W2=_e3*(_j3+_l3);
const _X2=2*_e3*(_g3-_h3);const _Y2=_e3*(_j3-_l3);this._U2=_U2/_m3;this._V2=_V2/_m3;this._W2=_W2/_m3;this._X2=_X2/_m3;this._Y2=_Y2/_m3;this._23=_93;this._33=_a3;this._43=_b3;}}registerProcessor("lo-shelf-processor",_r3);class _s3 extends AudioWorkletProcessor{static get parameterDescriptors(){const _o3=Math.min(sampleRate/2.0,20000.0);return [{name:"bypass",automationRate:"a-rate",defaultValue:0,minValue:0,maxValue:1},{name:"cutoff",automationRate:"a-rate",defaultValue:Math.min(500.0,_o3),minValue:10.0,maxValue:_o3}
,{name:"q",automationRate:"a-rate",defaultValue:1.5,minValue:1.0,maxValue:100.0}];}constructor(_M1){super();this._w1();const _N1=_M1.outputChannelCount[0];this._U2=0;this._V2=0;this._W2=0;this._X2=0;this._Y2=0;this._Z2=new Float32Array(_N1);this.__2=new Float32Array(_N1);this._03=new Float32Array(_N1);this._13=new Float32Array(_N1);this._p3=-1;this._33=-1;}process(_A1,_B1,parameters){const input=_A1[0];const output=_B1[0];const bypass=parameters.bypass;const cutoff=parameters.cutoff;const q=parameters.q;const _53=(cutoff.length===1&&q.length===1);
if(_53)this._63(cutoff[0],q[0]);for(let c=0;c<input.length;++c){const _C1=input[c];const _I1=output[c];for(let _D1=0;_D1<_C1.length;++_D1){if(_53===false){const c=(cutoff[_D1]!==undefined)?cutoff[_D1]:cutoff[0];const _73=(q[_D1]!==undefined)?q[_D1]:q[0];this._63(c,_73);}const _83=this._W2*_C1[_D1]+this._X2*this._Z2[c]+this._Y2*this.__2[c]-this._U2*this._03[c]-this._V2*this._13[c];this.__2[c]=this._Z2[c];this._Z2[c]=_C1[_D1];this._13[c]=this._03[c];this._03[c]=_83;const _E1=(bypass[_D1]!==undefined)?bypass[_D1]:bypass[0];
_I1[_D1]=(_E1>0)?_C1[_D1]:_83;}}return this._x1;}_63(_q3,_a3){if(_q3===this._p3&&_a3===this._33)return;const _c3=2*Math.PI*_q3/sampleRate;const alpha=Math.sin(_c3)/(2*_a3);const _d3=Math.cos(_c3);const _m3=1+alpha;const _U2=-2*_d3;const _V2=1-alpha;const _W2=(1-_d3)/2;const _X2=1-_d3;const _Y2=(1-_d3)/2;this._U2=_U2/_m3;this._V2=_V2/_m3;this._W2=_W2/_m3;this._X2=_X2/_m3;this._Y2=_Y2/_m3;this._p3=_q3;this._33=_a3;}}registerProcessor("lpf2-processor",_s3);class _t3 extends AudioWorkletProcessor{static get parameterDescriptors(){
const _T2=Math.min(sampleRate/2.0,20000.0);return [{name:"bypass",automationRate:"a-rate",defaultValue:0,minValue:0,maxValue:1},{name:"freq",automationRate:"a-rate",defaultValue:Math.min(1500.0,_T2),minValue:10.0,maxValue:_T2},{name:"q",automationRate:"a-rate",defaultValue:1.0,minValue:1.0,maxValue:100.0},{name:"gain",automationRate:"a-rate",defaultValue:1e-2,minValue:1e-6}];}constructor(_M1){super();this._w1();const _N1=_M1.outputChannelCount[0];this._U2=0;this._V2=0;this._W2=0;this._X2=0;this._Y2=0;
this._Z2=new Float32Array(_N1);this.__2=new Float32Array(_N1);this._03=new Float32Array(_N1);this._13=new Float32Array(_N1);this._23=-1;this._33=-1;this._43=-1;}process(_A1,_B1,parameters){const input=_A1[0];const output=_B1[0];const bypass=parameters.bypass;const freq=parameters.freq;const q=parameters.q;const gain=parameters.gain;const _53=(freq.length===1&&q.length===1&&gain.length===1);if(_53)this._63(freq[0],q[0],gain[0]);for(let c=0;c<input.length;++c){const _C1=input[c];const _I1=output[c];for(let _D1=0;
_D1<_C1.length;++_D1){if(_53===false){const _Q1=(freq[_D1]!==undefined)?freq[_D1]:freq[0];const _73=(q[_D1]!==undefined)?q[_D1]:q[0];const _J1=(gain[_D1]!==undefined)?gain[_D1]:gain[0];this._63(_Q1,_73,_J1);}const _83=this._W2*_C1[_D1]+this._X2*this._Z2[c]+this._Y2*this.__2[c]-this._U2*this._03[c]-this._V2*this._13[c];this.__2[c]=this._Z2[c];this._Z2[c]=_C1[_D1];this._13[c]=this._03[c];this._03[c]=_83;const _E1=(bypass[_D1]!==undefined)?bypass[_D1]:bypass[0];_I1[_D1]=(_E1>0)?_C1[_D1]:_83;}}return this._x1;
}_63(_93,_a3,_b3){if(_93===this._23&&_a3===this._33&&_b3===this._43)return;const _c3=2*Math.PI*_93/sampleRate;const _d3=Math.cos(_c3);const _e3=Math.sqrt(_b3);const alpha=Math.sin(_c3)/(2*_a3);const _u3=alpha/_e3;const _v3=alpha*_e3;const _m3=1+_u3;const _U2=-2*_d3;const _V2=1-_u3;const _W2=1+_v3;const _X2=_U2;const _Y2=1-_v3;this._U2=_U2/_m3;this._V2=_V2/_m3;this._W2=_W2/_m3;this._X2=_X2/_m3;this._Y2=_Y2/_m3;this._23=_93;this._33=_a3;this._43=_b3;}}registerProcessor("peak-eq-processor",_t3);class _w3{constructor(_x3){
this._y3=0;this._z3=0;this.feedback=0;this._A3=0;this.buffer=new Float32Array(_x3);this._B3=0;}process(_F2){const out=this.buffer[this._B3];this._A3=(this._A3*this._y3)+(out*this._z3);this.buffer[this._B3]=_F2+(this._A3*this.feedback);++this._B3;this._B3%=this.buffer.length;return out;}_C3(_D3){this.feedback=Math.min(Math.max(0,_D3),1);}_E3(_F3){this._y3=Math.min(Math.max(0,_F3),1);this._z3=1-this._y3;}}class _G3{constructor(_x3){this.feedback=0;this.buffer=new Float32Array(_x3);this._B3=0;}process(_F2){
const out=this.buffer[this._B3];this.buffer[this._B3]=_F2+(out*this.feedback);++this._B3;this._B3%=this.buffer.length;return(out-_F2);}_C3(_D3){this.feedback=Math.min(Math.max(0,_D3),1);}}class _H3 extends AudioWorkletProcessor{static _I3=8;static _J3=4;static _K3=0.015;static _L3=0.4;static _M3=0.28;static _N3=0.7;static _O3=[1116,1188,1277,1356,1422,1491,1557,1617];static _P3=[1139,1211,1300,1379,1445,1514,1580,1640];static _Q3=[556,441,341,225];static _R3=[579,464,364,248];static get parameterDescriptors(){return [{
name:"bypass",automationRate:"a-rate",defaultValue:0,minValue:0,maxValue:1},{name:"size",automationRate:"a-rate",defaultValue:0.7,minValue:0.0,maxValue:1.0},{name:"damp",automationRate:"a-rate",defaultValue:0.1,minValue:0.0,maxValue:1.0},{name:"mix",automationRate:"a-rate",defaultValue:0.35,minValue:0.0,maxValue:1.0}];}constructor(_M1){super();this._w1();const _N1=_M1.outputChannelCount[0];this._S3=-1;this._T3=-1;this._U3=new Array(_N1);this._V3=new Array(_N1);const _W3=[_H3._O3,_H3._P3];const _X3=[_H3._Q3,
_H3._R3];for(let c=0;c<_N1;++c){this._U3[c]=new Array(_H3._I3);this._V3[c]=new Array(_H3._J3);for(let i=0;i<_H3._I3;++i)this._U3[c][i]=new _w3(_W3[c%_W3.length][i]);for(let i=0;i<_H3._J3;++i)this._V3[c][i]=new _G3(_X3[c%_X3.length][i]);}this._Y3(0.5);this._E3(0.5);for(let c=0;c<_N1;++c)for(let i=0;i<_H3._J3;++i)this._V3[c][i]._C3(0.5);}process(_A1,_B1,parameters){const input=_A1[0];const output=_B1[0];const bypass=parameters.bypass;const size=parameters.size;const damp=parameters.damp;const mix=parameters.mix;
for(let c=0;c<input.length;++c){const _C1=input[c];const _I1=output[c];for(let _Z3=0;_Z3<_C1.length;++_Z3){const _D1=(size[_Z3]!==undefined)?size[_Z3]:size[0];const __3=(damp[_Z3]!==undefined)?damp[_Z3]:damp[0];this._Y3(_D1);this._E3(__3);_I1[_Z3]=_C1[_Z3];let out=0;const _R1=_C1[_Z3]*_H3._K3;for(let i=0;i<_H3._I3;++i)out+=this._U3[c][i].process(_R1);for(let i=0;i<_H3._J3;++i)out=this._V3[c][i].process(out);const _E1=(bypass[_Z3]!==undefined)?bypass[_Z3]:bypass[0];if(_E1>0.0){continue;}const _T1=(mix[_Z3]!==undefined)?mix[_Z3]:mix[0];
_I1[_Z3]*=(1-_T1);_I1[_Z3]+=(out*_T1);}}return this._x1;}_Y3(_x3){if(_x3===this._S3)return;const size=(_x3*_H3._M3)+_H3._N3;for(let c=0;c<this._U3.length;++c)for(let i=0;i<_H3._I3;++i)this._U3[c][i]._C3(size);this._S3=_x3;}_E3(_F3){if(_F3===this._T3)return;const damp=_F3*_H3._L3;for(let c=0;c<this._U3.length;++c)for(let i=0;i<_H3._I3;++i)this._U3[c][i]._E3(damp);this._T3=_F3;}}registerProcessor("reverb1-processor",_H3);class _04 extends AudioWorkletProcessor{static get parameterDescriptors(){return [{name:"bypass",
automationRate:"a-rate",defaultValue:0,minValue:0,maxValue:1},{name:"rate",automationRate:"a-rate",defaultValue:5.0,minValue:0.0,maxValue:20.0},{name:"intensity",automationRate:"a-rate",defaultValue:1.0,minValue:0.0,maxValue:1.0},{name:"offset",automationRate:"a-rate",defaultValue:0.0,minValue:0.0,maxValue:1.0},{name:"shape",automationRate:"a-rate",defaultValue:0,minValue:0,maxValue:4}];}constructor(_M1){super();this._w1();const _N1=_M1.outputChannelCount[0];this._14=new Array(_N1).fill(1.0);this._24=new Array(_N1).fill(0.0);
this._34=new Array(_N1).fill(_44._54._64);this._74=new Array(_N1);for(let c=0;c<_N1;++c){this._74[c]=new _84();this._74[c]._94(sampleRate);this._74[c]._a4(this._14[c]);this._74[c]._b4(this._34[c]);if(c%2===1){this._74[c]._c4(this._24[c]);}}}process(_A1,_B1,parameters){const input=_A1[0];const output=_B1[0];const bypass=parameters.bypass;const rate=parameters.rate;const intensity=parameters.intensity;const offset=parameters.offset;const shape=parameters.shape;for(let c=0;c<input.length;++c){const _C1=input[c];
const _I1=output[c];for(let _D1=0;_D1<_C1.length;++_D1){_I1[_D1]=_C1[_D1];const _S1=(rate[_D1]!==undefined)?rate[_D1]:rate[0];const _d4=(offset[_D1]!==undefined)?offset[_D1]:offset[0];const _e4=(shape[_D1]!==undefined)?shape[_D1]:shape[0];this._f4(c,_S1,_d4,_e4);const _g4=this._74[c]._x2();const _E1=(bypass[_D1]!==undefined)?bypass[_D1]:bypass[0];if(_E1>0.0){continue;}const i=(intensity[_D1]!==undefined)?intensity[_D1]:intensity[0];const out=_C1[_D1]*_g4*i;_I1[_D1]*=(1.0-i);_I1[_D1]+=out;}}return this._x1;
}_f4(_z2,_h4,_i4,_j4){if(_h4!==this._14[_z2]){this._74[_z2]._a4(_h4);this._14[_z2]=_h4;}if(_i4!==this._24[_z2]){if(_z2%2===1){this._74[_z2]._c4(_i4);}this._24[_z2]=_i4;}if(_j4!==this._34[_z2]){this._74[_z2]._b4(_j4);this._34[_z2]=_j4;}}}registerProcessor("tremolo-processor",_04);function _44(){}_44._54={_64:0,_k4:1,_l4:2,_m4:3,_n4:4,_o4:5};_44._p4=function(_q4){return 1.0-_q4;};_44._r4=function(_q4){return _q4;};_44._s4=function(_q4){return 0.5*(Math.sin((_q4*2.0*Math.PI)-(Math.PI/2.0))+1.0);};_44._t4=function(_q4){
if(_q4<0.5){return 0.0;}return 1.0;};_44._u4=function(_q4){if(_q4<0.5){return 2.0*_q4;}return 2.0-(2.0*_q4);};_44._v4=[_44._p4,_44._r4,_44._s4,_44._t4,_44._u4];_w4._x4=512;_w4._y4=1.0/_w4._x4;function _w4(_z4){this.data=new Float32Array(_w4._x4);for(let i=0;i<_w4._x4;++i){this.data[i]=_z4(i*_w4._y4);}}_w4.prototype._x2=function(_q4){_q4=Math.max(0.0,_q4);_q4=Math.min(_q4,1.0);const _A4=_q4*_w4._x4;const _B4=~~_A4;const _C4=_A4-_B4;let _B2=_B4;let _C2=_B2+1;if(_B2>=_w4._x4){_B2-=_w4._x4;}if(_C2>=_w4._x4){_C2-=_w4._x4;
}const _D2=this.data[_B2];const _E2=this.data[_C2];return _D2+(_E2-_D2)*_C4;};_84._D4=[];_84._E4=false;_84._F4=0.0;_84._T2=20.0;function _84(){this._G4=48000;this.shape=_44._54._l4;this.freq=1.0;this._H4=0.0;this._y4=0.0;this._I4=0.0;if(_84._E4==true){return;}for(let i=0;i<_44._54._o4;++i){_84._D4[i]=new _w4(_44._v4[i]);}_84._E4=true;}_84._J4=function(){return(_84._E4==true);};_84.prototype._94=function(_K4){this._G4=_K4;this._L4();};_84.prototype._a4=function(_93){_93=Math.max(_84._F4,_93);_93=Math.min(_93,
_84._T2);this.freq=_93;this._L4();};_84.prototype._c4=function(_i4){_i4=Math.max(0.0,_i4);_i4=Math.min(_i4,1.0);const _M4=_i4-this._I4;this._I4=_i4;this._H4+=_M4;while(this._H4>=1.0){this._H4-=1.0;}while(this._H4<0.0){this._H4+=1.0;}};_84.prototype._b4=function(_j4){_j4=Math.max(0,_j4);_j4=Math.min(_j4,_44._54._o4-1);this.shape=_j4;};_84.prototype._x2=function(){const result=_84._D4[this.shape]._x2(this._H4);this._H4+=this._y4;while(this._H4>=1.0){this._H4-=1.0;}return result;};_84.prototype._L4=function(){
this._y4=this.freq/this._G4;};