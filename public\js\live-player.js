// Caribbean Advantage TV - Live Player JavaScript

// Live Player Configuration
const PLAYER_CONFIG = {
    externalUrl: 'http://caribbeanadvantage.com/CA-tv.html',
    retryAttempts: 3,
    retryDelay: 2000,
    loadingTimeout: 10000
};

// Hide loading indicator when iframe loads
function hideLoading() {
    setTimeout(() => {
        const loadingIndicator = document.getElementById('loading-indicator');
        if (loadingIndicator) {
            loadingIndicator.style.display = 'none';
        }
    }, 2000);
}

// Show error message if iframe fails to load
function showError() {
    const loadingIndicator = document.getElementById('loading-indicator');
    const errorMessage = document.getElementById('error-message');
    
    if (loadingIndicator) loadingIndicator.style.display = 'none';
    if (errorMessage) errorMessage.style.display = 'flex';
}

// Reload the player
function reloadPlayer() {
    const iframe = document.getElementById('tv-player');
    const loadingIndicator = document.getElementById('loading-indicator');
    const errorMessage = document.getElementById('error-message');
    
    if (!iframe) return;
    
    // Show loading and hide error
    if (loadingIndicator) loadingIndicator.style.display = 'flex';
    if (errorMessage) errorMessage.style.display = 'none';
    
    // Reload iframe
    iframe.src = iframe.src;
}

// Toggle fullscreen
function toggleFullscreen() {
    const playerContainer = document.querySelector('.player-container');
    
    if (!playerContainer) return;
    
    if (!document.fullscreenElement) {
        playerContainer.requestFullscreen().catch(err => {
            console.log('Error attempting to enable fullscreen:', err);
            // Fallback for older browsers
            if (playerContainer.webkitRequestFullscreen) {
                playerContainer.webkitRequestFullscreen();
            } else if (playerContainer.mozRequestFullScreen) {
                playerContainer.mozRequestFullScreen();
            } else if (playerContainer.msRequestFullscreen) {
                playerContainer.msRequestFullscreen();
            }
        });
    } else {
        if (document.exitFullscreen) {
            document.exitFullscreen();
        } else if (document.webkitExitFullscreen) {
            document.webkitExitFullscreen();
        } else if (document.mozCancelFullScreen) {
            document.mozCancelFullScreen();
        } else if (document.msExitFullscreen) {
            document.msExitFullscreen();
        }
    }
}

// Open player in new tab
function openInNewTab() {
    window.open(PLAYER_CONFIG.externalUrl, '_blank');
}

// Simulate viewer count updates
function updateViewerCount() {
    const viewerElement = document.getElementById('viewer-count');
    if (!viewerElement) return;
    
    const baseCount = 1200;
    const randomVariation = Math.floor(Math.random() * 100);
    viewerElement.textContent = (baseCount + randomVariation).toLocaleString();
}

// Update current time
function updateTime() {
    const timeElement = document.getElementById('current-time');
    if (!timeElement) return;
    
    const now = new Date();
    const timeString = now.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        timeZone: 'America/Jamaica'
    });
    timeElement.textContent = timeString + ' (Jamaica Time)';
}

// Handle iframe communication
function handlePlayerMessages() {
    window.addEventListener('message', function(event) {
        // Handle messages from the external player if needed
        if (event.origin === 'http://caribbeanadvantage.com') {
            console.log('Message from player:', event.data);
            
            // Handle different message types
            switch(event.data.type) {
                case 'player_ready':
                    hideLoading();
                    break;
                case 'player_error':
                    showError();
                    break;
                case 'viewer_count':
                    if (event.data.count) {
                        const viewerElement = document.getElementById('viewer-count');
                        if (viewerElement) {
                            viewerElement.textContent = event.data.count.toLocaleString();
                        }
                    }
                    break;
            }
        }
    });
}

// Keyboard shortcuts
function initKeyboardShortcuts() {
    document.addEventListener('keydown', function(event) {
        // Prevent shortcuts when typing in inputs
        if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
            return;
        }
        
        switch(event.key.toLowerCase()) {
            case 'f':
                if (event.ctrlKey || event.metaKey) {
                    event.preventDefault();
                    toggleFullscreen();
                }
                break;
            case 'r':
                if (event.ctrlKey || event.metaKey) {
                    event.preventDefault();
                    reloadPlayer();
                }
                break;
            case ' ':
                // Spacebar - could be used for play/pause if player supports it
                event.preventDefault();
                // Send message to player iframe
                const iframe = document.getElementById('tv-player');
                if (iframe && iframe.contentWindow) {
                    iframe.contentWindow.postMessage({type: 'toggle_play'}, '*');
                }
                break;
            case 'escape':
                // Exit fullscreen
                if (document.fullscreenElement) {
                    document.exitFullscreen();
                }
                break;
        }
    });
}

// Player health check
function checkPlayerHealth() {
    const iframe = document.getElementById('tv-player');
    if (!iframe) return;
    
    // Set a timeout to show error if player doesn't load
    setTimeout(() => {
        const loadingIndicator = document.getElementById('loading-indicator');
        if (loadingIndicator && loadingIndicator.style.display !== 'none') {
            console.warn('Player loading timeout - showing error');
            showError();
        }
    }, PLAYER_CONFIG.loadingTimeout);
}

// Initialize player controls
function initPlayerControls() {
    // Refresh button
    const refreshBtn = document.querySelector('[onclick="reloadPlayer()"]');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', reloadPlayer);
    }
    
    // Fullscreen button
    const fullscreenBtn = document.querySelector('[onclick="toggleFullscreen()"]');
    if (fullscreenBtn) {
        fullscreenBtn.addEventListener('click', toggleFullscreen);
    }
    
    // New tab button
    const newTabBtn = document.querySelector('[onclick="openInNewTab()"]');
    if (newTabBtn) {
        newTabBtn.addEventListener('click', openInNewTab);
    }
}

// Player quality detection
function detectPlayerQuality() {
    // This would typically check the player's actual quality
    // For now, we'll simulate it
    const qualityIndicator = document.querySelector('.quality-indicator');
    if (qualityIndicator) {
        // Simulate quality detection
        setTimeout(() => {
            qualityIndicator.textContent = 'HD 1080p';
            qualityIndicator.classList.add('quality-hd');
        }, 3000);
    }
}

// Connection status monitoring
function monitorConnection() {
    let connectionStatus = 'online';
    
    window.addEventListener('online', () => {
        connectionStatus = 'online';
        console.log('Connection restored');
        // Optionally reload player
        if (document.getElementById('error-message').style.display === 'flex') {
            reloadPlayer();
        }
    });
    
    window.addEventListener('offline', () => {
        connectionStatus = 'offline';
        console.log('Connection lost');
        showError();
    });
    
    return connectionStatus;
}

// Player analytics
function trackPlayerEvents() {
    // Track player interactions for analytics
    const events = {
        player_loaded: () => console.log('Player loaded'),
        fullscreen_toggled: () => console.log('Fullscreen toggled'),
        player_reloaded: () => console.log('Player reloaded'),
        new_tab_opened: () => console.log('Opened in new tab')
    };
    
    // You can integrate with Google Analytics or other services here
    return events;
}

// Initialize everything
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Caribbean Advantage TV Live Player Initializing...');
    
    // Initialize all player components
    handlePlayerMessages();
    initKeyboardShortcuts();
    initPlayerControls();
    checkPlayerHealth();
    detectPlayerQuality();
    monitorConnection();
    trackPlayerEvents();
    
    // Set initial viewer count and time
    updateViewerCount();
    if (document.getElementById('current-time')) {
        updateTime();
        setInterval(updateTime, 1000);
    }
    
    // Update viewer count every 30 seconds
    setInterval(updateViewerCount, 30000);
    
    // Add error handling for iframe
    const iframe = document.getElementById('tv-player');
    if (iframe) {
        iframe.addEventListener('error', showError);
        iframe.addEventListener('load', () => {
            console.log('Player iframe loaded');
            setTimeout(hideLoading, 1000);
        });
    }
    
    console.log('✅ Live Player initialized successfully');
});

// Export functions for global access
window.LivePlayer = {
    hideLoading,
    showError,
    reloadPlayer,
    toggleFullscreen,
    openInNewTab,
    updateViewerCount,
    updateTime
};
