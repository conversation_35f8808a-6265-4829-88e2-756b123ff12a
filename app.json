{"name": "Caribbean Advantage TV", "description": "Your Caribbean gateway to gaming, anime, and cartoon entertainment", "repository": "https://github.com/your-username/caribbean-advantage-tv", "logo": "https://your-app-name.herokuapp.com/favicon.ico", "keywords": ["caribbean", "tv", "streaming", "gaming", "anime", "cartoons", "entertainment"], "image": "hero<PERSON>/nodejs", "stack": "heroku-22", "buildpacks": [{"url": "hero<PERSON>/nodejs"}], "formation": {"web": {"quantity": 1, "size": "basic"}}, "addons": [{"plan": "heroku-postgresql:mini", "options": {"version": "14"}}], "env": {"NODE_ENV": {"description": "Node environment", "value": "production"}, "NPM_CONFIG_PRODUCTION": {"description": "Skip dev dependencies", "value": "true"}}, "scripts": {"postdeploy": "echo 'Caribbean Advantage TV deployed successfully!'"}, "success_url": "/", "website": "https://your-app-name.herokuapp.com"}