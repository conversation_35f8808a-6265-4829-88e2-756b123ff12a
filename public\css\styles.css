/* Caribbean Advantage TV - Modern Nature-Inspired Design */

:root {
    /* Nature-Inspired Blue Palette */
    --ocean-blue: #0077BE;
    --sky-blue: #87CEEB;
    --steel-blue: #4682B4;
    --deep-ocean: #003F5C;
    --light-blue: #B0E0E6;
    --powder-blue: #E6F3FF;

    /* Cool Grays & Whites */
    --charcoal: #2C3E50;
    --slate-gray: #708090;
    --light-gray: #F8F9FA;
    --medium-gray: #E9ECEF;
    --white: #FFFFFF;
    --off-white: #FAFBFC;

    /* Accent Colors */
    --coral-accent: #FF6B6B;
    --mint-accent: #4ECDC4;
    --success-green: #28A745;
    --warning-orange: #FFA500;

    /* Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-secondary: 'Poppins', sans-serif;

    /* Spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    --spacing-xxl: 4rem;

    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 119, 190, 0.1);
    --shadow-md: 0 4px 12px rgba(0, 119, 190, 0.15);
    --shadow-lg: 0 8px 24px rgba(0, 119, 190, 0.2);
    --shadow-xl: 0 16px 48px rgba(0, 119, 190, 0.25);

    /* Border Radius */
    --radius-sm: 6px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 24px;

    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* Reset & Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-primary);
    background: linear-gradient(135deg, var(--off-white) 0%, var(--powder-blue) 100%);
    color: var(--charcoal);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-secondary);
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: var(--spacing-sm);
    color: var(--deep-ocean);
}

h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
}

h2 {
    font-size: clamp(2rem, 4vw, 3rem);
}

h3 {
    font-size: clamp(1.5rem, 3vw, 2rem);
}

p {
    margin-bottom: var(--spacing-sm);
    color: var(--slate-gray);
    font-size: 1.1rem;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

@media (max-width: 768px) {
    .container {
        padding: 0 var(--spacing-sm);
    }
}

/* Modern Card Components */
.card {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    padding: var(--spacing-lg);
    transition: all var(--transition-normal);
    border: 1px solid var(--medium-gray);
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--sky-blue);
}

.floating-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-lg);
    transition: all var(--transition-normal);
    border: 1px solid rgba(135, 206, 235, 0.3);
}

.floating-card:hover {
    transform: translateY(-6px) scale(1.02);
    box-shadow: var(--shadow-xl);
    border-color: var(--ocean-blue);
}

/* Modern Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    font-family: var(--font-primary);
    font-weight: 600;
    font-size: 1rem;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(135deg, var(--ocean-blue), var(--steel-blue));
    color: var(--white);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, var(--steel-blue), var(--deep-ocean));
}

.btn-secondary {
    background: var(--white);
    color: var(--ocean-blue);
    border: 2px solid var(--ocean-blue);
}

.btn-secondary:hover {
    background: var(--ocean-blue);
    color: var(--white);
    transform: translateY(-2px);
}

.btn-ghost {
    background: rgba(255, 255, 255, 0.1);
    color: var(--ocean-blue);
    border: 1px solid rgba(0, 119, 190, 0.3);
    backdrop-filter: blur(10px);
}

.btn-ghost:hover {
    background: rgba(0, 119, 190, 0.1);
    border-color: var(--ocean-blue);
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--medium-gray);
    z-index: 1000;
    transition: all var(--transition-normal);
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-md);
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-sm) 0;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-family: var(--font-secondary);
    font-weight: 800;
    font-size: 1.5rem;
    color: var(--deep-ocean);
    text-decoration: none;
}

.nav-logo-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--ocean-blue), var(--sky-blue));
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-weight: 900;
    font-size: 1.2rem;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    list-style: none;
}

.nav-link {
    color: var(--charcoal);
    text-decoration: none;
    font-weight: 500;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--ocean-blue);
    background: var(--powder-blue);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--ocean-blue);
    transition: all var(--transition-fast);
    transform: translateX(-50%);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

/* Mobile Navigation */
.nav-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-xs);
}

.nav-toggle span {
    width: 24px;
    height: 3px;
    background: var(--charcoal);
    border-radius: 2px;
    transition: all var(--transition-fast);
}

@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--white);
        flex-direction: column;
        padding: var(--spacing-lg);
        box-shadow: var(--shadow-lg);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all var(--transition-normal);
    }

    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(6px, 6px);
    }

    .nav-toggle.active span:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(6px, -6px);
    }
}

/* Hero Section */
.hero {
    min-height: 100vh;
    background: linear-gradient(135deg, var(--deep-ocean) 0%, var(--ocean-blue) 50%, var(--steel-blue) 100%);
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    pointer-events: none;
}

.hero-content {
    position: relative;
    z-index: 2;
    color: var(--white);
    text-align: center;
}

.hero h1 {
    color: var(--white);
    margin-bottom: var(--spacing-md);
}

.hero p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.25rem;
    margin-bottom: var(--spacing-xl);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Content Grid */
.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin: var(--spacing-xl) 0;
}

/* Media Player */
.media-player {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    transition: all var(--transition-normal);
}

.media-player:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.media-thumbnail {
    width: 100%;
    height: 200px;
    background: linear-gradient(135deg, var(--sky-blue), var(--ocean-blue));
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: var(--white);
    position: relative;
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--ocean-blue);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.play-button:hover {
    background: var(--white);
    transform: translate(-50%, -50%) scale(1.1);
}

.media-info {
    padding: var(--spacing-md);
}

.media-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--deep-ocean);
    margin-bottom: var(--spacing-xs);
}

.media-description {
    color: var(--slate-gray);
    font-size: 0.95rem;
}

/* Now Playing Bar */
.now-playing {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-top: 1px solid var(--medium-gray);
    padding: var(--spacing-sm) var(--spacing-md);
    z-index: 1000;
    transform: translateY(100%);
    transition: transform var(--transition-normal);
}

.now-playing.active {
    transform: translateY(0);
}

.now-playing-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
}

.now-playing-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.now-playing-artwork {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--ocean-blue), var(--sky-blue));
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.25rem;
}

.now-playing-text h4 {
    font-size: 0.9rem;
    margin: 0;
    color: var(--deep-ocean);
}

.now-playing-text p {
    font-size: 0.8rem;
    margin: 0;
    color: var(--slate-gray);
}

.now-playing-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.control-btn {
    background: none;
    border: none;
    color: var(--ocean-blue);
    font-size: 1.25rem;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.control-btn:hover {
    background: var(--powder-blue);
    transform: scale(1.1);
}

/* Section Styles */
.section {
    padding: var(--spacing-xxl) 0;
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.section-title {
    margin-bottom: var(--spacing-sm);
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--slate-gray);
    max-width: 600px;
    margin: 0 auto;
}

/* Footer */
.footer {
    background: var(--charcoal);
    color: var(--white);
    padding: var(--spacing-xl) 0 var(--spacing-lg);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.footer-section h4 {
    color: var(--white);
    margin-bottom: var(--spacing-sm);
}

.footer-section p,
.footer-section a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.footer-section a:hover {
    color: var(--sky-blue);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: var(--spacing-lg);
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }

.d-flex { display: flex; }
.d-grid { display: grid; }
.d-block { display: block; }
.d-none { display: none; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.align-center { align-items: center; }

.w-full { width: 100%; }
.h-full { height: 100%; }