/* Caribbean Advantage TV - Luxury Modern Design */

:root {
    /* Luxury Color Palette */
    --deep-gold: #D4AF37;
    --rich-gold: #FFD700;
    --warm-black: #0A0A0A;
    --charcoal: #1A1A1A;
    --soft-ivory: #FFF8DC;
    --pearl-white: #F8F6F0;
    --coral: #FF6B6B;
    --teal: #4ECDC4;
    --emerald: #50E3C2;

    /* Gradient Combinations */
    --gradient-luxury: linear-gradient(135deg, var(--deep-gold) 0%, var(--rich-gold) 100%);
    --gradient-dark: linear-gradient(135deg, var(--warm-black) 0%, var(--charcoal) 100%);
    --gradient-tropical: linear-gradient(135deg, var(--coral) 0%, var(--teal) 100%);
    --gradient-hero: linear-gradient(135deg, rgba(10,10,10,0.9) 0%, rgba(26,26,26,0.8) 50%, rgba(10,10,10,0.9) 100%);
    --gradient-glass: linear-gradient(135deg, rgba(255,248,220,0.1) 0%, rgba(212,175,55,0.1) 100%);

    /* Text Colors */
    --text-primary: var(--soft-ivory);
    --text-secondary: rgba(255,248,220,0.8);
    --text-accent: var(--deep-gold);
    --text-dark: var(--warm-black);

    /* Shadows & Effects */
    --shadow-luxury: 0 20px 60px rgba(212,175,55,0.3);
    --shadow-dark: 0 10px 40px rgba(0,0,0,0.5);
    --shadow-glow: 0 0 30px rgba(212,175,55,0.4);
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
    background: var(--warm-black);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
    scroll-behavior: smooth;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
    letter-spacing: -0.02em;
}

h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
}

h2 {
    font-size: clamp(2rem, 4vw, 3rem);
}

p {
    margin-bottom: 1rem;
    color: var(--text-secondary);
    font-weight: 400;
}

/* Container */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

@media (max-width: 768px) {
    .container {
        padding: 0 1rem;
    }
}

/* Luxury Buttons */
.btn-luxury {
    background: var(--gradient-luxury);
    color: var(--warm-black);
    padding: 1rem 2rem;
    border: none;
    border-radius: 50px;
    font-weight: 700;
    font-size: 0.95rem;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    box-shadow: var(--shadow-luxury);
    letter-spacing: 0.02em;
    text-transform: uppercase;
}

.btn-luxury:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: var(--shadow-glow);
    background: linear-gradient(135deg, var(--rich-gold) 0%, var(--deep-gold) 100%);
}

.btn-glass {
    background: rgba(255,248,220,0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(212,175,55,0.3);
    color: var(--text-primary);
    padding: 1rem 2rem;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.4s ease;
    cursor: pointer;
}

.btn-glass:hover {
    background: rgba(212,175,55,0.2);
    border-color: var(--deep-gold);
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(212,175,55,0.2);
}

/* Modern Glassmorphism */
.glass-card {
    background: rgba(255,248,220,0.05);
    backdrop-filter: blur(30px);
    border: 1px solid rgba(212,175,55,0.2);
    border-radius: 24px;
    padding: 2rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.glass-card:hover {
    background: rgba(255,248,220,0.1);
    border-color: rgba(212,175,55,0.4);
    transform: translateY(-5px);
    box-shadow: 0 20px 60px rgba(212,175,55,0.1);
}

/* Floating Cards */
.floating-card {
    background: var(--gradient-glass);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(212,175,55,0.3);
    border-radius: 20px;
    padding: 1.5rem;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.floating-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(212,175,55,0.1), transparent);
    transition: left 0.8s ease;
}

.floating-card:hover::before {
    left: 100%;
}

.floating-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-luxury);
    border-color: var(--deep-gold);
}

/* Hero Section */
.hero-cinematic {
    min-height: 100vh;
    background: linear-gradient(135deg, rgba(10,10,10,0.8) 0%, rgba(26,26,26,0.6) 100%);
    position: relative;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.hero-cinematic::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

/* Modern Media Cards */
.media-card {
    background: var(--charcoal);
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(212,175,55,0.1);
    position: relative;
}

.media-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-luxury);
    opacity: 0;
    transition: opacity 0.4s ease;
    pointer-events: none;
}

.media-card:hover::after {
    opacity: 0.1;
}

.media-card:hover {
    transform: translateY(-10px) scale(1.03);
    box-shadow: var(--shadow-luxury);
    border-color: var(--deep-gold);
}

/* Game Zone Cards */
.game-zone-card {
    background: var(--gradient-dark);
    border-radius: 24px;
    padding: 2rem;
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.game-zone-card::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: var(--gradient-tropical);
    border-radius: 24px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.4s ease;
}

.game-zone-card:hover::before {
    opacity: 1;
}

.game-zone-card:hover {
    transform: translateY(-12px) rotateY(5deg);
    box-shadow: 0 30px 80px rgba(255,107,107,0.3);
}

/* Now Playing Bar */
.now-playing-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(10,10,10,0.95);
    backdrop-filter: blur(30px);
    border-top: 1px solid rgba(212,175,55,0.3);
    padding: 1rem 2rem;
    z-index: 1000;
    transform: translateY(100%);
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.now-playing-bar.active {
    transform: translateY(0);
}

.now-playing-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1400px;
    margin: 0 auto;
}

.now-playing-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.now-playing-artwork {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    background: var(--gradient-luxury);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.now-playing-text h4 {
    color: var(--text-primary);
    font-size: 0.9rem;
    font-weight: 600;
    margin: 0;
}

.now-playing-text p {
    color: var(--text-secondary);
    font-size: 0.8rem;
    margin: 0;
}

.now-playing-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.control-btn {
    background: none;
    border: none;
    color: var(--text-primary);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.control-btn:hover {
    background: rgba(212,175,55,0.2);
    transform: scale(1.1);
}

/* Game Modals */
.game-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.game-modal.active {
    display: flex;
}

.game-iframe {
    width: 90%;
    height: 90%;
    border: none;
    border-radius: 10px;
}

.close-btn {
    position: absolute;
    top: 20px;
    right: 30px;
    background: #ff4757;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: bold;
    cursor: pointer;
    z-index: 1001;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: #ff3742;
    transform: scale(1.1);
}

/* Gradient Text */
.gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* High Contrast Text for Accessibility */
.text-contrast-dark {
    color: var(--text-dark) !important;
}

.text-contrast-light {
    color: var(--text-light) !important;
}

.text-contrast-medium {
    color: var(--text-medium) !important;
}

/* Fix overlapping sections and z-index issues */
section {
    position: relative;
    z-index: 1;
    margin-bottom: 0;
    clear: both;
}

/* Hero section should not overlap */
.hero-bg {
    z-index: 2;
    margin-bottom: 0;
    padding-bottom: 2rem;
}

/* Header stays on top */
header {
    z-index: 50;
}

/* Ensure proper spacing between sections */
.py-20 {
    padding-top: 5rem !important;
    padding-bottom: 5rem !important;
}

/* Fix section overlapping */
section + section {
    margin-top: 0;
    border-top: none;
}

/* Ensure sections don't float over each other */
.bg-light-grey,
.bg-gradient-to-br {
    position: relative;
    z-index: 1;
    display: block;
    width: 100%;
}

/* Mobile responsive fixes */
@media (max-width: 768px) {
    .hero-bg {
        min-height: 80vh;
        padding: 2rem 0;
    }

    .text-5xl {
        font-size: 2.5rem !important;
    }

    .text-7xl {
        font-size: 3.5rem !important;
    }

    .container {
        padding-left: 1rem !important;
        padding-right: 1rem !important;
    }

    /* Fix mobile spacing */
    .py-20 {
        padding-top: 3rem !important;
        padding-bottom: 3rem !important;
    }

    /* Prevent text overflow on mobile */
    h1, h2, h3 {
        word-wrap: break-word;
        overflow-wrap: break-word;
    }

    /* Fix mobile navigation */
    .space-x-8 > * + * {
        margin-left: 1rem !important;
    }

    /* Mobile menu improvements */
    #mobile-menu {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 0 0 20px 20px;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
}