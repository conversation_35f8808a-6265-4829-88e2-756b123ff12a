/* Caribbean Advantage TV - Modern Nature-Inspired Design */

:root {
    /* Nature-Inspired Blue Palette */
    --ocean-blue: #0077BE;
    --sky-blue: #87CEEB;
    --steel-blue: #4682B4;
    --deep-ocean: #003F5C;
    --light-blue: #B0E0E6;
    --powder-blue: #E6F3FF;

    /* Cool Grays & Whites */
    --charcoal: #2C3E50;
    --slate-gray: #708090;
    --light-gray: #F8F9FA;
    --medium-gray: #E9ECEF;
    --white: #FFFFFF;
    --off-white: #FAFBFC;

    /* Accent Colors */
    --coral-accent: #FF6B6B;
    --mint-accent: #4ECDC4;
    --success-green: #28A745;
    --warning-orange: #FFA500;

    /* Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-secondary: 'Poppins', sans-serif;

    /* Spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    --spacing-xxl: 4rem;

    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 119, 190, 0.1);
    --shadow-md: 0 4px 12px rgba(0, 119, 190, 0.15);
    --shadow-lg: 0 8px 24px rgba(0, 119, 190, 0.2);
    --shadow-xl: 0 16px 48px rgba(0, 119, 190, 0.25);

    /* Border Radius */
    --radius-sm: 6px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 24px;

    /* Enhanced Transitions */
    --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* Glass Effects */
    --glass-white: rgba(255, 255, 255, 0.1);
    --glass-dark: rgba(0, 0, 0, 0.2);
    --glass-blur: blur(20px);

    /* Glow Effects */
    --glow-mint: 0 0 20px rgba(78, 205, 196, 0.4);
    --glow-coral: 0 0 20px rgba(255, 107, 107, 0.4);
    --glow-blue: 0 0 20px rgba(0, 119, 190, 0.4);
}

/* Advanced Keyframe Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.05);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: var(--glow-mint);
    }
    50% {
        box-shadow: 0 0 30px rgba(78, 205, 196, 0.6);
    }
}

@keyframes slideInFromTop {
    from {
        opacity: 0;
        transform: translateY(-100%);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes rotateIn {
    from {
        opacity: 0;
        transform: rotate(-180deg) scale(0.5);
    }
    to {
        opacity: 1;
        transform: rotate(0deg) scale(1);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Glass Morphism Effects */
.glass-effect {
    background: var(--glass-white);
    backdrop-filter: var(--glass-blur);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.glass-dark {
    background: var(--glass-dark);
    backdrop-filter: var(--glass-blur);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Enhanced Animation Classes */
.animate-fade-in-up {
    animation: fadeInUp 0.8s var(--transition-normal) forwards;
    opacity: 0;
}

.animate-fade-in-left {
    animation: fadeInLeft 0.8s var(--transition-normal) forwards;
    opacity: 0;
}

.animate-fade-in-right {
    animation: fadeInRight 0.8s var(--transition-normal) forwards;
    opacity: 0;
}

.animate-scale-in {
    animation: scaleIn 0.6s var(--transition-bounce) forwards;
    opacity: 0;
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

.animate-glow {
    animation: glow 2s ease-in-out infinite;
}

.animate-pulse {
    animation: pulse 2s ease-in-out infinite;
}

.animate-shimmer {
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    background-size: 200% 100%;
    animation: shimmer 2s infinite;
}

/* Staggered Animation Delays */
.animate-delay-100 { animation-delay: 0.1s; }
.animate-delay-200 { animation-delay: 0.2s; }
.animate-delay-300 { animation-delay: 0.3s; }
.animate-delay-400 { animation-delay: 0.4s; }
.animate-delay-500 { animation-delay: 0.5s; }
.animate-delay-600 { animation-delay: 0.6s; }
.animate-delay-700 { animation-delay: 0.7s; }
.animate-delay-800 { animation-delay: 0.8s; }

/* Professional Utility Classes */
.text-white { color: var(--white) !important; }
.text-dark { color: var(--charcoal) !important; }
.text-ocean { color: var(--ocean-blue) !important; }
.text-mint { color: var(--mint-accent) !important; }
.text-coral { color: var(--coral-accent) !important; }

.bg-glass {
    background: var(--glass-white);
    backdrop-filter: var(--glass-blur);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.bg-glass-dark {
    background: var(--glass-dark);
    backdrop-filter: var(--glass-blur);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.shadow-glow-mint { box-shadow: var(--glow-mint); }
.shadow-glow-coral { box-shadow: var(--glow-coral); }
.shadow-glow-blue { box-shadow: var(--glow-blue); }

.opacity-90 { opacity: 0.9; }
.opacity-80 { opacity: 0.8; }
.opacity-70 { opacity: 0.7; }
.opacity-60 { opacity: 0.6; }
.opacity-50 { opacity: 0.5; }

.hover-lift {
    transition: all var(--transition-normal);
}

.hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.hover-glow {
    transition: all var(--transition-normal);
}

.hover-glow:hover {
    box-shadow: var(--glow-blue);
}

.text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.text-shadow-strong {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.5);
}

.backdrop-blur {
    backdrop-filter: blur(10px);
}

.backdrop-blur-strong {
    backdrop-filter: blur(20px);
}

/* Professional Grid System */
.grid-auto-fit {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
}

.grid-auto-fill {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

/* Flex Utilities */
.d-flex { display: flex; }
.flex-column { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.align-center { align-items: center; }
.align-start { align-items: flex-start; }
.align-end { align-items: flex-end; }

.w-full { width: 100%; }
.h-full { height: 100%; }

/* Professional Spacing */
.m-0 { margin: 0; }
.mt-auto { margin-top: auto; }
.mb-auto { margin-bottom: auto; }
.mx-auto { margin-left: auto; margin-right: auto; }

.p-0 { padding: 0; }
.px-0 { padding-left: 0; padding-right: 0; }
.py-0 { padding-top: 0; padding-bottom: 0; }

/* Reset & Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-primary);
    background: linear-gradient(135deg, var(--off-white) 0%, var(--powder-blue) 100%);
    color: var(--charcoal);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-secondary);
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: var(--spacing-sm);
    color: var(--deep-ocean);
}

h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 800;
}

h2 {
    font-size: clamp(2rem, 4vw, 3rem);
}

h3 {
    font-size: clamp(1.5rem, 3vw, 2rem);
}

p {
    margin-bottom: var(--spacing-sm);
    color: var(--slate-gray);
    font-size: 1.1rem;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

@media (max-width: 768px) {
    .container {
        padding: 0 var(--spacing-sm);
    }
}

/* Modern Card Components */
.card {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    padding: var(--spacing-lg);
    transition: all var(--transition-normal);
    border: 1px solid var(--medium-gray);
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--sky-blue);
}

.floating-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-lg);
    transition: all var(--transition-normal);
    border: 1px solid rgba(135, 206, 235, 0.3);
}

.floating-card:hover {
    transform: translateY(-6px) scale(1.02);
    box-shadow: var(--shadow-xl);
    border-color: var(--ocean-blue);
}

/* Modern Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    font-family: var(--font-primary);
    font-weight: 600;
    font-size: 1rem;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(135deg, var(--ocean-blue), var(--steel-blue));
    color: var(--white);
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.6s ease;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: var(--shadow-xl), var(--glow-blue);
    background: linear-gradient(135deg, var(--steel-blue), var(--deep-ocean));
}

.btn-secondary {
    background: var(--white);
    color: var(--ocean-blue);
    border: 2px solid var(--ocean-blue);
}

.btn-secondary:hover {
    background: var(--ocean-blue);
    color: var(--white);
    transform: translateY(-2px);
}

.btn-ghost {
    background: rgba(255, 255, 255, 0.1);
    color: var(--ocean-blue);
    border: 1px solid rgba(0, 119, 190, 0.3);
    backdrop-filter: blur(10px);
}

.btn-ghost:hover {
    background: rgba(0, 119, 190, 0.1);
    border-color: var(--ocean-blue);
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--medium-gray);
    z-index: 1000;
    transition: all var(--transition-normal);
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-md);
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-sm) 0;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-family: var(--font-secondary);
    font-weight: 800;
    font-size: 1.5rem;
    color: var(--deep-ocean);
    text-decoration: none;
}

.nav-logo-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--ocean-blue), var(--sky-blue));
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-weight: 900;
    font-size: 1.2rem;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    list-style: none;
}

/* Language Selector */
.language-selector {
    position: relative;
    margin-left: var(--spacing-md);
}

.language-toggle {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    background: var(--glass-white);
    backdrop-filter: var(--glass-blur);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-md);
    padding: var(--spacing-xs) var(--spacing-sm);
    color: var(--charcoal);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all var(--transition-normal);
}

.language-toggle:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.language-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--white);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--medium-gray);
    min-width: 120px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-normal);
    z-index: 1000;
}

.language-selector:hover .language-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.language-option {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    color: var(--charcoal);
    text-decoration: none;
    font-size: 0.9rem;
    transition: all var(--transition-fast);
}

.language-option:hover {
    background: var(--powder-blue);
    color: var(--ocean-blue);
}

.language-option.active {
    background: var(--ocean-blue);
    color: var(--white);
}

.globe-icon {
    font-size: 1rem;
}

.nav-link {
    color: var(--charcoal);
    text-decoration: none;
    font-weight: 500;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--ocean-blue);
    background: var(--powder-blue);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--ocean-blue);
    transition: all var(--transition-fast);
    transform: translateX(-50%);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

/* Mobile Navigation */
.nav-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-xs);
}

.nav-toggle span {
    width: 24px;
    height: 3px;
    background: var(--charcoal);
    border-radius: 2px;
    transition: all var(--transition-fast);
}

@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--white);
        flex-direction: column;
        padding: var(--spacing-lg);
        box-shadow: var(--shadow-lg);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all var(--transition-normal);
    }

    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(6px, 6px);
    }

    .nav-toggle.active span:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(6px, -6px);
    }
}

/* Hero Section */
.hero {
    min-height: 100vh;
    background: linear-gradient(135deg, var(--deep-ocean) 0%, var(--ocean-blue) 50%, var(--steel-blue) 100%);
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    pointer-events: none;
}

.hero-content {
    position: relative;
    z-index: 2;
    color: var(--white);
    text-align: center;
}

.hero h1 {
    color: var(--white);
    margin-bottom: var(--spacing-md);
}

.hero p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.25rem;
    margin-bottom: var(--spacing-xl);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Hero Floating Elements */
.hero-floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.floating-element {
    position: absolute;
    font-size: 2rem;
    opacity: 0.3;
    pointer-events: none;
    z-index: 1;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

/* Content Grid */
.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin: var(--spacing-xl) 0;
}

/* Media Player */
.media-player {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    transition: all var(--transition-normal);
}

.media-player:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.media-thumbnail {
    width: 100%;
    height: 200px;
    background: linear-gradient(135deg, var(--sky-blue), var(--ocean-blue));
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: var(--white);
    position: relative;
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--ocean-blue);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.play-button:hover {
    background: var(--white);
    transform: translate(-50%, -50%) scale(1.1);
}

.media-info {
    padding: var(--spacing-md);
}

.media-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--deep-ocean);
    margin-bottom: var(--spacing-xs);
}

.media-description {
    color: var(--slate-gray);
    font-size: 0.95rem;
}

/* Now Playing Bar */
.now-playing {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-top: 1px solid var(--medium-gray);
    padding: var(--spacing-sm) var(--spacing-md);
    z-index: 1000;
    transform: translateY(100%);
    transition: transform var(--transition-normal);
}

.now-playing.active {
    transform: translateY(0);
}

.now-playing-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
}

.now-playing-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.now-playing-artwork {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--ocean-blue), var(--sky-blue));
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.25rem;
}

.now-playing-text h4 {
    font-size: 0.9rem;
    margin: 0;
    color: var(--deep-ocean);
}

.now-playing-text p {
    font-size: 0.8rem;
    margin: 0;
    color: var(--slate-gray);
}

.now-playing-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.control-btn {
    background: none;
    border: none;
    color: var(--ocean-blue);
    font-size: 1.25rem;
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.control-btn:hover {
    background: var(--powder-blue);
    transform: scale(1.1);
}

/* Section Styles */
.section {
    padding: var(--spacing-xxl) 0;
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.section-title {
    margin-bottom: var(--spacing-sm);
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--slate-gray);
    max-width: 600px;
    margin: 0 auto;
}

/* Footer */
.footer {
    background: var(--charcoal);
    color: var(--white);
    padding: var(--spacing-xl) 0 var(--spacing-lg);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.footer-section h4 {
    color: var(--white);
    margin-bottom: var(--spacing-sm);
}

.footer-section p,
.footer-section a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.footer-section a:hover {
    color: var(--sky-blue);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: var(--spacing-lg);
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }

.d-flex { display: flex; }
.d-grid { display: grid; }
.d-block { display: block; }
.d-none { display: none; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.align-center { align-items: center; }

.w-full { width: 100%; }
.h-full { height: 100%; }