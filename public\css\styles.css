/* Caribbean Advantage TV - Clean Unified Styles */

:root {
    /* Primary Colors */
    --primary-blue: #1E90FF;
    --primary-blue-dark: #1873CC;
    --primary-blue-light: #4FA8FF;
    --primary-blue-ultra-light: #87CEEB;

    /* Neutral Colors */
    --light-grey: #F8F9FA;
    --medium-grey: #E9ECEF;
    --dark-grey: #6C757D;
    --white: #FFFFFF;
    --black: #212529;

    /* Text Colors for Accessibility */
    --text-dark: #1a202c;
    --text-medium: #4a5568;
    --text-light: #ffffff;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-blue-light) 100%);
    --gradient-hero: linear-gradient(135deg, var(--primary-blue-dark) 0%, var(--primary-blue) 50%, var(--primary-blue-light) 100%);
    --gradient-background: linear-gradient(135deg, var(--light-grey) 0%, var(--medium-grey) 100%);
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: var(--gradient-background);
    color: var(--text-dark);
    line-height: 1.6;
    overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
}

p {
    margin-bottom: 1rem;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Buttons */
.btn-primary {
    background: var(--gradient-primary);
    color: var(--text-light);
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.5rem;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    cursor: pointer;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-blue-dark) 0%, var(--primary-blue) 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(30, 144, 255, 0.3);
}

/* Navigation */
.nav-link {
    text-decoration: none;
    transition: color 0.3s ease;
}

.nav-link:hover {
    text-decoration: none;
}

/* Hero Background */
.hero-bg {
    background: var(--gradient-hero);
    position: relative;
    overflow: hidden;
}

/* Glassmorphism Effect */
.glassmorphism {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Cards */
.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Game Cards */
.game-card {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid var(--medium-grey);
}

.game-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Banner Cards */
.banner-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 1rem;
    padding: 1.5rem;
    text-align: center;
    color: white;
    transition: all 0.3s ease;
}

.banner-card:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-3px);
}

/* Game Modals */
.game-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.game-modal.active {
    display: flex;
}

.game-iframe {
    width: 90%;
    height: 90%;
    border: none;
    border-radius: 10px;
}

.close-btn {
    position: absolute;
    top: 20px;
    right: 30px;
    background: #ff4757;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: bold;
    cursor: pointer;
    z-index: 1001;
    transition: all 0.3s ease;
}

.close-btn:hover {
    background: #ff3742;
    transform: scale(1.1);
}

/* Gradient Text */
.gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* High Contrast Text for Accessibility */
.text-contrast-dark {
    color: var(--text-dark) !important;
}

.text-contrast-light {
    color: var(--text-light) !important;
}

.text-contrast-medium {
    color: var(--text-medium) !important;
}

/* Fix overlapping sections and z-index issues */
section {
    position: relative;
    z-index: 1;
    margin-bottom: 0;
    clear: both;
}

/* Hero section should not overlap */
.hero-bg {
    z-index: 2;
    margin-bottom: 0;
    padding-bottom: 2rem;
}

/* Header stays on top */
header {
    z-index: 50;
}

/* Ensure proper spacing between sections */
.py-20 {
    padding-top: 5rem !important;
    padding-bottom: 5rem !important;
}

/* Fix section overlapping */
section + section {
    margin-top: 0;
    border-top: none;
}

/* Ensure sections don't float over each other */
.bg-light-grey,
.bg-gradient-to-br {
    position: relative;
    z-index: 1;
    display: block;
    width: 100%;
}

/* Mobile responsive fixes */
@media (max-width: 768px) {
    .hero-bg {
        min-height: 80vh;
        padding: 2rem 0;
    }

    .text-5xl {
        font-size: 2.5rem !important;
    }

    .text-7xl {
        font-size: 3.5rem !important;
    }

    .container {
        padding-left: 1rem !important;
        padding-right: 1rem !important;
    }

    /* Fix mobile spacing */
    .py-20 {
        padding-top: 3rem !important;
        padding-bottom: 3rem !important;
    }

    /* Prevent text overflow on mobile */
    h1, h2, h3 {
        word-wrap: break-word;
        overflow-wrap: break-word;
    }

    /* Fix mobile navigation */
    .space-x-8 > * + * {
        margin-left: 1rem !important;
    }

    /* Mobile menu improvements */
    #mobile-menu {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 0 0 20px 20px;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
}