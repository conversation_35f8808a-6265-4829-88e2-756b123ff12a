{"name": "caribbean-advantage-tv-nextjs", "version": "2.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start"}, "dependencies": {"framer-motion": "^10.16.16", "lucide-react": "^0.294.0", "next": "14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/react": "19.1.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "typescript": "^5.3.3"}}