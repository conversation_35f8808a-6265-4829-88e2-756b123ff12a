{"name": "caribbean-advantage-tv", "version": "1.0.0", "description": "Caribbean Advantage TV - Your gateway to gaming, anime, and cartoon entertainment with live streaming", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js --watch server.js --watch views --watch public", "build": "echo 'No build step required - static assets served from public/'", "test": "echo 'No tests specified'", "lint": "echo 'No linting configured'", "heroku-postbuild": "echo 'Caribbean Advantage TV build complete'", "local": "npm install && npm start"}, "engines": {"node": "18.x", "npm": "9.x"}, "dependencies": {"express": "^4.18.2", "compression": "^1.7.4", "helmet": "^7.1.0", "cors": "^2.8.5", "morgan": "^1.10.0", "serve-static": "^1.15.0", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["caribbean", "tv", "streaming", "live-tv", "gaming", "anime", "cartoons", "entertainment", "<PERSON><PERSON>", "express", "nodejs"], "author": {"name": "Caribbean Advantage TV", "email": "<EMAIL>", "url": "https://github.com/joelgriiyo"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/joelgriiyo/caribbeanadvantage.git"}, "bugs": {"url": "https://github.com/joelgriiyo/caribbeanadvantage/issues"}, "homepage": "https://github.com/joelgriiyo/caribbeanadvantage#readme", "directories": {"views": "./views", "public": "./public"}, "files": ["server.js", "views/", "public/", "Procfile", "app.json", "README.md"]}