#!/bin/bash

# Caribbean Advantage TV - Heroku Deployment Script
echo "🚀 Starting Caribbean Advantage TV deployment to Heroku..."

# Check if Heroku CLI is installed
if ! command -v heroku &> /dev/null; then
    echo "❌ Heroku CLI is not installed. Please install it first:"
    echo "   https://devcenter.heroku.com/articles/heroku-cli"
    exit 1
fi

# Check if user is logged in to Heroku
if ! heroku auth:whoami &> /dev/null; then
    echo "🔐 Please login to Hero<PERSON> first:"
    heroku login
fi

# Get app name from user
read -p "📝 Enter your Heroku app name (or press Enter for auto-generated): " APP_NAME

# Create Heroku app
if [ -z "$APP_NAME" ]; then
    echo "🎲 Creating Heroku app with auto-generated name..."
    heroku create
else
    echo "🎯 Creating Heroku app: $APP_NAME"
    heroku create $APP_NAME
fi

# Set environment variables
echo "⚙️ Setting environment variables..."
heroku config:set NODE_ENV=production
heroku config:set NPM_CONFIG_PRODUCTION=true

# Add buildpack (if not already set)
echo "🔧 Setting Node.js buildpack..."
heroku buildpacks:set heroku/nodejs

# Initialize git if not already initialized
if [ ! -d ".git" ]; then
    echo "📦 Initializing Git repository..."
    git init
    git add .
    git commit -m "Initial commit - Caribbean Advantage TV"
fi

# Deploy to Heroku
echo "🚀 Deploying to Heroku..."
git add .
git commit -m "Deploy Caribbean Advantage TV - $(date)"
git push heroku main

# Open the app
echo "🌐 Opening your app in the browser..."
heroku open

echo "✅ Deployment complete!"
echo "📺 Your Caribbean Advantage TV is now live!"
echo "🔗 You can also visit: https://$(heroku apps:info --json | jq -r '.app.web_url')"
echo ""
echo "📋 Useful commands:"
echo "   heroku logs --tail    # View live logs"
echo "   heroku restart        # Restart the app"
echo "   heroku open           # Open in browser"
