<!DOCTYPE html>
<html lang="en-us">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Unity WebGL Player | Guara-Mania</title>
  </head>
  <body style="text-align: center; padding: 0; border: 0; margin: 0;">
    <canvas id="unity-canvas" width=960 height=600 style="width: 960px; height: 600px; background: url('Build/GuaraMania.jpg') center / cover"></canvas>
    <script src="Build/GuaraMania.loader.js"></script>
    <script>
      if (/iPhone|iPad|iPod|Android/i.test(navigator.userAgent)) {
        // Mobile device style: fill the whole browser client area with the game canvas:
        var meta = document.createElement('meta');
        meta.name = 'viewport';
        meta.content = 'width=device-width, height=device-height, initial-scale=1.0, user-scalable=no, shrink-to-fit=yes';
        document.getElementsByTagName('head')[0].appendChild(meta);

        var canvas = document.querySelector("#unity-canvas");
        canvas.style.width = "100%";
        canvas.style.height = "100%";
        canvas.style.position = "fixed";

        document.body.style.textAlign = "left";
      }

      createUnityInstance(document.querySelector("#unity-canvas"), {
        dataUrl: "Build/GuaraMania.data",
        frameworkUrl: "Build/GuaraMania.framework.js",
        codeUrl: "Build/GuaraMania.wasm",
        streamingAssetsUrl: "StreamingAssets",
        companyName: "Gamestorme",
        productName: "Guara-Mania",
        productVersion: "1.0.1",
        // matchWebGLToCanvasSize: false, // Uncomment this to separately control WebGL canvas render size and DOM element size.
        // devicePixelRatio: 1, // Uncomment this to override low DPI rendering on high DPI displays.
      });
    </script>
  </body>
</html>
