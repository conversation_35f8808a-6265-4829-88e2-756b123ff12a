

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Caribbean Advantage TV - Gaming, Anime & Cartoons</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'ca-blue': '#003d6b',
                        'ca-dark-blue': '#001f3f',
                        'ca-light-blue': '#0066cc',
                        'ca-accent-blue': '#4a90e2',
                        'ca-yellow': '#ffc107',
                        'ca-orange': '#ff9800',
                        'ca-green': '#4caf50',
                        'ca-dark': '#1a1a1a',
                        'ca-gray': '#2d3748',
                        'ca-light-gray': '#f7fafc',
                    },
                    animation: {
                        'float': 'float 6s ease-in-out infinite',
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'bounce-slow': 'bounce 2s infinite',
                        'fade-in': 'fadeIn 0.8s ease-out',
                        'slide-up': 'slideUp 0.6s ease-out',
                        'scale-in': 'scaleIn 0.5s ease-out',
                    },
                    keyframes: {
                        float: {
                            '0%, 100%': { transform: 'translateY(0px)' },
                            '50%': { transform: 'translateY(-20px)' },
                        },
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(30px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' },
                        },
                        scaleIn: {
                            '0%': { transform: 'scale(0.9)', opacity: '0' },
                            '100%': { transform: 'scale(1)', opacity: '1' },
                        },
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
            color: #1a1a1a;
            overflow-x: hidden;
        }

        /* Loading Animation */
        .page-loader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #003d6b, #001f3f);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.5s ease-out;
        }

        .loader-content {
            text-align: center;
            color: white;
        }

        .loader-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 255, 255, 0.1);
            border-left: 4px solid #ffc107;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .gradient-text {
            background: linear-gradient(135deg, #003d6b, #0066cc, #4a90e2);
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            background-size: 200% 200%;
            animation: gradient-shift 3s ease infinite;
        }

        @keyframes gradient-shift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .card-hover {
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
        }

        .card-hover::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .card-hover:hover::before {
            left: 100%;
        }

        .card-hover:hover {
            transform: translateY(-15px) scale(1.02);
            box-shadow:
                0 25px 50px -12px rgba(0, 61, 107, 0.25),
                0 0 0 1px rgba(255, 255, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        .glassmorphism {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
        }

        .nav-link {
            position: relative;
            overflow: hidden;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 61, 107, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .nav-link:hover::before {
            left: 100%;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 3px;
            bottom: -2px;
            left: 50%;
            background: linear-gradient(90deg, #003d6b, #0066cc, #4a90e2);
            transition: all 0.3s ease;
            transform: translateX(-50%);
            border-radius: 2px;
        }

        .nav-link:hover::after {
            width: 100%;
        }

        .hero-bg {
            background:
                linear-gradient(135deg, rgba(0, 61, 107, 0.95), rgba(0, 31, 63, 0.9), rgba(0, 102, 204, 0.8)),
                radial-gradient(circle at 20% 80%, rgba(255, 193, 7, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(74, 144, 226, 0.1) 0%, transparent 50%),
                url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-3.134-3-7-3s-7 3.134-7 7 3.134 7 7 7zm-44 14c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4z' fill='%23ffffff' fill-opacity='0.05'/%3E%3C/svg%3E");
            background-size: cover, 400px 400px, 300px 300px, 100px 100px;
            background-position: center, 0% 0%, 100% 100%, center;
            position: relative;
        }

        .hero-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 30%, rgba(255, 193, 7, 0.05) 50%, transparent 70%);
            animation: shimmer 8s ease-in-out infinite;
        }

        @keyframes shimmer {
            0%, 100% { transform: translateX(-100%); }
            50% { transform: translateX(100%); }
        }

        .schedule-item {
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
        }

        .schedule-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 61, 107, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .schedule-item:hover::before {
            left: 100%;
        }

        .schedule-item:hover {
            background: linear-gradient(135deg, rgba(0, 61, 107, 0.1), rgba(0, 102, 204, 0.05));
            transform: scale(1.03) translateX(5px);
            box-shadow: 0 10px 25px -5px rgba(0, 61, 107, 0.2);
            border-left: 4px solid #003d6b;
        }

        .program-tag {
            background: linear-gradient(135deg, #003d6b, #0066cc, #4a90e2);
            border-radius: 9999px;
            padding: 0.25rem 0.75rem;
            font-size: 0.75rem;
            font-weight: 600;
            color: white;
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 61, 107, 0.3);
        }

        .program-tag::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .program-tag:hover::before {
            left: 100%;
        }

        .scroll-animation {
            opacity: 0;
            transform: translateY(30px);
            transition: opacity 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275), transform 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        .scroll-animation.active {
            opacity: 1;
            transform: translateY(0);
        }

        .scroll-animation:nth-child(even) {
            transform: translateY(30px) translateX(-20px);
        }

        .scroll-animation:nth-child(even).active {
            transform: translateY(0) translateX(0);
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            25% { transform: translateY(-15px) rotate(1deg); }
            50% { transform: translateY(-10px) rotate(0deg); }
            75% { transform: translateY(-20px) rotate(-1deg); }
        }

        .float-animation {
            animation: float 6s ease-in-out infinite;
        }

        .mobile-menu {
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            transform: translateX(-100%);
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }

        .mobile-menu.active {
            transform: translateX(0);
        }

        .caribbean-pattern {
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23003d6b' fill-opacity='0.08'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }

        /* Particle Animation */
        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            pointer-events: none;
        }

        .particle {
            position: absolute;
            background: rgba(255, 193, 7, 0.6);
            border-radius: 50%;
            animation: particle-float 15s infinite linear;
        }

        @keyframes particle-float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) rotate(360deg);
                opacity: 0;
            }
        }

        /* Advanced Button Styles */
        .btn-primary {
            background: linear-gradient(135deg, #003d6b, #0066cc);
            border: none;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 61, 107, 0.3);
        }

        /* Counter Animation */
        .counter {
            font-weight: bold;
            color: #003d6b;
        }

        /* Smooth Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #003d6b, #0066cc);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #001f3f, #003d6b);
        }
    </style>
</head>
<body>
    <!-- Loading Screen -->
    <div id="page-loader" class="page-loader">
        <div class="loader-content">
            <div class="loader-spinner"></div>
            <h2 class="text-2xl font-bold mb-2">Caribbean Advantage TV</h2>
            <p class="text-lg opacity-80">Loading your entertainment...</p>
        </div>
    </div>

    <!-- Particles Background -->
    <div class="particles" id="particles"></div>

    <!-- Header -->
    <header class="fixed top-0 left-0 right-0 z-50 glassmorphism shadow-lg border-b border-white/20">
        <div class="container mx-auto px-4 py-3">
            <div class="flex justify-between items-center">
                <div class="flex items-center">
                    <div class="text-2xl font-bold">
                        <span class="text-ca-blue">Caribbean</span> <span class="text-ca-orange">Advantage</span> <span class="text-ca-blue">TV</span>
                    </div>
                </div>

                <!-- Desktop Navigation -->
                <nav class="hidden md:flex space-x-8">
                    <a href="index.html" class="nav-link text-white hover:text-ca-yellow transition-colors font-medium">Home</a>
                    <a href="shows.html" class="nav-link text-white hover:text-ca-yellow transition-colors font-medium">Shows</a>
                    <a href="schedule.html" class="nav-link text-white hover:text-ca-yellow transition-colors font-medium">Schedule</a>
                    <a href="about.html" class="nav-link text-white hover:text-ca-yellow transition-colors font-medium">About</a>
                    <a href="contact.html" class="nav-link text-white hover:text-ca-yellow transition-colors font-medium">Contact</a>
                    <a href="news.html" class="nav-link text-white hover:text-ca-yellow transition-colors font-medium">News</a>
                </nav>

                <div class="hidden md:flex items-center space-x-4">
                    <a href="live.html" class="btn-primary text-white px-6 py-2 rounded-lg font-medium">
                        Watch Live
                    </a>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="menu-toggle" class="text-white focus:outline-none">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div id="mobile-menu" class="mobile-menu fixed inset-y-0 left-0 w-64 shadow-2xl md:hidden">
            <div class="p-5">
                <div class="flex justify-between items-center mb-6">
                    <div class="text-xl font-bold">
                        <span class="text-ca-blue">Caribbean</span> <span class="text-ca-orange">TV</span>
                    </div>
                    <button id="close-menu" class="text-ca-blue">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <nav class="flex flex-col space-y-4">
                    <a href="index.html" class="text-ca-dark hover:text-ca-blue transition-colors font-medium">Home</a>
                    <a href="shows.html" class="text-ca-dark hover:text-ca-blue transition-colors font-medium">Shows</a>
                    <a href="schedule.html" class="text-ca-dark hover:text-ca-blue transition-colors font-medium">Schedule</a>
                    <a href="about.html" class="text-ca-dark hover:text-ca-blue transition-colors font-medium">About</a>
                    <a href="contact.html" class="text-ca-dark hover:text-ca-blue transition-colors font-medium">Contact</a>
                    <a href="news.html" class="text-ca-dark hover:text-ca-blue transition-colors font-medium">News</a>
                    <a href="live.html" class="btn-primary text-white px-4 py-2 rounded-lg font-medium mt-4 block text-center">
                        Watch Live
                    </a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section id="home" class="hero-bg min-h-screen flex items-center pt-16 relative overflow-hidden">
        <div class="container mx-auto px-4 py-16 relative z-10">
            <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-1/2 mb-10 md:mb-0 scroll-animation">
                    <h1 class="text-4xl md:text-6xl font-bold mb-6 text-white leading-tight">
                        Your Caribbean Gateway to
                        <span class="gradient-text bg-gradient-to-r from-ca-yellow via-ca-orange to-ca-yellow bg-clip-text text-transparent animate-pulse-slow">Gaming</span>,
                        <span class="gradient-text bg-gradient-to-r from-ca-yellow via-ca-orange to-ca-yellow bg-clip-text text-transparent animate-pulse-slow">Anime</span> &
                        <span class="gradient-text bg-gradient-to-r from-ca-yellow via-ca-orange to-ca-yellow bg-clip-text text-transparent animate-pulse-slow">Cartoons</span>
                    </h1>
                    <p class="text-xl text-white/90 mb-8 leading-relaxed">
                        Experience the best entertainment across gaming, anime, and cartoons with a unique Caribbean flavor. Stream your favorite shows anytime, anywhere.
                    </p>
                    <div class="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-6">
                        <a href="live.html" class="bg-gradient-to-r from-ca-yellow to-ca-orange hover:from-ca-orange hover:to-ca-yellow text-ca-dark px-8 py-4 rounded-lg transition-all duration-300 font-bold text-lg shadow-lg hover:shadow-xl transform hover:scale-105 inline-block">
                            🎬 Watch Now
                        </a>
                        <button class="glassmorphism border border-white/30 text-white hover:bg-white/20 px-8 py-4 rounded-lg transition-all duration-300 font-medium text-lg backdrop-blur-sm">
                            📅 View Schedule
                        </button>
                    </div>
                </div>
                <div class="md:w-1/2 flex justify-center">
                    <div class="relative w-full max-w-md">
                        <!-- TV Frame SVG -->
                        <svg class="w-full h-auto float-animation" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
                            <!-- TV Body -->
                            <rect x="50" y="30" width="300" height="200" rx="10" fill="#333333" stroke="#555555" stroke-width="4"/>
                            <!-- TV Screen -->
                            <rect x="70" y="50" width="260" height="160" rx="5" fill="#111111"/>
                            <!-- TV Stand -->
                            <path d="M150 230 L250 230 L270 260 L130 260 Z" fill="#333333" stroke="#555555" stroke-width="2"/>

                            <!-- Animated Content on Screen -->
                            <g>
                                <!-- Gaming Icon -->
                                <rect x="90" y="70" width="60" height="60" rx="5" fill="#0071bc" opacity="0.8"/>
                                <path d="M110 90 L110 110 M100 100 L120 100" stroke="white" stroke-width="4" stroke-linecap="round"/>
                                <circle cx="130" cy="90" r="5" fill="white"/>
                                <circle cx="140" cy="100" r="5" fill="white"/>

                                <!-- Anime Character -->
                                <circle cx="190" cy="100" r="25" fill="#ffc107" opacity="0.8"/>
                                <path d="M180 95 L185 95 M195 95 L200 95" stroke="white" stroke-width="2" stroke-linecap="round"/>
                                <path d="M190 105 C193 110, 187 110, 190 105" stroke="white" stroke-width="2" fill="none"/>

                                <!-- Cartoon Cloud -->
                                <path d="M260 80 C270 70, 290 70, 295 80 C305 70, 325 75, 320 90 C330 100, 320 115, 305 110 C300 120, 280 120, 275 110 C265 115, 250 105, 260 80" fill="#ff9800" opacity="0.8"/>
                                <text x="290" y="100" font-size="20" fill="white" text-anchor="middle">!</text>
                            </g>

                            <!-- Caribbean Flag Colors as TV Controls -->
                            <circle cx="200" cy="220" r="5" fill="#ffc107"/>
                            <rect x="180" y="215" width="10" height="10" rx="2" fill="#4caf50"/>
                            <rect x="210" y="215" width="10" height="10" rx="2" fill="#0071bc"/>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Shows Section -->
    <section id="shows" class="py-20 bg-gradient-to-br from-ca-light-gray to-white relative">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16 scroll-animation">
                <h2 class="text-3xl md:text-4xl font-bold mb-4">Featured <span class="gradient-text">Shows</span></h2>
                <p class="text-ca-dark max-w-2xl mx-auto text-lg">Discover our most popular shows across gaming, anime, and cartoons with a unique Caribbean perspective.</p>
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Show Card 1 -->
                <div class="bg-white rounded-xl overflow-hidden shadow-lg card-hover border border-gray-200 scroll-animation">
                    <div class="h-48 bg-gradient-to-r from-ca-blue to-ca-light-blue relative">
                        <div class="absolute inset-0 flex items-center justify-center">
                            <svg class="w-24 h-24 text-white/80 animate-bounce-slow" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M21 6H3c-1.1 0-2 .9-2 2v8c0 1.1.9 2 2 2h18c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zm-1 10H4c-.55 0-1-.45-1-1V9c0-.55.45-1 1-1h16c.55 0 1 .45 1 1v6c0 .55-.45 1-1 1z"/>
                                <path d="M15 11l-5 3V8z"/>
                            </svg>
                        </div>
                        <div class="absolute top-2 left-2">
                            <span class="program-tag">🎮 Gaming</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2 text-ca-dark">Caribbean Gamer Challenge</h3>
                        <p class="text-gray-600 mb-4">Watch top Caribbean gamers compete in the ultimate gaming championship across multiple genres.</p>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-500 font-medium">⏰ Weekdays 8PM</span>
                            <button class="text-ca-blue hover:text-ca-dark-blue font-bold flex items-center transition-all duration-300 hover:scale-105">
                                Watch Now
                                <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Show Card 2 -->
                <div class="bg-white rounded-xl overflow-hidden shadow-lg card-hover border border-gray-200 scroll-animation">
                    <div class="h-48 bg-gradient-to-r from-ca-yellow to-ca-orange relative">
                        <div class="absolute inset-0 flex items-center justify-center">
                            <svg class="w-24 h-24 text-white/80 animate-pulse-slow" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                            </svg>
                        </div>
                        <div class="absolute top-2 left-2">
                            <span class="program-tag">🎌 Anime</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2 text-ca-dark">Island Anime Hour</h3>
                        <p class="text-gray-600 mb-4">The best anime series with Caribbean commentary and cultural insights you won't find anywhere else.</p>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-500 font-medium">⏰ Weekends 9PM</span>
                            <button class="text-ca-blue hover:text-ca-dark-blue font-bold flex items-center transition-all duration-300 hover:scale-105">
                                Watch Now
                                <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Show Card 3 -->
                <div class="bg-white rounded-xl overflow-hidden shadow-lg card-hover border border-gray-200 scroll-animation">
                    <div class="h-48 bg-gradient-to-r from-ca-green to-ca-light-blue relative">
                        <div class="absolute inset-0 flex items-center justify-center">
                            <svg class="w-24 h-24 text-white/80 animate-bounce-slow" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                            </svg>
                        </div>
                        <div class="absolute top-2 left-2">
                            <span class="program-tag">🎨 Cartoon</span>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-2 text-ca-dark">Caribbean Toons</h3>
                        <p class="text-gray-600 mb-4">Original Caribbean cartoons and international favorites with a local twist that kids and adults will love.</p>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-500 font-medium">⏰ Daily 5PM</span>
                            <button class="text-ca-blue hover:text-ca-dark-blue font-bold flex items-center transition-all duration-300 hover:scale-105">
                                Watch Now
                                <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-12">
                <button class="bg-white hover:bg-gray-100 text-ca-blue border border-ca-blue px-6 py-3 rounded-md transition-colors font-medium">
                    View All Shows
                </button>
            </div>
        </div>
    </section>

    <!-- Schedule Section -->
    <section id="schedule" class="py-20 bg-gray-100 caribbean-pattern">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold mb-4">Today's <span class="text-ca-blue">Schedule</span></h2>
                <p class="text-ca-dark max-w-2xl mx-auto">Check out what's playing today on Caribbean Advantage TV. Never miss your favorite shows again!</p>
            </div>

            <div class="bg-white rounded-xl shadow-lg overflow-hidden max-w-4xl mx-auto">
                <div class="grid grid-cols-1 md:grid-cols-3 divide-y md:divide-y-0 md:divide-x divide-gray-200">
                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-4 text-ca-blue">Morning</h3>
                        <ul class="space-y-4">
                            <li class="schedule-item p-3 rounded-lg">
                                <div class="flex justify-between mb-1">
                                    <span class="font-medium">Caribbean Sunrise</span>
                                    <span class="text-sm text-gray-500">8:00 AM</span>
                                </div>
                                <p class="text-sm text-gray-500">Morning news and entertainment</p>
                            </li>
                            <li class="schedule-item p-3 rounded-lg">
                                <div class="flex justify-between mb-1">
                                    <span class="font-medium">Gaming News</span>
                                    <span class="text-sm text-gray-500">10:00 AM</span>
                                </div>
                                <p class="text-sm text-gray-500">Latest updates from the gaming world</p>
                            </li>
                            <li class="schedule-item p-3 rounded-lg">
                                <div class="flex justify-between mb-1">
                                    <span class="font-medium">Anime Marathon</span>
                                    <span class="text-sm text-gray-500">11:30 AM</span>
                                </div>
                                <p class="text-sm text-gray-500">Back-to-back episodes of top anime</p>
                            </li>
                        </ul>
                    </div>

                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-4 text-ca-orange">Afternoon</h3>
                        <ul class="space-y-4">
                            <li class="schedule-item p-3 rounded-lg">
                                <div class="flex justify-between mb-1">
                                    <span class="font-medium">Island Game Reviews</span>
                                    <span class="text-sm text-gray-500">1:00 PM</span>
                                </div>
                                <p class="text-sm text-gray-500">In-depth analysis of new releases</p>
                            </li>
                            <li class="schedule-item p-3 rounded-lg">
                                <div class="flex justify-between mb-1">
                                    <span class="font-medium">Caribbean Toons</span>
                                    <span class="text-sm text-gray-500">3:00 PM</span>
                                </div>
                                <p class="text-sm text-gray-500">Season 2, Episode 5</p>
                            </li>
                            <li class="schedule-item p-3 rounded-lg">
                                <div class="flex justify-between mb-1">
                                    <span class="font-medium">Anime Spotlight</span>
                                    <span class="text-sm text-gray-500">4:30 PM</span>
                                </div>
                                <p class="text-sm text-gray-500">Featured anime of the week</p>
                            </li>
                        </ul>
                    </div>

                    <div class="p-6">
                        <h3 class="text-xl font-bold mb-4 text-ca-green">Evening</h3>
                        <ul class="space-y-4">
                            <li class="schedule-item p-3 rounded-lg">
                                <div class="flex justify-between mb-1">
                                    <span class="font-medium">Caribbean Gamer Challenge</span>
                                    <span class="text-sm text-gray-500">7:00 PM</span>
                                </div>
                                <p class="text-sm text-gray-500">Semi-finals tournament</p>
                            </li>
                            <li class="schedule-item p-3 rounded-lg">
                                <div class="flex justify-between mb-1">
                                    <span class="font-medium">Island Anime Hour</span>
                                    <span class="text-sm text-gray-500">9:00 PM</span>
                                </div>
                                <p class="text-sm text-gray-500">Season 1, Episode 8</p>
                            </li>
                            <li class="schedule-item p-3 rounded-lg">
                                <div class="flex justify-between mb-1">
                                    <span class="font-medium">Late Night Gaming</span>
                                    <span class="text-sm text-gray-500">11:00 PM</span>
                                </div>
                                <p class="text-sm text-gray-500">Live gameplay with commentary</p>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="text-center mt-12">
                <button class="bg-white hover:bg-gray-100 text-ca-blue border border-ca-blue px-6 py-3 rounded-md transition-colors font-medium">
                    View Full Schedule
                </button>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-20 bg-white">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-1/2 mb-10 md:mb-0 md:pr-10">
                    <h2 class="text-3xl md:text-4xl font-bold mb-6">About <span class="text-ca-blue">Caribbean Advantage TV</span></h2>
                    <p class="text-gray-600 mb-6">
                        Caribbean Advantage TV is your premier destination for all things gaming, anime, and cartoons with a unique Caribbean perspective. We're passionate about bringing you the best entertainment across these creative mediums.
                    </p>
                    <p class="text-gray-600 mb-6">
                        Founded by a team of Caribbean enthusiasts, our mission is to create a space where fans can discover new content, revisit classics, and connect with a community that shares their interests while celebrating our Caribbean heritage.
                    </p>
                    <div class="grid grid-cols-2 gap-4 mt-8">
                        <div class="bg-gray-100 p-4 rounded-lg text-center">
                            <div class="text-3xl font-bold text-ca-blue mb-2">500+</div>
                            <div class="text-sm text-gray-600">Hours of Content</div>
                        </div>
                        <div class="bg-gray-100 p-4 rounded-lg text-center">
                            <div class="text-3xl font-bold text-ca-orange mb-2">50+</div>
                            <div class="text-sm text-gray-600">Original Shows</div>
                        </div>
                        <div class="bg-gray-100 p-4 rounded-lg text-center">
                            <div class="text-3xl font-bold text-ca-green mb-2">24/7</div>
                            <div class="text-sm text-gray-600">Live Streaming</div>
                        </div>
                        <div class="bg-gray-100 p-4 rounded-lg text-center">
                            <div class="text-3xl font-bold text-ca-blue mb-2">1M+</div>
                            <div class="text-sm text-gray-600">Monthly Viewers</div>
                        </div>
                    </div>
                </div>
                <div class="md:w-1/2 flex justify-center">
                    <div class="relative w-full max-w-md">
                        <svg class="w-full h-auto" viewBox="0 0 400 400" xmlns="http://www.w3.org/2000/svg">
                            <!-- Background Circle -->
                            <circle cx="200" cy="200" r="180" fill="#f8f9fa" stroke="#e9ecef" stroke-width="2" />

                            <!-- Caribbean Map Outline -->
                            <path d="M120,160 C140,140 180,150 200,140 C220,130 240,120 270,130 C300,140 310,170 300,200 C290,230 270,250 240,260 C210,270 180,260 160,240 C140,220 100,180 120,160 Z"
                                  fill="#0071bc" fill-opacity="0.1" stroke="#0071bc" stroke-width="2" />

                            <!-- Gaming Icon -->
                            <g transform="translate(100, 100)">
                                <rect x="0" y="0" width="80" height="80" rx="10" fill="#0071bc" opacity="0.8"/>
                                <path d="M30 30 L30 50 M20 40 L40 40" stroke="white" stroke-width="6" stroke-linecap="round"/>
                                <circle cx="60" cy="25" r="7" fill="white"/>
                                <circle cx="60" cy="55" r="7" fill="white"/>
                            </g>

                            <!-- Anime Icon -->
                            <g transform="translate(220, 100)">
                                <circle cx="40" cy="40" r="40" fill="#ffc107" opacity="0.8"/>
                                <path d="M25 30 L35 30 M45 30 L55 30" stroke="white" stroke-width="3" stroke-linecap="round"/>
                                <path d="M40 50 C45 60, 35 60, 40 50" stroke="white" stroke-width="3" fill="none"/>
                                <path d="M20 15 L30 25 M60 15 L50 25" stroke="white" stroke-width="3" stroke-linecap="round"/>
                            </g>

                            <!-- Cartoon Icon -->
                            <g transform="translate(160, 220)">
                                <path d="M40 0 C60 0, 80 20, 80 40 C80 60, 60 80, 40 80 C20 80, 0 60, 0 40 C0 20, 20 0, 40 0" fill="#4caf50" opacity="0.8"/>
                                <path d="M25 30 L35 30 M45 30 L55 30" stroke="white" stroke-width="3" stroke-linecap="round"/>
                                <path d="M40 50 C50 65, 30 65, 40 50" stroke="white" stroke-width="3" fill="white"/>
                            </g>

                            <!-- Connecting Lines -->
                            <path d="M140 140 L220 140" stroke="#0071bc" stroke-width="3" stroke-dasharray="5,5"/>
                            <path d="M140 140 L180 220" stroke="#0071bc" stroke-width="3" stroke-dasharray="5,5"/>
                            <path d="M260 140 L220 220" stroke="#0071bc" stroke-width="3" stroke-dasharray="5,5"/>

                            <!-- Central Point -->
                            <circle cx="200" cy="170" r="15" fill="#ff9800"/>
                            <text x="200" y="175" font-size="16" fill="white" text-anchor="middle" font-weight="bold">CA</text>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Newsletter Section -->
    <section class="py-16 bg-ca-blue">
        <div class="container mx-auto px-4">
            <div class="max-w-3xl mx-auto text-center">
                <h2 class="text-3xl font-bold mb-6 text-white">Stay Updated with <span class="text-ca-yellow">Caribbean Advantage TV</span></h2>
                <p class="text-white mb-8">Subscribe to our newsletter to get weekly updates on new shows, special events, and exclusive content.</p>
                <form class="flex flex-col sm:flex-row gap-4 max-w-lg mx-auto">
                    <input type="email" placeholder="Enter your email" class="flex-1 px-4 py-3 rounded-md bg-white border border-gray-300 text-gray-800 focus:outline-none focus:ring-2 focus:ring-ca-yellow">
                    <button type="submit" class="bg-ca-yellow hover:bg-ca-orange text-ca-dark px-6 py-3 rounded-md transition-colors font-medium">
                        Subscribe
                    </button>
                </form>
                <p class="text-sm text-white/80 mt-4">We respect your privacy. Unsubscribe at any time.</p>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-20 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold mb-4">Get in <span class="text-ca-blue">Touch</span></h2>
                <p class="text-gray-600 max-w-2xl mx-auto">Have questions or feedback? We'd love to hear from you!</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-10 max-w-4xl mx-auto">
                <div>
                    <h3 class="text-xl font-bold mb-6 text-ca-blue">Contact Information</h3>
                    <div class="space-y-6">
                        <div class="flex items-start">
                            <div class="bg-gray-100 p-3 rounded-lg mr-4">
                                <svg class="w-6 h-6 text-ca-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-medium mb-1">Email</h4>
                                <p class="text-gray-600"><EMAIL></p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="bg-gray-100 p-3 rounded-lg mr-4">
                                <svg class="w-6 h-6 text-ca-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-medium mb-1">Phone</h4>
                                <p class="text-gray-600">+****************</p>
                            </div>
                        </div>
                        <div class="flex items-start">
                            <div class="bg-gray-100 p-3 rounded-lg mr-4">
                                <svg class="w-6 h-6 text-ca-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <h4 class="font-medium mb-1">Address</h4>
                                <p class="text-gray-600">123 Caribbean Blvd, Suite 456<br>Kingston, Jamaica</p>
                            </div>
                        </div>
                    </div>

                    <h3 class="text-xl font-bold mt-10 mb-6 text-ca-blue">Follow Us</h3>
                    <div class="flex space-x-4">
                        <a href="#" class="bg-gray-100 hover:bg-gray-200 p-3 rounded-lg transition-colors">
                            <svg class="w-6 h-6 text-ca-blue" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                            </svg>
                        </a>
                        <a href="#" class="bg-gray-100 hover:bg-gray-200 p-3 rounded-lg transition-colors">
                            <svg class="w-6 h-6 text-ca-blue" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                            </svg>
                        </a>
                        <a href="#" class="bg-gray-100 hover:bg-gray-200 p-3 rounded-lg transition-colors">
                            <svg class="w-6 h-6 text-ca-blue" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"/>
                            </svg>
                        </a>
                        <a href="#" class="bg-gray-100 hover:bg-gray-200 p-3 rounded-lg transition-colors">
                            <svg class="w-6 h-6 text-ca-blue" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"/>
                            </svg>
                        </a>
                    </div>
                </div>

                <div>
                    <h3 class="text-xl font-bold mb-6 text-ca-blue">Send Us a Message</h3>
                    <form class="space-y-6">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-600 mb-2">Name</label>
                            <input type="text" id="name" class="w-full px-4 py-3 rounded-md bg-gray-100 border border-gray-300 text-gray-800 focus:outline-none focus:ring-2 focus:ring-ca-blue">
                        </div>
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-600 mb-2">Email</label>
                            <input type="email" id="email" class="w-full px-4 py-3 rounded-md bg-gray-100 border border-gray-300 text-gray-800 focus:outline-none focus:ring-2 focus:ring-ca-blue">
                        </div>
                        <div>
                            <label for="subject" class="block text-sm font-medium text-gray-600 mb-2">Subject</label>
                            <input type="text" id="subject" class="w-full px-4 py-3 rounded-md bg-gray-100 border border-gray-300 text-gray-800 focus:outline-none focus:ring-2 focus:ring-ca-blue">
                        </div>
                        <div>
                            <label for="message" class="block text-sm font-medium text-gray-600 mb-2">Message</label>
                            <textarea id="message" rows="4" class="w-full px-4 py-3 rounded-md bg-gray-100 border border-gray-300 text-gray-800 focus:outline-none focus:ring-2 focus:ring-ca-blue"></textarea>
                        </div>
                        <button type="submit" class="w-full bg-ca-blue hover:bg-ca-dark-blue text-white px-6 py-3 rounded-md transition-colors font-medium">
                            Send Message
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-ca-dark text-white py-12">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-10">
                <div>
                    <div class="text-2xl font-bold mb-6">
                        <span class="text-ca-blue">Caribbean</span> <span class="text-ca-orange">Advantage</span> <span class="text-ca-blue">TV</span>
                    </div>
                    <p class="text-gray-300 mb-6">Your Caribbean gateway to gaming, anime, and cartoon entertainment.</p>
                    <p class="text-gray-400 text-sm">© 2023 Caribbean Advantage TV. All rights reserved.</p>
                </div>

                <div>
                    <h4 class="text-lg font-bold mb-6 text-white">Quick Links</h4>
                    <ul class="space-y-3">
                        <li><a href="#home" class="text-gray-300 hover:text-ca-yellow transition-colors">Home</a></li>
                        <li><a href="#shows" class="text-gray-300 hover:text-ca-yellow transition-colors">Shows</a></li>
                        <li><a href="#schedule" class="text-gray-300 hover:text-ca-yellow transition-colors">Schedule</a></li>
                        <li><a href="#about" class="text-gray-300 hover:text-ca-yellow transition-colors">About</a></li>
                        <li><a href="#contact" class="text-gray-300 hover:text-ca-yellow transition-colors">Contact</a></li>
                    </ul>
                </div>

                <div>
                    <h4 class="text-lg font-bold mb-6 text-white">Categories</h4>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-gray-300 hover:text-ca-yellow transition-colors">Gaming</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-ca-yellow transition-colors">Anime</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-ca-yellow transition-colors">Cartoons</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-ca-yellow transition-colors">News & Reviews</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-ca-yellow transition-colors">Live Events</a></li>
                    </ul>
                </div>

                <div>
                    <h4 class="text-lg font-bold mb-6 text-white">Support</h4>
                    <ul class="space-y-3">
                        <li><a href="#" class="text-gray-300 hover:text-ca-yellow transition-colors">FAQ</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-ca-yellow transition-colors">Help Center</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-ca-yellow transition-colors">Terms of Service</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-ca-yellow transition-colors">Privacy Policy</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-ca-yellow transition-colors">Advertise With Us</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // Page Loading Animation
        document.addEventListener('DOMContentLoaded', function() {
            const loader = document.getElementById('page-loader');

            // Hide loader after 2 seconds
            setTimeout(() => {
                loader.style.opacity = '0';
                setTimeout(() => {
                    loader.style.display = 'none';
                }, 500);
            }, 2000);
        });

        // Particle Animation
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 50;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.width = Math.random() * 4 + 2 + 'px';
                particle.style.height = particle.style.width;
                particle.style.animationDelay = Math.random() * 15 + 's';
                particle.style.animationDuration = (Math.random() * 10 + 10) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // Mobile menu toggle
        const menuToggle = document.getElementById('menu-toggle');
        const closeMenu = document.getElementById('close-menu');
        const mobileMenu = document.getElementById('mobile-menu');

        menuToggle.addEventListener('click', () => {
            mobileMenu.classList.add('active');
        });

        closeMenu.addEventListener('click', () => {
            mobileMenu.classList.remove('active');
        });

        // Counter Animation
        function animateCounters() {
            const counters = document.querySelectorAll('.counter');

            counters.forEach(counter => {
                const target = parseInt(counter.getAttribute('data-target'));
                const increment = target / 100;
                let current = 0;

                const updateCounter = () => {
                    if (current < target) {
                        current += increment;
                        counter.textContent = Math.ceil(current);
                        setTimeout(updateCounter, 20);
                    } else {
                        counter.textContent = target;
                    }
                };

                updateCounter();
            });
        }

        // Enhanced Scroll animations
        document.addEventListener('DOMContentLoaded', function() {
            createParticles();

            const scrollElements = document.querySelectorAll('.scroll-animation');

            const elementInView = (el, dividend = 1) => {
                const elementTop = el.getBoundingClientRect().top;
                return (
                    elementTop <= (window.innerHeight || document.documentElement.clientHeight) / dividend
                );
            };

            const displayScrollElement = (element) => {
                element.classList.add('active');

                // Trigger counter animation if element has counters
                const counters = element.querySelectorAll('.counter');
                if (counters.length > 0) {
                    animateCounters();
                }
            };

            const handleScrollAnimation = () => {
                scrollElements.forEach((el) => {
                    if (elementInView(el, 1.25)) {
                        displayScrollElement(el);
                    }
                });
            };

            window.addEventListener('scroll', () => {
                handleScrollAnimation();
            });

            // Initialize on page load
            handleScrollAnimation();
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function(e) {
                e.preventDefault();

                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);

                if (targetElement) {
                    window.scrollTo({
                        top: targetElement.offsetTop - 80,
                        behavior: 'smooth'
                    });

                    // Close mobile menu if open
                    if (mobileMenu.classList.contains('active')) {
                        mobileMenu.classList.remove('active');
                    }
                }
            });
        });

        // Enhanced Button Interactions
        document.querySelectorAll('.btn-primary').forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-2px) scale(1.05)';
            });

            button.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // Parallax Effect for Hero Section
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const parallax = document.querySelector('.hero-bg');
            const speed = scrolled * 0.5;

            if (parallax) {
                parallax.style.transform = `translateY(${speed}px)`;
            }
        });
    </script>
<script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'9470971b026fa04f',t:'MTc0ODQ2NTA2OS4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script></body>
</html>
