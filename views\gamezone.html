<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GameZone - Caribbean Advantage Channel 99-9</title>
    <meta name="description" content="Caribbean Advantage GameZone - Live gaming streams, tournaments, and gaming content from Puerto Rico on Channel 99-9.">

    <!-- Stylesheets -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/css/styles.css">

    <!-- Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'ca-blue': '#003d6b',
                        'ca-dark-blue': '#001f3f',
                        'ca-light-blue': '#0066cc',
                        'ca-accent-blue': '#4a90e2',
                        'ca-yellow': '#ffc107',
                        'ca-orange': '#ff9800',
                        'ca-green': '#4caf50',
                        'ca-dark': '#1a1a1a',
                        'ca-gray': '#2d3748',
                        'ca-light-gray': '#f7fafc',
                    }
                }
            }
        }
    </script>

    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #003d6b 0%, #0066cc 50%, #4a90e2 100%);
            color: white;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        /* Animated Background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
            animation: backgroundFloat 20s ease-in-out infinite;
            z-index: -1;
        }

        @keyframes backgroundFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-20px) rotate(1deg); }
            66% { transform: translateY(10px) rotate(-1deg); }
        }

        /* Floating Animation Elements */
        .floating-element {
            position: fixed;
            font-size: 2rem;
            opacity: 0.1;
            animation: float 15s ease-in-out infinite;
            z-index: -1;
            pointer-events: none;
        }

        .floating-element:nth-child(1) { top: 10%; left: 10%; animation-delay: 0s; }
        .floating-element:nth-child(2) { top: 20%; right: 15%; animation-delay: 3s; }
        .floating-element:nth-child(3) { top: 60%; left: 20%; animation-delay: 6s; }
        .floating-element:nth-child(4) { bottom: 20%; right: 25%; animation-delay: 9s; }
        .floating-element:nth-child(5) { top: 40%; left: 50%; animation-delay: 12s; }

        @keyframes float {
            0%, 100% { transform: translateY(0px) translateX(0px); }
            25% { transform: translateY(-20px) translateX(10px); }
            50% { transform: translateY(10px) translateX(-5px); }
            75% { transform: translateY(-15px) translateX(15px); }
        }
    </style>
</head>
<body>
    <script>
        // Language Management
        let currentLanguage = localStorage.getItem('language') || 'en';

        const translations = {
            en: {
                'nav.home': 'Home',
                'nav.tv': 'Online TV',
                'nav.gamezone': 'GameZone',
                'nav.radio': 'Online Radio',
                'common.watch_live': '📺 Watch Live'
            },
            es: {
                'nav.home': 'Inicio',
                'nav.tv': 'TV en Línea',
                'nav.gamezone': 'Zona de Juegos',
                'nav.radio': 'Radio en Línea',
                'common.watch_live': '📺 Ver en Vivo'
            }
        };

        function changeLanguage(lang) {
            currentLanguage = lang;
            localStorage.setItem('language', lang);
            document.getElementById('current-lang').textContent = lang.toUpperCase();

            // Update all translatable elements
            document.querySelectorAll('[data-translate]').forEach(element => {
                const key = element.getAttribute('data-translate');
                if (translations[lang] && translations[lang][key]) {
                    element.textContent = translations[lang][key];
                }
            });
        }

        // Initialize language on page load
        document.addEventListener('DOMContentLoaded', function() {
            changeLanguage(currentLanguage);
        });
    </script>

    <!-- Floating Animation Elements -->
    <div class="floating-element">🌊</div>
    <div class="floating-element">🏝️</div>
    <div class="floating-element">🎵</div>
    <div class="floating-element">📺</div>
    <div class="floating-element">🎮</div>

    <!-- Enhanced Navigation with Blue Theme -->
    <header class="bg-gradient-to-r from-ca-blue/95 to-ca-light-blue/95 backdrop-blur-md shadow-lg fixed w-full top-0 z-50 border-b border-white/20">
        <div class="container mx-auto px-4 py-3">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-r from-ca-light-blue to-ca-accent-blue rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold text-lg">CA</span>
                    </div>
                    <span class="font-bold text-xl text-white">Caribbean Advantage</span>
                </div>

                <nav class="hidden md:flex items-center space-x-8">
                    <a href="/" class="nav-link text-white/90 hover:text-white font-medium transition-colors" data-translate="nav.home">Home</a>
                    <a href="/live" class="nav-link text-white/90 hover:text-white font-medium transition-colors" data-translate="nav.tv">Online TV</a>
                    <a href="/gamezone" class="nav-link active text-white font-medium hover:text-ca-yellow transition-colors" data-translate="nav.gamezone">GameZone</a>
                    <a href="/radio" class="nav-link text-white/90 hover:text-white font-medium transition-colors" data-translate="nav.radio">Online Radio</a>
                </nav>

                <div class="flex items-center space-x-4">
                    <a href="/live" class="bg-ca-accent-blue hover:bg-ca-light-blue text-white px-4 py-2 rounded-lg font-medium transition-colors" data-translate="common.watch_live">
                        📺 Watch Live
                    </a>

                    <!-- Language Selector -->
                    <div class="dropdown-container relative">
                        <button class="flex items-center space-x-2 text-white/90 hover:text-white transition-colors">
                            <span class="text-lg">🌐</span>
                            <span id="current-lang" class="font-medium">EN</span>
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div class="dropdown absolute top-full right-0 mt-2 w-40 bg-white rounded-md shadow-lg py-1 z-10 opacity-0 visibility-hidden transform translateY(-10px) transition-all duration-300">
                            <a href="#" onclick="changeLanguage('en')" class="language-option flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-100">
                                🇺🇸 English
                            </a>
                            <a href="#" onclick="changeLanguage('es')" class="language-option flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-100">
                                🇪🇸 Español
                            </a>
                        </div>
                    </div>

                    <button id="mobile-menu-btn" class="md:hidden text-white hover:text-ca-yellow">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div id="mobile-menu" class="hidden md:hidden bg-ca-blue/95 backdrop-blur-md border-t border-white/20">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="/" class="block px-3 py-2 rounded-md text-base font-medium text-white/90 hover:text-white hover:bg-white/10" data-translate="nav.home">Home</a>
                <a href="/live" class="block px-3 py-2 rounded-md text-base font-medium text-white/90 hover:text-white hover:bg-white/10" data-translate="nav.tv">Online TV</a>
                <a href="/gamezone" class="block px-3 py-2 rounded-md text-base font-medium text-white bg-white/10" data-translate="nav.gamezone">GameZone</a>
                <a href="/radio" class="block px-3 py-2 rounded-md text-base font-medium text-white/90 hover:text-white hover:bg-white/10" data-translate="nav.radio">Online Radio</a>
            </div>
        </div>
    </header>

    <style>
        .dropdown-container:hover .dropdown {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
    </style>

    <!-- Hero Section -->
    <section class="bg-gradient-to-r from-ca-blue to-ca-light-blue min-h-screen flex items-center pt-16">
        <div class="container mx-auto px-4 py-16">
            <div class="text-center text-white">
                <div class="text-8xl mb-6">🎮</div>
                <h1 class="text-5xl md:text-7xl font-bold mb-6">Game<span class="text-ca-yellow">Zone</span></h1>
                <p class="text-xl mb-8 max-w-3xl mx-auto">Live gaming streams, tournaments, and gaming content from Puerto Rico on Channel 99-9</p>
                <div class="flex flex-wrap justify-center gap-4 mb-12">
                    <div class="bg-white/10 backdrop-blur-sm px-6 py-3 rounded-lg font-bold">
                        🔴 Live Gaming Streams
                    </div>
                    <div class="bg-white/10 backdrop-blur-sm px-6 py-3 rounded-lg font-bold">
                        🏆 Tournaments
                    </div>
                    <div class="bg-white/10 backdrop-blur-sm px-6 py-3 rounded-lg font-bold">
                        🎯 Local Gamers
                    </div>
                </div>
                <a href="/live" class="btn-primary bg-ca-yellow hover:bg-ca-orange text-ca-dark px-8 py-4 rounded-lg font-bold text-lg">
                    🎮 Start Gaming Now
                </a>
            </div>
        </div>
    </section>

    <!-- Gaming Content -->
    <section class="py-20 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold mb-4">Gaming <span class="gradient-text">Content</span></h2>
                <p class="text-xl text-gray-600">Discover our gaming streams, tournaments, and community content</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Live Streams -->
                <div class="bg-gradient-to-br from-ca-blue to-ca-light-blue text-white p-8 rounded-xl card-hover">
                    <div class="text-5xl mb-4">🔴</div>
                    <h3 class="text-2xl font-bold mb-4">Live Streams</h3>
                    <p class="mb-6">Watch live gaming sessions from Puerto Rican gamers covering popular games and new releases.</p>
                    <a href="/live" class="bg-white text-ca-blue px-6 py-3 rounded-lg font-bold hover:bg-gray-100 transition-colors">
                        Watch Live
                    </a>
                </div>

                <!-- Tournaments -->
                <div class="bg-gradient-to-br from-ca-orange to-ca-yellow text-white p-8 rounded-xl card-hover">
                    <div class="text-5xl mb-4">🏆</div>
                    <h3 class="text-2xl font-bold mb-4">Tournaments</h3>
                    <p class="mb-6">Join competitive gaming tournaments and watch the best local players compete for prizes.</p>
                    <a href="/events" class="bg-white text-ca-orange px-6 py-3 rounded-lg font-bold hover:bg-gray-100 transition-colors">
                        View Events
                    </a>
                </div>

                <!-- Gaming Community -->
                <div class="bg-gradient-to-br from-ca-green to-ca-light-blue text-white p-8 rounded-xl card-hover">
                    <div class="text-5xl mb-4">👥</div>
                    <h3 class="text-2xl font-bold mb-4">Community</h3>
                    <p class="mb-6">Connect with fellow gamers, share tips, and be part of the Caribbean gaming community.</p>
                    <a href="/contact" class="bg-white text-ca-green px-6 py-3 rounded-lg font-bold hover:bg-gray-100 transition-colors">
                        Join Community
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Popular Games -->
    <section class="py-20 bg-gradient-to-br from-ca-light-gray to-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold mb-4">Popular <span class="gradient-text">Games</span></h2>
                <p class="text-xl text-gray-600">Games featured on Caribbean Advantage GameZone</p>
            </div>

            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6">
                <div class="bg-white p-6 rounded-xl shadow-lg text-center card-hover">
                    <div class="text-4xl mb-2">🎯</div>
                    <h4 class="font-bold">Fortnite</h4>
                </div>
                <div class="bg-white p-6 rounded-xl shadow-lg text-center card-hover">
                    <div class="text-4xl mb-2">⚽</div>
                    <h4 class="font-bold">FIFA</h4>
                </div>
                <div class="bg-white p-6 rounded-xl shadow-lg text-center card-hover">
                    <div class="text-4xl mb-2">🎮</div>
                    <h4 class="font-bold">Call of Duty</h4>
                </div>
                <div class="bg-white p-6 rounded-xl shadow-lg text-center card-hover">
                    <div class="text-4xl mb-2">🏎️</div>
                    <h4 class="font-bold">Gran Turismo</h4>
                </div>
                <div class="bg-white p-6 rounded-xl shadow-lg text-center card-hover">
                    <div class="text-4xl mb-2">🥊</div>
                    <h4 class="font-bold">Street Fighter</h4>
                </div>
                <div class="bg-white p-6 rounded-xl shadow-lg text-center card-hover">
                    <div class="text-4xl mb-2">🎲</div>
                    <h4 class="font-bold">More Games</h4>
                </div>
            </div>
        </div>
    </section>

    <!-- Gaming Schedule -->
    <section class="py-20 bg-ca-blue text-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-4xl font-bold mb-4">Gaming <span class="text-ca-yellow">Schedule</span></h2>
                <p class="text-xl">When to catch your favorite gaming content</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="bg-white/10 backdrop-blur-sm p-6 rounded-xl">
                    <h3 class="text-2xl font-bold mb-4 text-ca-yellow">Weekdays</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span>Live Gaming</span>
                            <span>6:00 PM - 8:00 PM</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Game Reviews</span>
                            <span>8:00 PM - 9:00 PM</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Community Play</span>
                            <span>9:00 PM - 11:00 PM</span>
                        </div>
                    </div>
                </div>

                <div class="bg-white/10 backdrop-blur-sm p-6 rounded-xl">
                    <h3 class="text-2xl font-bold mb-4 text-ca-yellow">Weekends</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span>Tournament Streams</span>
                            <span>2:00 PM - 6:00 PM</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Retro Gaming</span>
                            <span>6:00 PM - 8:00 PM</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Late Night Gaming</span>
                            <span>10:00 PM - 12:00 AM</span>
                        </div>
                    </div>
                </div>

                <div class="bg-white/10 backdrop-blur-sm p-6 rounded-xl">
                    <h3 class="text-2xl font-bold mb-4 text-ca-yellow">Special Events</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span>Monthly Tournaments</span>
                            <span>First Saturday</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Game Launches</span>
                            <span>As Announced</span>
                        </div>
                        <div class="flex justify-between">
                            <span>Community Events</span>
                            <span>Weekends</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-12">
                <a href="/live" class="bg-ca-yellow hover:bg-ca-orange text-ca-dark px-8 py-4 rounded-lg font-bold text-lg">
                    🔴 Watch Gaming Live Now
                </a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-ca-dark text-white py-12">
        <div class="container mx-auto px-4 text-center">
            <div class="flex items-center justify-center space-x-3 mb-4">
                <div class="w-12 h-12 bg-gradient-to-r from-ca-blue to-ca-orange rounded-lg flex items-center justify-center">
                    <span class="text-white font-bold text-xl">CA</span>
                </div>
                <div class="text-2xl font-bold">
                    <span class="text-ca-blue">Caribbean</span> <span class="text-ca-orange">Advantage</span>
                </div>
            </div>
            <p class="text-gray-300 mb-4">GameZone - Channel 99-9 North Coast Puerto Rico</p>
            <p class="text-gray-400 text-sm">© 2023 Caribbean Advantage. All rights reserved.</p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="/js/main.js"></script>
</body>
</html>
