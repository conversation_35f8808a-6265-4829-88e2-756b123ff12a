<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GameZone - Caribbean Advantage | Channel 99-9</title>
    <meta name="description" content="Interactive gaming hub on Channel 99-9. Play games, join tournaments, and engage with Caribbean gaming community.">

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Styles -->
    <link rel="stylesheet" href="/css/styles.css">

    <style>
        .gaming-hero {
            background: linear-gradient(135deg, var(--deep-ocean) 0%, var(--coral-accent) 50%, var(--mint-accent) 100%);
            position: relative;
            overflow: hidden;
        }

        .gaming-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hexagon" width="20" height="20" patternUnits="userSpaceOnUse"><polygon points="10,2 18,7 18,13 10,18 2,13 2,7" fill="none" stroke="white" stroke-width="0.5" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23hexagon)"/></svg>');
            pointer-events: none;
        }

        .game-card {
            background: var(--white);
            border-radius: var(--radius-xl);
            overflow: hidden;
            box-shadow: var(--shadow-lg);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 2px solid transparent;
            position: relative;
        }

        .game-card::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, var(--coral-accent), var(--mint-accent), var(--ocean-blue));
            border-radius: var(--radius-xl);
            z-index: -1;
            opacity: 0;
            transition: opacity 0.4s ease;
        }

        .game-card:hover::before {
            opacity: 1;
        }

        .game-card:hover {
            transform: translateY(-12px) rotateX(5deg);
            box-shadow: 0 25px 80px rgba(255, 107, 107, 0.3);
        }

        .game-thumbnail {
            height: 200px;
            background: linear-gradient(135deg, var(--coral-accent), var(--mint-accent));
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 4rem;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .game-thumbnail::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.8s ease;
        }

        .game-card:hover .game-thumbnail::after {
            left: 100%;
        }

        .play-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .game-card:hover .play-overlay {
            opacity: 1;
        }

        .play-btn {
            background: var(--white);
            border: none;
            border-radius: 50%;
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            color: var(--coral-accent);
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: var(--shadow-lg);
        }

        .play-btn:hover {
            transform: scale(1.1);
            background: var(--coral-accent);
            color: var(--white);
        }

        .game-info {
            padding: var(--spacing-lg);
        }

        .game-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--deep-ocean);
            margin-bottom: var(--spacing-xs);
        }

        .game-description {
            color: var(--slate-gray);
            margin-bottom: var(--spacing-md);
            line-height: 1.5;
        }

        .game-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-md);
            font-size: 0.9rem;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            color: var(--slate-gray);
        }

        .leaderboard {
            background: linear-gradient(135deg, var(--white) 0%, var(--powder-blue) 100%);
            border-radius: var(--radius-xl);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--sky-blue);
        }

        .leaderboard-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
            padding: var(--spacing-md);
            background: var(--white);
            border-radius: var(--radius-md);
            margin-bottom: var(--spacing-sm);
            box-shadow: var(--shadow-sm);
            transition: all var(--transition-fast);
        }

        .leaderboard-item:hover {
            transform: translateX(8px);
            box-shadow: var(--shadow-md);
        }

        .rank-badge {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--coral-accent), var(--mint-accent));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 700;
        }

        .rank-badge.gold {
            background: linear-gradient(135deg, #FFD700, #FFA500);
        }

        .rank-badge.silver {
            background: linear-gradient(135deg, #C0C0C0, #A0A0A0);
        }

        .rank-badge.bronze {
            background: linear-gradient(135deg, #CD7F32, #B8860B);
        }

        .tournament-card {
            background: linear-gradient(135deg, var(--deep-ocean), var(--ocean-blue));
            color: white;
            border-radius: var(--radius-xl);
            padding: var(--spacing-xl);
            position: relative;
            overflow: hidden;
        }

        .tournament-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 200px;
            height: 200px;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            transform: translate(50%, -50%);
        }

        .tournament-timer {
            display: flex;
            gap: var(--spacing-md);
            margin-top: var(--spacing-lg);
        }

        .timer-unit {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            border-radius: var(--radius-md);
            padding: var(--spacing-sm);
            min-width: 60px;
        }

        .timer-number {
            font-size: 1.5rem;
            font-weight: 700;
            display: block;
        }

        .timer-label {
            font-size: 0.8rem;
            opacity: 0.8;
        }

        .category-filter {
            display: flex;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .category-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            background: var(--white);
            border: 2px solid var(--medium-gray);
            border-radius: var(--radius-md);
            color: var(--charcoal);
            text-decoration: none;
            font-weight: 600;
            transition: all var(--transition-normal);
            position: relative;
            overflow: hidden;
        }

        .category-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, var(--coral-accent), var(--mint-accent));
            transition: left 0.3s ease;
            z-index: -1;
        }

        .category-btn:hover::before,
        .category-btn.active::before {
            left: 0;
        }

        .category-btn:hover,
        .category-btn.active {
            color: white;
            border-color: var(--coral-accent);
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <!-- Modern Navigation -->
    <nav class="navbar" id="navbar">
        <div class="container">
            <div class="nav-container">
                <a href="/" class="nav-logo">
                    <div class="nav-logo-icon">CA</div>
                    <span>Caribbean Advantage</span>
                </a>

                <ul class="nav-menu" id="nav-menu">
                    <li><a href="/" class="nav-link">Home</a></li>
                    <li><a href="/live" class="nav-link">Live TV</a></li>
                    <li><a href="/gamezone" class="nav-link active">GameZone</a></li>
                    <li><a href="/radio" class="nav-link">Radio</a></li>
                    <li><a href="/events" class="nav-link">Events</a></li>
                    <li><a href="/contact" class="nav-link">Contact</a></li>
                </ul>

                <button class="nav-toggle" id="nav-toggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </div>
    </nav>

    <!-- Gaming Hero -->
    <section class="hero gaming-hero">
        <div class="container">
            <div class="hero-content">
                <h1>Caribbean GameZone</h1>
                <p>Enter the ultimate gaming experience with interactive tournaments, challenges, and community competitions on Channel 99-9.</p>

                <div class="d-flex justify-center" style="gap: var(--spacing-md); flex-wrap: wrap;">
                    <a href="#games" class="btn btn-primary">
                        🎮 Play Now
                    </a>
                    <a href="#tournaments" class="btn btn-secondary">
                        🏆 Join Tournament
                    </a>
                    <a href="#leaderboard" class="btn btn-ghost">
                        📊 Leaderboard
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Tournament Section -->
    <section class="section" id="tournaments">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Active Tournament</h2>
                <p class="section-subtitle">Join our weekly Caribbean Gaming Championship and compete for amazing prizes.</p>
            </div>

            <div class="tournament-card">
                <h3 style="font-size: 2rem; margin-bottom: var(--spacing-sm);">🏆 Caribbean Championship</h3>
                <p style="font-size: 1.1rem; opacity: 0.9; margin-bottom: var(--spacing-md);">
                    Weekly tournament featuring the best Caribbean gamers. Winner takes home $500 and the champion title!
                </p>

                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: var(--spacing-lg);">
                    <div>
                        <strong>Prize Pool: $1,000</strong><br>
                        <span style="opacity: 0.8;">Entry Fee: Free</span>
                    </div>
                    <div>
                        <strong>Players: 47/64</strong><br>
                        <span style="opacity: 0.8;">Spots Available: 17</span>
                    </div>
                </div>

                <h4 style="margin-bottom: var(--spacing-sm);">Tournament starts in:</h4>
                <div class="tournament-timer">
                    <div class="timer-unit">
                        <span class="timer-number">02</span>
                        <span class="timer-label">Days</span>
                    </div>
                    <div class="timer-unit">
                        <span class="timer-number">14</span>
                        <span class="timer-label">Hours</span>
                    </div>
                    <div class="timer-unit">
                        <span class="timer-number">32</span>
                        <span class="timer-label">Minutes</span>
                    </div>
                    <div class="timer-unit">
                        <span class="timer-number">18</span>
                        <span class="timer-label">Seconds</span>
                    </div>
                </div>

                <a href="#" class="btn btn-primary" style="margin-top: var(--spacing-lg);">
                    🎯 Register Now
                </a>
            </div>
        </div>
    </section>

    <!-- Games Section -->
    <section class="section" style="background: var(--light-gray);" id="games">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Featured Games</h2>
                <p class="section-subtitle">Discover our collection of interactive games designed for Caribbean entertainment.</p>
            </div>

            <div class="category-filter">
                <a href="#" class="category-btn active">All Games</a>
                <a href="#" class="category-btn">Puzzle</a>
                <a href="#" class="category-btn">Action</a>
                <a href="#" class="category-btn">Strategy</a>
                <a href="#" class="category-btn">Trivia</a>
                <a href="#" class="category-btn">Caribbean Culture</a>
            </div>

            <div class="content-grid">
                <div class="game-card">
                    <div class="game-thumbnail">
                        🧩
                        <div class="play-overlay">
                            <button class="play-btn">▶</button>
                        </div>
                    </div>
                    <div class="game-info">
                        <h3 class="game-title">Caribbean Puzzle Challenge</h3>
                        <p class="game-description">Test your skills with beautiful Caribbean-themed puzzles featuring stunning island landscapes.</p>
                        <div class="game-stats">
                            <div class="stat-item">
                                <span>👥</span>
                                <span>1,234 players</span>
                            </div>
                            <div class="stat-item">
                                <span>⭐</span>
                                <span>4.8/5</span>
                            </div>
                            <div class="stat-item">
                                <span>🎯</span>
                                <span>Easy</span>
                            </div>
                        </div>
                        <a href="#" class="btn btn-primary w-full justify-center">Play Now</a>
                    </div>
                </div>

                <div class="game-card">
                    <div class="game-thumbnail" style="background: linear-gradient(135deg, var(--success-green), var(--mint-accent));">
                        🌴
                        <div class="play-overlay">
                            <button class="play-btn">▶</button>
                        </div>
                    </div>
                    <div class="game-info">
                        <h3 class="game-title">Island Adventure Quest</h3>
                        <p class="game-description">Embark on an exciting adventure through Caribbean islands, solving mysteries and collecting treasures.</p>
                        <div class="game-stats">
                            <div class="stat-item">
                                <span>👥</span>
                                <span>892 players</span>
                            </div>
                            <div class="stat-item">
                                <span>⭐</span>
                                <span>4.9/5</span>
                            </div>
                            <div class="stat-item">
                                <span>🎯</span>
                                <span>Medium</span>
                            </div>
                        </div>
                        <a href="#" class="btn btn-primary w-full justify-center">Play Now</a>
                    </div>
                </div>

                <div class="game-card">
                    <div class="game-thumbnail" style="background: linear-gradient(135deg, var(--warning-orange), var(--coral-accent));">
                        🧠
                        <div class="play-overlay">
                            <button class="play-btn">▶</button>
                        </div>
                    </div>
                    <div class="game-info">
                        <h3 class="game-title">Caribbean Trivia Master</h3>
                        <p class="game-description">Test your knowledge of Caribbean history, culture, music, and traditions in this engaging trivia game.</p>
                        <div class="game-stats">
                            <div class="stat-item">
                                <span>👥</span>
                                <span>2,156 players</span>
                            </div>
                            <div class="stat-item">
                                <span>⭐</span>
                                <span>4.7/5</span>
                            </div>
                            <div class="stat-item">
                                <span>🎯</span>
                                <span>Hard</span>
                            </div>
                        </div>
                        <a href="#" class="btn btn-primary w-full justify-center">Play Now</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Leaderboard Section -->
    <section class="section" id="leaderboard">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Top Players</h2>
                <p class="section-subtitle">See who's dominating the Caribbean gaming scene this week.</p>
            </div>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: var(--spacing-xl);">
                <div class="leaderboard">
                    <h3 style="margin-bottom: var(--spacing-lg); color: var(--deep-ocean);">🏆 Weekly Champions</h3>

                    <div class="leaderboard-item">
                        <div class="rank-badge gold">1</div>
                        <div style="flex: 1;">
                            <strong style="color: var(--deep-ocean);">IslandKing_PR</strong>
                            <div style="color: var(--slate-gray); font-size: 0.9rem;">2,847 points</div>
                        </div>
                        <div style="color: var(--success-green); font-weight: 600;">👑</div>
                    </div>

                    <div class="leaderboard-item">
                        <div class="rank-badge silver">2</div>
                        <div style="flex: 1;">
                            <strong style="color: var(--deep-ocean);">CaribbeanQueen</strong>
                            <div style="color: var(--slate-gray); font-size: 0.9rem;">2,634 points</div>
                        </div>
                        <div style="color: var(--slate-gray); font-weight: 600;">🥈</div>
                    </div>

                    <div class="leaderboard-item">
                        <div class="rank-badge bronze">3</div>
                        <div style="flex: 1;">
                            <strong style="color: var(--deep-ocean);">TropicalGamer</strong>
                            <div style="color: var(--slate-gray); font-size: 0.9rem;">2,421 points</div>
                        </div>
                        <div style="color: var(--warning-orange); font-weight: 600;">🥉</div>
                    </div>

                    <div class="leaderboard-item">
                        <div class="rank-badge">4</div>
                        <div style="flex: 1;">
                            <strong style="color: var(--deep-ocean);">BeachMaster</strong>
                            <div style="color: var(--slate-gray); font-size: 0.9rem;">2,198 points</div>
                        </div>
                    </div>

                    <div class="leaderboard-item">
                        <div class="rank-badge">5</div>
                        <div style="flex: 1;">
                            <strong style="color: var(--deep-ocean);">SalsaPlayer99</strong>
                            <div style="color: var(--slate-gray); font-size: 0.9rem;">1,987 points</div>
                        </div>
                    </div>
                </div>

                <div class="floating-card">
                    <h3 style="margin-bottom: var(--spacing-lg);">🎮 Your Gaming Stats</h3>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-md); margin-bottom: var(--spacing-lg);">
                        <div style="text-align: center; padding: var(--spacing-md); background: var(--powder-blue); border-radius: var(--radius-md);">
                            <div style="font-size: 2rem; font-weight: 700; color: var(--ocean-blue);">847</div>
                            <div style="color: var(--slate-gray); font-size: 0.9rem;">Total Points</div>
                        </div>
                        <div style="text-align: center; padding: var(--spacing-md); background: var(--powder-blue); border-radius: var(--radius-md);">
                            <div style="font-size: 2rem; font-weight: 700; color: var(--ocean-blue);">23</div>
                            <div style="color: var(--slate-gray); font-size: 0.9rem;">Games Played</div>
                        </div>
                        <div style="text-align: center; padding: var(--spacing-md); background: var(--powder-blue); border-radius: var(--radius-md);">
                            <div style="font-size: 2rem; font-weight: 700; color: var(--ocean-blue);">15</div>
                            <div style="color: var(--slate-gray); font-size: 0.9rem;">Wins</div>
                        </div>
                        <div style="text-align: center; padding: var(--spacing-md); background: var(--powder-blue); border-radius: var(--radius-md);">
                            <div style="font-size: 2rem; font-weight: 700; color: var(--ocean-blue);">#47</div>
                            <div style="color: var(--slate-gray); font-size: 0.9rem;">Your Rank</div>
                        </div>
                    </div>

                    <a href="#" class="btn btn-primary w-full justify-center">View Full Profile</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>Caribbean Advantage TV</h4>
                    <p>Modern Caribbean entertainment on Channel 99-9. Broadcasting live from North Coast Puerto Rico.</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <p><a href="/live">Live TV</a></p>
                    <p><a href="/radio">Radio</a></p>
                    <p><a href="/gamezone">GameZone</a></p>
                    <p><a href="/events">Events</a></p>
                </div>
                <div class="footer-section">
                    <h4>Contact</h4>
                    <p>Channel 99-9</p>
                    <p>North Coast Puerto Rico</p>
                    <p><EMAIL></p>
                </div>
                <div class="footer-section">
                    <h4>Follow Us</h4>
                    <p><a href="#">Facebook</a></p>
                    <p><a href="#">Twitter</a></p>
                    <p><a href="#">Instagram</a></p>
                    <p><a href="#">YouTube</a></p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 Caribbean Advantage TV. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="/js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Navigation functionality
            const navbar = document.getElementById('navbar');
            const navToggle = document.getElementById('nav-toggle');
            const navMenu = document.getElementById('nav-menu');

            window.addEventListener('scroll', function() {
                if (window.scrollY > 50) {
                    navbar.classList.add('scrolled');
                } else {
                    navbar.classList.remove('scrolled');
                }
            });

            navToggle.addEventListener('click', function() {
                navToggle.classList.toggle('active');
                navMenu.classList.toggle('active');
            });

            // Category filter functionality
            const categoryBtns = document.querySelectorAll('.category-btn');
            categoryBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    e.preventDefault();
                    categoryBtns.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                });
            });

            // Tournament timer
            function updateTimer() {
                const timerNumbers = document.querySelectorAll('.timer-number');
                const now = new Date().getTime();
                const tournament = new Date(now + (2 * 24 * 60 * 60 * 1000) + (14 * 60 * 60 * 1000) + (32 * 60 * 1000) + (18 * 1000));
                const distance = tournament - now;

                const days = Math.floor(distance / (1000 * 60 * 60 * 24));
                const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((distance % (1000 * 60)) / 1000);

                if (timerNumbers.length >= 4) {
                    timerNumbers[0].textContent = days.toString().padStart(2, '0');
                    timerNumbers[1].textContent = hours.toString().padStart(2, '0');
                    timerNumbers[2].textContent = minutes.toString().padStart(2, '0');
                    timerNumbers[3].textContent = seconds.toString().padStart(2, '0');
                }
            }

            updateTimer();
            setInterval(updateTimer, 1000);
        });
    </script>
</body>
</html>