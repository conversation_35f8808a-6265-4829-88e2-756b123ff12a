<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-i18n="gamezone.title">GameZone - Caribbean Advantage Channel 99-9</title>
    <meta name="description" content="Play amazing games on Caribbean Advantage Channel 99-9. Gaming tournaments, live streams, and local gaming community from North Coast Puerto Rico.">

    <!-- Stylesheets -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/css/styles.css">

    <!-- Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary-blue': '#1E90FF',
                        'primary-blue-dark': '#1873CC',
                        'primary-blue-light': '#4FA8FF',
                        'primary-blue-ultra-light': '#87CEEB',
                        'light-grey': '#F8F9FA',
                        'medium-grey': '#E9ECEF',
                        'dark-grey': '#6C757D',
                        'white': '#FFFFFF',
                        'black': '#212529',
                    }
                }
            }
        }
    </script>

    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #F8F9FA 0%, #E9ECEF 100%);
            color: #1a202c;
            overflow-x: hidden;
        }

        .game-card {
            background: linear-gradient(145deg, #ffffff, #f0f4f8);
            border: 2px solid transparent;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(30, 144, 255, 0.1);
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            position: relative;
            overflow: hidden;
        }

        .game-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(30, 144, 255, 0.1), transparent);
            transition: left 0.6s ease;
        }

        .game-card:hover::before {
            left: 100%;
        }

        .game-card:hover {
            transform: translateY(-15px) scale(1.02);
            border-color: #1E90FF;
            box-shadow: 0 20px 50px rgba(30, 144, 255, 0.25);
        }

        .banner-card {
            background: linear-gradient(135deg, #1E90FF, #4FA8FF);
            border-radius: 15px;
            padding: 20px;
            color: white;
            text-align: center;
            box-shadow: 0 8px 25px rgba(30, 144, 255, 0.3);
            transition: all 0.3s ease;
        }

        .banner-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 35px rgba(30, 144, 255, 0.4);
        }

        .game-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            z-index: 1000;
            backdrop-filter: blur(10px);
        }

        .game-modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .game-iframe {
            width: 95%;
            height: 90%;
            border: none;
            border-radius: 15px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
        }

        .close-btn {
            position: absolute;
            top: 20px;
            right: 30px;
            background: #ff4757;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            z-index: 1001;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            background: #ff3742;
            transform: scale(1.1);
        }

        /* High contrast text for accessibility */
        .text-contrast-dark {
            color: #1a202c !important;
        }

        .text-contrast-light {
            color: #ffffff !important;
        }

        .text-contrast-medium {
            color: #4a5568 !important;
        }

        /* Fix overlapping sections and z-index issues */
        section {
            position: relative;
            z-index: 1;
            margin-bottom: 0;
            clear: both;
            display: block;
            width: 100%;
        }

        /* Hero section should not overlap */
        .hero-bg {
            z-index: 2;
            margin-bottom: 0;
            padding-bottom: 2rem;
        }

        /* Header stays on top */
        header {
            z-index: 50;
        }

        /* Ensure proper spacing between sections */
        .py-20 {
            padding-top: 5rem !important;
            padding-bottom: 5rem !important;
        }

        /* Fix section overlapping */
        section + section {
            margin-top: 0;
            border-top: none;
        }

        /* Ensure sections don't float over each other */
        .bg-light-grey,
        .bg-gradient-to-br {
            position: relative;
            z-index: 1;
            display: block;
            width: 100%;
        }

        /* Mobile responsive fixes */
        @media (max-width: 768px) {
            .hero-bg {
                min-height: 80vh;
                padding: 2rem 0;
            }

            .text-5xl {
                font-size: 2.5rem !important;
            }

            .text-7xl {
                font-size: 3.5rem !important;
            }

            .container {
                padding-left: 1rem !important;
                padding-right: 1rem !important;
            }

            /* Fix mobile spacing */
            .py-20 {
                padding-top: 3rem !important;
                padding-bottom: 3rem !important;
            }

            /* Prevent text overflow on mobile */
            h1, h2, h3 {
                word-wrap: break-word;
                overflow-wrap: break-word;
            }

            /* Fix mobile navigation */
            .space-x-8 > * + * {
                margin-left: 1rem !important;
            }
        }
    </style>
</head>
<body>
    <!-- Header with Blue Background -->
    <header class="fixed top-0 left-0 right-0 z-50 bg-gradient-to-r from-primary-blue-dark to-primary-blue shadow-lg border-b border-white/20">
        <div class="container mx-auto px-4 py-3">
            <div class="flex justify-between items-center">
                <div class="flex items-center">
                    <div class="flex items-center space-x-3">
                        <div class="w-12 h-12 bg-gradient-to-r from-primary-blue-light to-primary-blue-ultra-light rounded-lg flex items-center justify-center shadow-lg">
                            <span class="text-white font-bold text-xl">CA</span>
                        </div>
                        <div class="text-2xl font-bold">
                            <span class="text-white">Caribbean</span> <span class="text-primary-blue-ultra-light">Advantage</span>
                        </div>
                    </div>
                </div>

                <nav class="hidden md:flex space-x-8">
                    <a href="/" class="nav-link text-white hover:text-primary-blue-ultra-light transition-colors font-medium" data-i18n="nav.home">Home</a>
                    <a href="/live" class="nav-link text-white hover:text-primary-blue-ultra-light transition-colors font-medium" data-i18n="nav.online-tv">Online TV</a>
                    <a href="/gamezone" class="nav-link text-primary-blue-ultra-light font-bold" data-i18n="nav.gamezone">GameZone</a>
                    <a href="/radio" class="nav-link text-white hover:text-primary-blue-ultra-light transition-colors font-medium" data-i18n="nav.radio">Online Radio</a>
                </nav>

                <div class="hidden md:flex items-center space-x-4">
                    <!-- Language Switcher -->
                    <div class="relative">
                        <button id="language-toggle" class="flex items-center space-x-2 text-white/90 hover:text-white transition-colors">
                            <span class="text-lg">🌍</span>
                            <span id="current-language" class="text-sm font-medium">English</span>
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div id="language-dropdown" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 hidden z-50">
                            <a href="#" onclick="changeLanguage('en')" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-t-lg">
                                <span class="mr-3">🇺🇸</span> English
                            </a>
                            <a href="#" onclick="changeLanguage('es')" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <span class="mr-3">🇪🇸</span> Español
                            </a>
                            <a href="#" onclick="changeLanguage('fr')" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <span class="mr-3">🇫🇷</span> Français
                            </a>
                            <a href="#" onclick="changeLanguage('sw')" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-b-lg">
                                <span class="mr-3">🌍</span> Kiswahili
                            </a>
                        </div>
                    </div>

                    <a href="/live" class="bg-white/20 hover:bg-white/30 text-white px-6 py-2 rounded-lg font-medium transition-colors" data-i18n="nav.watch-live">
                        📺 Watch Live
                    </a>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="menu-toggle" class="text-white focus:outline-none">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Mobile Menu -->
            <div id="mobile-menu" class="md:hidden hidden mt-4 pb-4">
                <div class="flex flex-col space-y-3">
                    <a href="/" class="text-white hover:text-primary-blue-ultra-light transition-colors font-medium" data-i18n="nav.home">Home</a>
                    <a href="/live" class="text-white hover:text-primary-blue-ultra-light transition-colors font-medium" data-i18n="nav.online-tv">Online TV</a>
                    <a href="/gamezone" class="text-primary-blue-ultra-light font-bold" data-i18n="nav.gamezone">GameZone</a>
                    <a href="/radio" class="text-white hover:text-primary-blue-ultra-light transition-colors font-medium" data-i18n="nav.radio">Online Radio</a>
                    <div class="border-t border-white/20 pt-3 mt-3">
                        <a href="/events" class="text-white/80 hover:text-white transition-colors font-medium block mb-2" data-i18n="nav.events">Events</a>
                        <a href="/contact" class="text-white/80 hover:text-white transition-colors font-medium block mb-3" data-i18n="nav.contact">Contact</a>
                        <a href="/live" class="bg-white/20 hover:bg-white/30 text-white px-6 py-2 rounded-lg font-medium block text-center transition-colors" data-i18n="nav.watch-live">📺 Watch Live</a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero-bg min-h-screen flex items-center pt-16 relative overflow-hidden">
        <div class="container mx-auto px-4 py-16 relative z-10">
            <div class="text-center">
                <div class="text-8xl mb-6 animate-bounce">🎮</div>
                <h1 class="text-5xl md:text-7xl font-bold mb-6 text-contrast-light">
                    Game<span class="gradient-text">Zone</span>
                </h1>
                <p class="text-xl text-white/90 mb-8 max-w-3xl mx-auto" data-i18n="gamezone.hero.description">
                    Play amazing games, watch live gaming streams, and join tournaments on Caribbean Advantage Channel 99-9
                </p>
                <div class="flex flex-wrap justify-center gap-4 mb-12">
                    <div class="bg-white/10 backdrop-blur-sm px-6 py-3 rounded-full font-bold text-white">
                        🔴 <span data-i18n="gamezone.hero.live">Live Gaming</span>
                    </div>
                    <div class="bg-white/10 backdrop-blur-sm px-6 py-3 rounded-full font-bold text-white">
                        🏆 <span data-i18n="gamezone.hero.tournaments">Tournaments</span>
                    </div>
                    <div class="bg-white/10 backdrop-blur-sm px-6 py-3 rounded-full font-bold text-white">
                        🎯 <span data-i18n="gamezone.hero.community">Community</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Games Section -->
    <section class="py-20 bg-light-grey">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-bold mb-6 text-contrast-dark">
                    <span data-i18n="gamezone.games.title">Play Amazing</span> <span class="gradient-text">Games</span>
                </h2>
                <p class="text-xl text-contrast-medium max-w-3xl mx-auto" data-i18n="gamezone.games.description">
                    Experience our collection of exciting games directly in your browser. No downloads required!
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
                <!-- Game 1: PIXELTWIPS -->
                <div class="game-card p-8 text-center">
                    <div class="w-24 h-24 mx-auto mb-6 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-4xl">
                        🏃‍♂️
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-contrast-dark">PIXELTWIPS</h3>
                    <p class="text-contrast-medium mb-6">A simplistic endless runner with retro arcade aesthetics that provides different ways to customize your experience.</p>
                    <div class="space-y-3 mb-6">
                        <div class="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm font-medium inline-block">Endless Runner</div>
                        <div class="bg-pink-100 text-pink-800 px-3 py-1 rounded-full text-sm font-medium inline-block ml-2">Retro Style</div>
                    </div>
                    <button onclick="openGame('pixeltwips')" class="bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white px-8 py-3 rounded-xl font-bold text-lg transition-all duration-300 transform hover:scale-105 shadow-lg">
                        🎮 Play Now
                    </button>
                </div>

                <!-- Game 2: GuaraMania -->
                <div class="game-card p-8 text-center">
                    <div class="w-24 h-24 mx-auto mb-6 bg-gradient-to-r from-green-500 to-yellow-500 rounded-full flex items-center justify-center text-4xl">
                        🥤
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-contrast-dark">GuaraMania</h3>
                    <p class="text-contrast-medium mb-6">A vibrant fruit-catching game that tests your reflexes. Control a blender to create the perfect juice by catching delicious fruits!</p>
                    <div class="space-y-3 mb-6">
                        <div class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium inline-block">Fruit Catching</div>
                        <div class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium inline-block ml-2">Unity WebGL</div>
                    </div>
                    <button onclick="openGame('guaramania')" class="bg-gradient-to-r from-green-500 to-yellow-500 hover:from-green-600 hover:to-yellow-600 text-white px-8 py-3 rounded-xl font-bold text-lg transition-all duration-300 transform hover:scale-105 shadow-lg">
                        🎮 Play Now
                    </button>
                </div>

                <!-- Game 3: We Need Cows -->
                <div class="game-card p-8 text-center">
                    <div class="w-24 h-24 mx-auto mb-6 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center text-4xl">
                        🛸
                    </div>
                    <h3 class="text-2xl font-bold mb-4 text-contrast-dark">We Need Cows</h3>
                    <p class="text-contrast-medium mb-6">Play alone or with a friend! Control UFOs to abduct cows while avoiding obstacles. Local multiplayer supported!</p>
                    <div class="space-y-3 mb-6">
                        <div class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium inline-block">Multiplayer</div>
                        <div class="bg-cyan-100 text-cyan-800 px-3 py-1 rounded-full text-sm font-medium inline-block ml-2">UFO Action</div>
                    </div>
                    <button onclick="openGame('weneedcows')" class="bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600 text-white px-8 py-3 rounded-xl font-bold text-lg transition-all duration-300 transform hover:scale-105 shadow-lg">
                        🎮 Play Now
                    </button>
                </div>
            </div>

            <!-- Game Controls Info -->
            <div class="bg-white rounded-2xl p-8 shadow-lg border border-medium-grey">
                <h3 class="text-2xl font-bold mb-6 text-center text-contrast-dark">Game Controls</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center">
                        <h4 class="font-bold text-lg mb-3 text-purple-600">PIXELTWIPS</h4>
                        <p class="text-contrast-medium">Use arrow keys or WASD to control your character in this endless runner adventure!</p>
                    </div>
                    <div class="text-center">
                        <h4 class="font-bold text-lg mb-3 text-green-600">GuaraMania</h4>
                        <p class="text-contrast-medium">Move your blender left and right to catch falling fruits and create delicious juices!</p>
                    </div>
                    <div class="text-center">
                        <h4 class="font-bold text-lg mb-3 text-blue-600">We Need Cows</h4>
                        <p class="text-contrast-medium">Player 1: WASD + E to abduct<br>Player 2: Arrow keys + M to abduct</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Gaming Banners Section (Like the model) -->
    <section class="py-20 bg-gradient-to-br from-primary-blue-dark to-primary-blue">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <h2 class="text-4xl md:text-5xl font-bold mb-6 text-white">
                    <span data-i18n="gamezone.banners.title">Gaming</span> <span class="text-primary-blue-ultra-light">Community</span>
                </h2>
                <p class="text-xl text-white/90 max-w-3xl mx-auto" data-i18n="gamezone.banners.description">
                    Join our vibrant gaming community and stay updated with the latest gaming news and events
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
                <!-- Live Streaming Banner -->
                <div class="banner-card">
                    <div class="text-4xl mb-4">🔴</div>
                    <h3 class="text-xl font-bold mb-2" data-i18n="gamezone.banners.live.title">Live Streaming</h3>
                    <p class="text-sm opacity-90" data-i18n="gamezone.banners.live.description">Watch live gaming sessions 24/7</p>
                    <div class="mt-4">
                        <a href="/live" class="bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                            <span data-i18n="gamezone.banners.live.action">Watch Now</span>
                        </a>
                    </div>
                </div>

                <!-- Tournaments Banner -->
                <div class="banner-card">
                    <div class="text-4xl mb-4">🏆</div>
                    <h3 class="text-xl font-bold mb-2" data-i18n="gamezone.banners.tournaments.title">Tournaments</h3>
                    <p class="text-sm opacity-90" data-i18n="gamezone.banners.tournaments.description">Compete in weekly gaming tournaments</p>
                    <div class="mt-4">
                        <a href="/events" class="bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                            <span data-i18n="gamezone.banners.tournaments.action">Join Events</span>
                        </a>
                    </div>
                </div>

                <!-- Community Banner -->
                <div class="banner-card">
                    <div class="text-4xl mb-4">👥</div>
                    <h3 class="text-xl font-bold mb-2" data-i18n="gamezone.banners.community.title">Community</h3>
                    <p class="text-sm opacity-90" data-i18n="gamezone.banners.community.description">Connect with fellow Caribbean gamers</p>
                    <div class="mt-4">
                        <a href="/contact" class="bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                            <span data-i18n="gamezone.banners.community.action">Join Us</span>
                        </a>
                    </div>
                </div>

                <!-- Gaming News Banner -->
                <div class="banner-card">
                    <div class="text-4xl mb-4">📰</div>
                    <h3 class="text-xl font-bold mb-2" data-i18n="gamezone.banners.news.title">Gaming News</h3>
                    <p class="text-sm opacity-90" data-i18n="gamezone.banners.news.description">Latest gaming news and reviews</p>
                    <div class="mt-4">
                        <a href="/radio" class="bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                            <span data-i18n="gamezone.banners.news.action">Listen</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Featured Gaming Content -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Gaming Schedule -->
                <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-8">
                    <h3 class="text-2xl font-bold mb-6 text-white">
                        🕒 <span data-i18n="gamezone.schedule.title">Gaming Schedule</span>
                    </h3>
                    <div class="space-y-4">
                        <div class="flex justify-between items-center bg-white/10 p-4 rounded-lg">
                            <div>
                                <div class="font-bold text-white" data-i18n="gamezone.schedule.live">Live Gaming</div>
                                <div class="text-sm text-white/80" data-i18n="gamezone.schedule.live.time">Daily 6:00 PM - 8:00 PM</div>
                            </div>
                            <div class="text-2xl">🎮</div>
                        </div>
                        <div class="flex justify-between items-center bg-white/10 p-4 rounded-lg">
                            <div>
                                <div class="font-bold text-white" data-i18n="gamezone.schedule.tournaments">Tournaments</div>
                                <div class="text-sm text-white/80" data-i18n="gamezone.schedule.tournaments.time">Weekends 2:00 PM - 6:00 PM</div>
                            </div>
                            <div class="text-2xl">🏆</div>
                        </div>
                        <div class="flex justify-between items-center bg-white/10 p-4 rounded-lg">
                            <div>
                                <div class="font-bold text-white" data-i18n="gamezone.schedule.community">Community Play</div>
                                <div class="text-sm text-white/80" data-i18n="gamezone.schedule.community.time">Daily 9:00 PM - 11:00 PM</div>
                            </div>
                            <div class="text-2xl">👥</div>
                        </div>
                    </div>
                </div>

                <!-- Popular Games -->
                <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-8">
                    <h3 class="text-2xl font-bold mb-6 text-white">
                        🎯 <span data-i18n="gamezone.popular.title">Popular Games</span>
                    </h3>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="bg-white/10 p-4 rounded-lg text-center">
                            <div class="text-3xl mb-2">🎯</div>
                            <div class="font-bold text-white text-sm">Fortnite</div>
                        </div>
                        <div class="bg-white/10 p-4 rounded-lg text-center">
                            <div class="text-3xl mb-2">⚽</div>
                            <div class="font-bold text-white text-sm">FIFA</div>
                        </div>
                        <div class="bg-white/10 p-4 rounded-lg text-center">
                            <div class="text-3xl mb-2">🎮</div>
                            <div class="font-bold text-white text-sm">Call of Duty</div>
                        </div>
                        <div class="bg-white/10 p-4 rounded-lg text-center">
                            <div class="text-3xl mb-2">🏎️</div>
                            <div class="font-bold text-white text-sm">Gran Turismo</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-12">
                <a href="/live" class="bg-white hover:bg-white/90 text-primary-blue px-8 py-4 rounded-xl font-bold text-lg transition-all duration-300 transform hover:scale-105 shadow-lg">
                    🔴 <span data-i18n="gamezone.cta">Watch Gaming Live Now</span>
                </a>
            </div>
        </div>
    </section>

    <!-- Game Modals -->
    <div id="pixeltwips-modal" class="game-modal">
        <button class="close-btn" onclick="closeGame()">✕ Close Game</button>
        <iframe id="pixeltwips-iframe" class="game-iframe" src=""></iframe>
    </div>

    <div id="guaramania-modal" class="game-modal">
        <button class="close-btn" onclick="closeGame()">✕ Close Game</button>
        <iframe id="guaramania-iframe" class="game-iframe" src=""></iframe>
    </div>

    <div id="weneedcows-modal" class="game-modal">
        <button class="close-btn" onclick="closeGame()">✕ Close Game</button>
        <iframe id="weneedcows-iframe" class="game-iframe" src=""></iframe>
    </div>

    <!-- Footer -->
    <footer class="bg-gradient-to-r from-primary-blue-dark to-primary-blue text-white py-12">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="md:col-span-2">
                    <div class="flex items-center space-x-3 mb-4">
                        <div class="w-12 h-12 bg-gradient-to-r from-primary-blue-light to-primary-blue-ultra-light rounded-lg flex items-center justify-center shadow-lg">
                            <span class="text-white font-bold text-xl">CA</span>
                        </div>
                        <div class="text-2xl font-bold">
                            <span class="text-white">Caribbean</span> <span class="text-primary-blue-ultra-light">Advantage</span>
                        </div>
                    </div>
                    <p class="text-white/90 mb-4" data-i18n="footer.description">Channel 99-9 covering North Coast Puerto Rico. Gaming, Online Radio, Live TV, and supporting local artists.</p>
                    <div class="flex space-x-4">
                        <a href="https://facebook.com/caribbeanadvantage" target="_blank" class="text-white/70 hover:text-white transition-colors">Facebook</a>
                        <a href="https://twitter.com/caribbeanadvantage" target="_blank" class="text-white/70 hover:text-white transition-colors">Twitter</a>
                        <a href="https://instagram.com/caribbeanadvantage" target="_blank" class="text-white/70 hover:text-white transition-colors">Instagram</a>
                        <a href="https://youtube.com/caribbeanadvantage" target="_blank" class="text-white/70 hover:text-white transition-colors">YouTube</a>
                    </div>
                </div>
                <div>
                    <h4 class="text-lg font-bold mb-4 text-white" data-i18n="footer.quick-links">Quick Links</h4>
                    <ul class="space-y-2">
                        <li><a href="/live" class="text-white/70 hover:text-white transition-colors" data-i18n="nav.online-tv">Online TV</a></li>
                        <li><a href="/gamezone" class="text-white/70 hover:text-white transition-colors" data-i18n="nav.gamezone">GameZone</a></li>
                        <li><a href="/radio" class="text-white/70 hover:text-white transition-colors" data-i18n="nav.radio">Online Radio</a></li>
                        <li><a href="/events" class="text-white/70 hover:text-white transition-colors" data-i18n="nav.events">Events</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="text-lg font-bold mb-4 text-white" data-i18n="footer.contact">Contact</h4>
                    <ul class="space-y-2 text-white/70">
                        <li><span data-i18n="nav.channel">📺 Channel 99-9</span></li>
                        <li><span data-i18n="nav.location">📍 North Coast Puerto Rico</span></li>
                        <li>📧 <EMAIL></li>
                        <li><a href="/contact" class="hover:text-white transition-colors">Contact Us</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-white/20 mt-8 pt-8 text-center">
                <p class="text-white/80" data-i18n="footer.copyright">© 2023 Caribbean Advantage TV. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="/js/i18n.js"></script>
    <script src="/js/main.js"></script>
    <script>
        // Game Modal Functions
        function openGame(gameType) {
            let modal, iframe, src;

            switch(gameType) {
                case 'pixeltwips':
                    modal = document.getElementById('pixeltwips-modal');
                    iframe = document.getElementById('pixeltwips-iframe');
                    src = 'https://html-classic.itch.zone/html/10075403/index.html';
                    break;
                case 'guaramania':
                    modal = document.getElementById('guaramania-modal');
                    iframe = document.getElementById('guaramania-iframe');
                    src = '/Games/GuaraMania/index.html';
                    break;
                case 'weneedcows':
                    modal = document.getElementById('weneedcows-modal');
                    iframe = document.getElementById('weneedcows-iframe');
                    src = '/Games/WE_NEED_COWS/index.html';
                    break;
            }

            if (modal && iframe) {
                iframe.src = src;
                modal.classList.add('active');
                document.body.style.overflow = 'hidden';
            }
        }

        function closeGame() {
            const modals = document.querySelectorAll('.game-modal');
            const iframes = document.querySelectorAll('.game-iframe');

            modals.forEach(modal => modal.classList.remove('active'));
            iframes.forEach(iframe => iframe.src = '');
            document.body.style.overflow = 'auto';
        }

        // Close modal when clicking outside
        document.querySelectorAll('.game-modal').forEach(modal => {
            modal.addEventListener('click', function(e) {
                if (e.target === this) {
                    closeGame();
                }
            });
        });

        // Close modal with Escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeGame();
            }
        });

        // Language switcher functionality
        document.getElementById('language-toggle')?.addEventListener('click', function() {
            const dropdown = document.getElementById('language-dropdown');
            dropdown.classList.toggle('hidden');
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            const toggle = document.getElementById('language-toggle');
            const dropdown = document.getElementById('language-dropdown');
            if (toggle && dropdown && !toggle.contains(e.target) && !dropdown.contains(e.target)) {
                dropdown.classList.add('hidden');
            }
        });

        // Change language function
        function changeLanguage(lang) {
            if (window.CaribbeanAdvantageI18n) {
                window.CaribbeanAdvantageI18n.changeLanguage(lang);

                // Update current language display
                const currentLang = document.getElementById('current-language');
                const langNames = {
                    'en': 'English',
                    'es': 'Español',
                    'fr': 'Français',
                    'sw': 'Kiswahili'
                };
                if (currentLang) {
                    currentLang.textContent = langNames[lang] || 'English';
                }

                // Hide dropdown
                document.getElementById('language-dropdown').classList.add('hidden');
            }
        }

        // Mobile menu toggle
        document.getElementById('menu-toggle')?.addEventListener('click', function() {
            // Add mobile menu functionality here
        });

        // Initialize i18n when page loads
        document.addEventListener('DOMContentLoaded', function() {
            if (window.CaribbeanAdvantageI18n) {
                window.CaribbeanAdvantageI18n.init();

                // Set initial language display
                const currentLang = localStorage.getItem('selectedLanguage') || 'en';
                const langNames = {
                    'en': 'English',
                    'es': 'Español',
                    'fr': 'Français',
                    'sw': 'Kiswahili'
                };
                const currentLangElement = document.getElementById('current-language');
                if (currentLangElement) {
                    currentLangElement.textContent = langNames[currentLang] || 'English';
                }
            }
        });
    </script>
</body>
</html>