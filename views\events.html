<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Events - Caribbean Advantage | Channel 99-9</title>
    <meta name="description" content="Discover upcoming Caribbean events, shows, and community gatherings on Channel 99-9. Join our vibrant Caribbean community.">

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Styles -->
    <link rel="stylesheet" href="/css/styles.css">

    <style>
        .events-hero {
            background: linear-gradient(135deg, var(--warning-orange) 0%, var(--coral-accent) 50%, var(--deep-ocean) 100%);
            position: relative;
            overflow: hidden;
        }

        .events-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="calendar" width="25" height="25" patternUnits="userSpaceOnUse"><rect x="2" y="2" width="21" height="21" fill="none" stroke="white" stroke-width="0.5" opacity="0.1"/><circle cx="7" cy="7" r="1" fill="white" opacity="0.1"/><circle cx="18" cy="7" r="1" fill="white" opacity="0.1"/><circle cx="7" cy="18" r="1" fill="white" opacity="0.1"/><circle cx="18" cy="18" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23calendar)"/></svg>');
            pointer-events: none;
        }

        .event-card {
            background: var(--white);
            border-radius: var(--radius-xl);
            overflow: hidden;
            box-shadow: var(--shadow-lg);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 2px solid transparent;
            position: relative;
        }

        .event-card::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, var(--warning-orange), var(--coral-accent), var(--mint-accent));
            border-radius: var(--radius-xl);
            z-index: -1;
            opacity: 0;
            transition: opacity 0.4s ease;
        }

        .event-card:hover::before {
            opacity: 1;
        }

        .event-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 60px rgba(255, 165, 0, 0.3);
        }

        .event-header {
            height: 180px;
            background: linear-gradient(135deg, var(--warning-orange), var(--coral-accent));
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .event-date {
            position: absolute;
            top: var(--spacing-sm);
            left: var(--spacing-sm);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-md);
            font-size: 0.9rem;
            font-weight: 600;
            text-align: center;
            min-width: 60px;
        }

        .event-status {
            position: absolute;
            top: var(--spacing-sm);
            right: var(--spacing-sm);
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-sm);
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-upcoming {
            background: var(--success-green);
            color: white;
        }

        .status-live {
            background: var(--coral-accent);
            color: white;
            animation: pulse 2s infinite;
        }

        .status-past {
            background: var(--slate-gray);
            color: white;
        }

        .event-content {
            padding: var(--spacing-lg);
        }

        .event-title {
            font-size: 1.4rem;
            font-weight: 700;
            color: var(--deep-ocean);
            margin-bottom: var(--spacing-xs);
        }

        .event-time {
            color: var(--warning-orange);
            font-weight: 600;
            margin-bottom: var(--spacing-sm);
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .event-location {
            color: var(--slate-gray);
            margin-bottom: var(--spacing-sm);
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }

        .event-description {
            color: var(--slate-gray);
            line-height: 1.5;
            margin-bottom: var(--spacing-md);
        }

        .event-actions {
            display: flex;
            gap: var(--spacing-sm);
            flex-wrap: wrap;
        }

        .featured-event {
            background: linear-gradient(135deg, var(--deep-ocean), var(--ocean-blue));
            color: white;
            border-radius: var(--radius-xl);
            padding: var(--spacing-xl);
            position: relative;
            overflow: hidden;
            margin-bottom: var(--spacing-xl);
        }

        .featured-event::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 300px;
            height: 300px;
            background: radial-gradient(circle, rgba(255,165,0,0.2) 0%, transparent 70%);
            transform: translate(50%, -50%);
        }

        .featured-content {
            position: relative;
            z-index: 2;
        }

        .featured-badge {
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
            background: var(--warning-orange);
            color: white;
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-sm);
            font-weight: 600;
            font-size: 0.9rem;
            margin-bottom: var(--spacing-md);
        }

        .calendar-view {
            background: var(--white);
            border-radius: var(--radius-xl);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--medium-gray);
        }

        .calendar-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-lg);
            padding-bottom: var(--spacing-md);
            border-bottom: 2px solid var(--light-gray);
        }

        .calendar-nav {
            display: flex;
            gap: var(--spacing-sm);
        }

        .calendar-nav button {
            background: var(--ocean-blue);
            color: white;
            border: none;
            border-radius: var(--radius-sm);
            padding: var(--spacing-xs) var(--spacing-sm);
            cursor: pointer;
            transition: all var(--transition-fast);
        }

        .calendar-nav button:hover {
            background: var(--deep-ocean);
            transform: scale(1.05);
        }

        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: var(--spacing-xs);
        }

        .calendar-day {
            aspect-ratio: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--radius-sm);
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-fast);
            position: relative;
        }

        .calendar-day:hover {
            background: var(--powder-blue);
        }

        .calendar-day.has-event {
            background: var(--warning-orange);
            color: white;
        }

        .calendar-day.has-event::after {
            content: '';
            position: absolute;
            bottom: 2px;
            left: 50%;
            transform: translateX(-50%);
            width: 4px;
            height: 4px;
            background: white;
            border-radius: 50%;
        }

        .calendar-day.today {
            background: var(--ocean-blue);
            color: white;
            font-weight: 700;
        }

        .filter-buttons {
            display: flex;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-xl);
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: var(--spacing-sm) var(--spacing-lg);
            background: var(--white);
            border: 2px solid var(--medium-gray);
            border-radius: var(--radius-md);
            color: var(--charcoal);
            text-decoration: none;
            font-weight: 600;
            transition: all var(--transition-normal);
            cursor: pointer;
        }

        .filter-btn:hover,
        .filter-btn.active {
            background: var(--warning-orange);
            color: white;
            border-color: var(--warning-orange);
            transform: translateY(-2px);
        }

        .event-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .stat-card {
            background: linear-gradient(135deg, var(--white) 0%, var(--powder-blue) 100%);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            text-align: center;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--sky-blue);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--ocean-blue);
            margin-bottom: var(--spacing-xs);
        }

        .stat-label {
            color: var(--slate-gray);
            font-weight: 500;
        }
    </style>
</head>
<body>
    <!-- Modern Navigation -->
    <nav class="navbar" id="navbar">
        <div class="container">
            <div class="nav-container">
                <a href="/" class="nav-logo">
                    <div class="nav-logo-icon">CA</div>
                    <span>Caribbean Advantage</span>
                </a>

                <ul class="nav-menu" id="nav-menu">
                    <li><a href="/" class="nav-link">Home</a></li>
                    <li><a href="/live" class="nav-link">Live TV</a></li>
                    <li><a href="/gamezone" class="nav-link">GameZone</a></li>
                    <li><a href="/radio" class="nav-link">Radio</a></li>
                    <li><a href="/events" class="nav-link active">Events</a></li>
                    <li><a href="/contact" class="nav-link">Contact</a></li>
                </ul>

                <button class="nav-toggle" id="nav-toggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </div>
    </nav>

    <!-- Events Hero -->
    <section class="hero events-hero">
        <div class="container">
            <div class="hero-content">
                <h1>Caribbean Events</h1>
                <p>Join our vibrant Caribbean community for exciting events, shows, and cultural celebrations on Channel 99-9.</p>

                <div class="d-flex justify-center" style="gap: var(--spacing-md); flex-wrap: wrap;">
                    <a href="#upcoming" class="btn btn-primary">
                        📅 Upcoming Events
                    </a>
                    <a href="#calendar" class="btn btn-secondary">
                        🗓️ Event Calendar
                    </a>
                    <a href="#featured" class="btn btn-ghost">
                        ⭐ Featured Event
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Event Stats -->
    <section class="section">
        <div class="container">
            <div class="event-stats">
                <div class="stat-card">
                    <div class="stat-number">24</div>
                    <div class="stat-label">Events This Month</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">1.2K</div>
                    <div class="stat-label">Community Members</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">156</div>
                    <div class="stat-label">Past Events</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">4.9</div>
                    <div class="stat-label">Average Rating</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Event -->
    <section class="section" id="featured">
        <div class="container">
            <div class="featured-event">
                <div class="featured-content">
                    <div class="featured-badge">
                        <span>⭐</span>
                        Featured Event
                    </div>
                    <h2 style="font-size: 2.5rem; margin-bottom: var(--spacing-md); color: white;">Caribbean Music Festival 2023</h2>
                    <p style="font-size: 1.2rem; margin-bottom: var(--spacing-lg); color: rgba(255,255,255,0.9);">
                        Join us for the biggest Caribbean music celebration of the year featuring top artists, local bands, and cultural performances.
                    </p>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: var(--spacing-lg); margin-bottom: var(--spacing-xl);">
                        <div>
                            <strong style="color: var(--warning-orange);">📅 Date:</strong><br>
                            <span>December 15-17, 2023</span>
                        </div>
                        <div>
                            <strong style="color: var(--warning-orange);">📍 Location:</strong><br>
                            <span>San Juan Convention Center</span>
                        </div>
                        <div>
                            <strong style="color: var(--warning-orange);">🎫 Tickets:</strong><br>
                            <span>$25 - $150</span>
                        </div>
                        <div>
                            <strong style="color: var(--warning-orange);">👥 Expected:</strong><br>
                            <span>5,000+ Attendees</span>
                        </div>
                    </div>

                    <div style="display: flex; gap: var(--spacing-md); flex-wrap: wrap;">
                        <a href="#" class="btn btn-primary">🎫 Get Tickets</a>
                        <a href="#" class="btn btn-secondary">📋 Learn More</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Upcoming Events -->
    <section class="section" style="background: var(--light-gray);" id="upcoming">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Upcoming Events</h2>
                <p class="section-subtitle">Don't miss these exciting Caribbean community events and shows.</p>
            </div>

            <div class="filter-buttons">
                <button class="filter-btn active" data-filter="all">All Events</button>
                <button class="filter-btn" data-filter="music">Music</button>
                <button class="filter-btn" data-filter="culture">Culture</button>
                <button class="filter-btn" data-filter="community">Community</button>
                <button class="filter-btn" data-filter="tv-show">TV Shows</button>
                <button class="filter-btn" data-filter="virtual">Virtual</button>
            </div>

            <div class="content-grid">
                <div class="event-card" data-category="music">
                    <div class="event-header">
                        🎵
                        <div class="event-date">DEC<br>08</div>
                        <div class="event-status status-upcoming">Upcoming</div>
                    </div>
                    <div class="event-content">
                        <h3 class="event-title">Salsa Night Live</h3>
                        <div class="event-time">
                            <span>🕒</span>
                            <span>Friday, 8:00 PM - 11:00 PM</span>
                        </div>
                        <div class="event-location">
                            <span>📍</span>
                            <span>Caribbean Advantage Studio</span>
                        </div>
                        <p class="event-description">Live salsa music performance featuring local bands and dance competitions. Join us for an evening of authentic Caribbean rhythms.</p>
                        <div class="event-actions">
                            <a href="#" class="btn btn-primary">Join Event</a>
                            <a href="#" class="btn btn-secondary">Share</a>
                        </div>
                    </div>
                </div>

                <div class="event-card" data-category="tv-show">
                    <div class="event-header" style="background: linear-gradient(135deg, var(--ocean-blue), var(--steel-blue));">
                        📺
                        <div class="event-date">DEC<br>10</div>
                        <div class="event-status status-upcoming">Upcoming</div>
                    </div>
                    <div class="event-content">
                        <h3 class="event-title">Caribbean Talk Show Special</h3>
                        <div class="event-time">
                            <span>🕒</span>
                            <span>Sunday, 7:00 PM - 8:30 PM</span>
                        </div>
                        <div class="event-location">
                            <span>📍</span>
                            <span>Live on Channel 99-9</span>
                        </div>
                        <p class="event-description">Special episode featuring interviews with Caribbean artists, community leaders, and cultural discussions.</p>
                        <div class="event-actions">
                            <a href="#" class="btn btn-primary">Watch Live</a>
                            <a href="#" class="btn btn-secondary">Set Reminder</a>
                        </div>
                    </div>
                </div>

                <div class="event-card" data-category="culture">
                    <div class="event-header" style="background: linear-gradient(135deg, var(--success-green), var(--mint-accent));">
                        🌴
                        <div class="event-date">DEC<br>12</div>
                        <div class="event-status status-upcoming">Upcoming</div>
                    </div>
                    <div class="event-content">
                        <h3 class="event-title">Caribbean Heritage Day</h3>
                        <div class="event-time">
                            <span>🕒</span>
                            <span>Tuesday, 2:00 PM - 6:00 PM</span>
                        </div>
                        <div class="event-location">
                            <span>📍</span>
                            <span>Old San Juan Plaza</span>
                        </div>
                        <p class="event-description">Celebrate Caribbean heritage with traditional food, music, dance, and cultural exhibitions from across the islands.</p>
                        <div class="event-actions">
                            <a href="#" class="btn btn-primary">RSVP</a>
                            <a href="#" class="btn btn-secondary">Details</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>Caribbean Advantage TV</h4>
                    <p>Modern Caribbean entertainment on Channel 99-9. Broadcasting live from North Coast Puerto Rico.</p>
                </div>
                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <p><a href="/live">Live TV</a></p>
                    <p><a href="/radio">Radio</a></p>
                    <p><a href="/gamezone">GameZone</a></p>
                    <p><a href="/events">Events</a></p>
                </div>
                <div class="footer-section">
                    <h4>Contact</h4>
                    <p>Channel 99-9</p>
                    <p>North Coast Puerto Rico</p>
                    <p><EMAIL></p>
                </div>
                <div class="footer-section">
                    <h4>Follow Us</h4>
                    <p><a href="#">Facebook</a></p>
                    <p><a href="#">Twitter</a></p>
                    <p><a href="#">Instagram</a></p>
                    <p><a href="#">YouTube</a></p>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2023 Caribbean Advantage TV. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="/js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Navigation functionality
            const navbar = document.getElementById('navbar');
            const navToggle = document.getElementById('nav-toggle');
            const navMenu = document.getElementById('nav-menu');

            window.addEventListener('scroll', function() {
                if (window.scrollY > 50) {
                    navbar.classList.add('scrolled');
                } else {
                    navbar.classList.remove('scrolled');
                }
            });

            navToggle.addEventListener('click', function() {
                navToggle.classList.toggle('active');
                navMenu.classList.toggle('active');
            });

            // Filter functionality
            const filterBtns = document.querySelectorAll('.filter-btn');
            const eventCards = document.querySelectorAll('.event-card');

            filterBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const filter = this.getAttribute('data-filter');

                    // Update active button
                    filterBtns.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');

                    // Filter events
                    eventCards.forEach(card => {
                        const category = card.getAttribute('data-category');
                        if (filter === 'all' || category === filter) {
                            card.style.display = 'block';
                        } else {
                            card.style.display = 'none';
                        }
                    });
                });
            });
        });
    </script>
</body>
</html>