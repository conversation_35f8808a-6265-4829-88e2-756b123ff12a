# Caribbean Advantage TV - Update All Pages Script
Write-Host "🎨 Caribbean Advantage Channel 99-9 - Updating All Pages" -ForegroundColor Cyan
Write-Host "=======================================================" -ForegroundColor Cyan
Write-Host ""

# Change to project directory
$projectPath = "C:\Users\<USER>\Documents\augment-projects\CAribbean Advantage TV"
Set-Location $projectPath

Write-Host "📍 Working in: $projectPath" -ForegroundColor Blue
Write-Host ""

# Function to update color scheme in files
function Update-ColorScheme {
    param($filePath)
    
    Write-Host "🎨 Updating color scheme in: $filePath" -ForegroundColor Yellow
    
    # Read file content
    $content = Get-Content $filePath -Raw
    
    # Replace old color scheme with new one
    $content = $content -replace "'ca-blue': '#003d6b'", "'primary-blue': '#1E90FF'"
    $content = $content -replace "'ca-dark-blue': '#001f3f'", "'primary-blue-dark': '#1873CC'"
    $content = $content -replace "'ca-light-blue': '#0066cc'", "'primary-blue-light': '#4FA8FF'"
    $content = $content -replace "'ca-accent-blue': '#4a90e2'", "'primary-blue-ultra-light': '#87CEEB'"
    $content = $content -replace "'ca-yellow': '#ffc107'", "'light-grey': '#F8F9FA'"
    $content = $content -replace "'ca-orange': '#ff9800'", "'medium-grey': '#E9ECEF'"
    $content = $content -replace "'ca-green': '#4caf50'", "'dark-grey': '#6C757D'"
    $content = $content -replace "'ca-dark': '#1a1a1a'", "'white': '#FFFFFF'"
    $content = $content -replace "'ca-gray': '#2d3748'", "'black': '#212529'"
    $content = $content -replace "'ca-light-gray': '#f7fafc'", "'light-grey': '#F8F9FA'"
    
    # Replace class names
    $content = $content -replace "text-ca-blue", "text-primary-blue"
    $content = $content -replace "text-ca-dark-blue", "text-primary-blue-dark"
    $content = $content -replace "text-ca-light-blue", "text-primary-blue-light"
    $content = $content -replace "text-ca-yellow", "text-primary-blue-ultra-light"
    $content = $content -replace "text-ca-orange", "text-primary-blue-light"
    $content = $content -replace "text-ca-dark", "text-contrast-dark"
    $content = $content -replace "text-ca-gray", "text-contrast-medium"
    
    $content = $content -replace "bg-ca-blue", "bg-primary-blue"
    $content = $content -replace "bg-ca-dark-blue", "bg-primary-blue-dark"
    $content = $content -replace "bg-ca-light-blue", "bg-primary-blue-light"
    $content = $content -replace "bg-ca-yellow", "bg-primary-blue-ultra-light"
    $content = $content -replace "bg-ca-orange", "bg-primary-blue-light"
    $content = $content -replace "bg-ca-dark", "bg-primary-blue-dark"
    $content = $content -replace "bg-ca-light-gray", "bg-light-grey"
    
    $content = $content -replace "from-ca-blue", "from-primary-blue"
    $content = $content -replace "to-ca-blue", "to-primary-blue"
    $content = $content -replace "from-ca-orange", "from-primary-blue-light"
    $content = $content -replace "to-ca-orange", "to-primary-blue-light"
    $content = $content -replace "from-ca-yellow", "from-primary-blue-ultra-light"
    $content = $content -replace "to-ca-yellow", "to-primary-blue-ultra-light"
    
    # Update background colors
    $content = $content -replace "background: linear-gradient\(135deg, #f7fafc 0%, #edf2f7 100%\)", "background: linear-gradient(135deg, #F8F9FA 0%, #E9ECEF 100%)"
    $content = $content -replace "color: #1a1a1a", "color: #1a202c"
    
    # Add contrast classes
    $content = $content -replace "color: #212529", "color: #1a202c"
    
    # Write updated content back to file
    Set-Content $filePath -Value $content -Encoding UTF8
    
    Write-Host "✅ Updated: $filePath" -ForegroundColor Green
}

# Function to add mobile responsive styles
function Add-ResponsiveStyles {
    param($filePath)
    
    Write-Host "📱 Adding responsive styles to: $filePath" -ForegroundColor Yellow
    
    $content = Get-Content $filePath -Raw
    
    # Check if responsive styles already exist
    if ($content -notmatch "@media \(max-width: 768px\)") {
        # Add responsive styles before </style>
        $responsiveCSS = @"

        /* High contrast text for accessibility */
        .text-contrast-dark {
            color: #1a202c !important;
        }

        .text-contrast-light {
            color: #ffffff !important;
        }

        .text-contrast-medium {
            color: #4a5568 !important;
        }

        /* Mobile responsive fixes */
        @media (max-width: 768px) {
            .hero-bg {
                min-height: 80vh;
                padding: 2rem 0;
            }
            
            .text-5xl {
                font-size: 2.5rem;
            }
            
            .text-7xl {
                font-size: 3.5rem;
            }
            
            .container {
                padding-left: 1rem;
                padding-right: 1rem;
            }

            .grid {
                gap: 1rem;
            }

            .space-x-8 > * + * {
                margin-left: 1rem;
            }
        }

        /* Ensure no overlapping elements */
        .fixed {
            z-index: 50;
        }

        .relative {
            z-index: 10;
        }

        /* Professional spacing */
        section {
            padding-top: 5rem;
            padding-bottom: 5rem;
        }
"@
        
        $content = $content -replace "</style>", "$responsiveCSS`n    </style>"
        Set-Content $filePath -Value $content -Encoding UTF8
        
        Write-Host "✅ Added responsive styles to: $filePath" -ForegroundColor Green
    }
}

# Update all HTML files
$htmlFiles = @(
    "views\index.html",
    "views\live.html", 
    "views\contact.html",
    "views\events.html",
    "views\radio.html",
    "views\404.html"
)

foreach ($file in $htmlFiles) {
    if (Test-Path $file) {
        Write-Host "🔧 Processing: $file" -ForegroundColor Blue
        Update-ColorScheme $file
        Add-ResponsiveStyles $file
        Write-Host "✅ Completed: $file" -ForegroundColor Green
        Write-Host ""
    } else {
        Write-Host "⚠️ File not found: $file" -ForegroundColor Yellow
    }
}

Write-Host "🎉 SUCCESS! All pages updated!" -ForegroundColor Green
Write-Host "==============================" -ForegroundColor Cyan
Write-Host ""
Write-Host "✅ Updated Features:" -ForegroundColor Yellow
Write-Host "   • Consistent color scheme across all pages" -ForegroundColor Green
Write-Host "   • High contrast text for accessibility" -ForegroundColor Green
Write-Host "   • Mobile responsive design" -ForegroundColor Green
Write-Host "   • Professional spacing and layout" -ForegroundColor Green
Write-Host "   • No overlapping UI elements" -ForegroundColor Green
Write-Host ""
Write-Host "📱 All pages are now mobile-ready and professional!" -ForegroundColor Cyan
