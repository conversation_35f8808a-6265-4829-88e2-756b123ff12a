<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page Not Found - Caribbean Advantage TV</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/css/styles.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'ca-blue': '#003d6b',
                        'ca-dark-blue': '#001f3f',
                        'ca-light-blue': '#0066cc',
                        'ca-accent-blue': '#4a90e2',
                        'ca-yellow': '#ffc107',
                        'ca-orange': '#ff9800',
                        'ca-green': '#4caf50',
                        'ca-dark': '#1a1a1a',
                        'ca-gray': '#2d3748',
                        'ca-light-gray': '#f7fafc',
                    }
                }
            }
        }
    </script>
    
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #003d6b 0%, #001f3f 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .float-animation {
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #ffc107, #ff9800);
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(255, 193, 7, 0.3);
        }
    </style>
</head>
<body>
    <div class="text-center px-4">
        <div class="float-animation mb-8">
            <div class="text-9xl mb-4">📺</div>
        </div>
        
        <h1 class="text-6xl md:text-8xl font-bold mb-4 text-ca-yellow">404</h1>
        <h2 class="text-2xl md:text-4xl font-bold mb-6">Page Not Found</h2>
        <p class="text-lg md:text-xl mb-8 max-w-2xl mx-auto text-gray-300">
            Oops! The page you're looking for seems to have gone off the air. 
            Let's get you back to watching some great Caribbean content!
        </p>
        
        <div class="space-y-4 md:space-y-0 md:space-x-4 md:flex md:justify-center">
            <a href="/" class="btn-primary text-ca-dark px-8 py-4 rounded-lg font-bold text-lg inline-block">
                🏠 Go Home
            </a>
            <a href="/live" class="bg-red-500 hover:bg-red-600 text-white px-8 py-4 rounded-lg font-bold text-lg inline-block transition-colors">
                📺 Watch Live TV
            </a>
        </div>
        
        <div class="mt-12">
            <p class="text-sm text-gray-400">
                <span class="text-ca-blue font-bold">Caribbean</span> 
                <span class="text-ca-orange font-bold">Advantage</span> 
                <span class="text-ca-blue font-bold">TV</span>
            </p>
        </div>
    </div>
    
    <script src="/js/main.js"></script>
</body>
</html>
