'use client'

import { motion } from 'framer-motion'
import { Navigation } from './components/Navigation'
import { HeroSection } from './components/HeroSection'
import { ServicesSection } from './components/ServicesSection'
import { LiveStatusBar } from './components/LiveStatusBar'
import { Footer } from './components/Footer'
import { FloatingElements } from './components/FloatingElements'

export default function Home() {
  return (
    <main className="relative min-h-screen">
      <Navigation />
      <FloatingElements />
      
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1 }}
        className="relative z-10"
      >
        <HeroSection />
        <ServicesSection />
        <LiveStatusBar />
        <Footer />
      </motion.div>
    </main>
  )
}
