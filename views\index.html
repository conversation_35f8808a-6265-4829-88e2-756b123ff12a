<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Caribbean Advantage TV - Modern Caribbean Entertainment | Channel 99-9</title>
    <meta name="description" content="Caribbean Advantage Channel 99-9 - Modern Caribbean entertainment with live TV, online radio, and interactive gaming. Clean, fresh, and digital-forward.">

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Styles -->
    <link rel="stylesheet" href="/css/styles.css">
</head>
<body>
    <!-- Modern Navigation -->
    <nav class="navbar" id="navbar">
        <div class="container">
            <div class="nav-container">
                <!-- Logo -->
                <a href="/" class="nav-logo">
                    <div class="nav-logo-icon">CA</div>
                    <span>Caribbean Advantage</span>
                </a>

                <!-- Desktop Menu -->
                <ul class="nav-menu" id="nav-menu">
                    <li><a href="/" class="nav-link active">Home</a></li>
                    <li><a href="/live" class="nav-link">Live TV</a></li>
                    <li><a href="/gamezone" class="nav-link">GameZone</a></li>
                    <li><a href="/radio" class="nav-link">Radio</a></li>
                    <li><a href="/events" class="nav-link">Events</a></li>
                    <li><a href="/contact" class="nav-link">Contact</a></li>
                </ul>

                <!-- Mobile Toggle -->
                <button class="nav-toggle" id="nav-toggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <h1>Modern Caribbean Entertainment</h1>
                <p>Experience the future of Caribbean media with Channel 99-9. Clean, fresh, and digital-forward entertainment featuring live TV, interactive gaming, and premium radio content.</p>

                <div class="d-flex justify-center" style="gap: var(--spacing-md); flex-wrap: wrap;">
                    <a href="/live" class="btn btn-primary">
                        📺 Watch Live TV
                    </a>
                    <a href="/radio" class="btn btn-secondary">
                        🎵 Listen to Radio
                    </a>
                    <a href="/gamezone" class="btn btn-ghost">
                        🎮 Play Games
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Content Section -->
    <section class="section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Featured Content</h2>
                <p class="section-subtitle">Discover our latest shows, games, and radio programs designed for modern Caribbean entertainment.</p>
            </div>

            <div class="content-grid">
                <!-- Live TV Card -->
                <div class="media-player">
                    <div class="media-thumbnail">
                        📺
                        <button class="play-button">▶</button>
                    </div>
                    <div class="media-info">
                        <h3 class="media-title">Live Caribbean TV</h3>
                        <p class="media-description">Watch live broadcasts featuring local news, entertainment, and cultural programming from North Coast Puerto Rico.</p>
                        <a href="/live" class="btn btn-primary" style="margin-top: var(--spacing-sm);">Watch Now</a>
                    </div>
                </div>

                <!-- GameZone Card -->
                <div class="media-player">
                    <div class="media-thumbnail" style="background: linear-gradient(135deg, var(--coral-accent), var(--mint-accent));">
                        🎮
                        <button class="play-button">▶</button>
                    </div>
                    <div class="media-info">
                        <h3 class="media-title">Interactive GameZone</h3>
                        <p class="media-description">Engage with our collection of interactive games, tournaments, and gaming content designed for all ages.</p>
                        <a href="/gamezone" class="btn btn-primary" style="margin-top: var(--spacing-sm);">Play Now</a>
                    </div>
                </div>

                <!-- Radio Card -->
                <div class="media-player">
                    <div class="media-thumbnail" style="background: linear-gradient(135deg, var(--success-green), var(--mint-accent));">
                        🎵
                        <button class="play-button">▶</button>
                    </div>
                    <div class="media-info">
                        <h3 class="media-title">Caribbean Radio</h3>
                        <p class="media-description">Listen to the best Caribbean music, local artists, and talk shows broadcasting 24/7 from Channel 99-9.</p>
                        <a href="/radio" class="btn btn-primary" style="margin-top: var(--spacing-sm);">Listen Live</a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Quick Access Section -->
    <section class="section" style="background: var(--light-gray);">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Quick Access</h2>
                <p class="section-subtitle">Jump straight into your favorite content with our streamlined access points.</p>
            </div>

            <div class="content-grid">
                <div class="floating-card text-center">
                    <div style="font-size: 3rem; margin-bottom: var(--spacing-sm);">📺</div>
                    <h3>Live Streaming</h3>
                    <p>High-quality live broadcasts available 24/7</p>
                    <a href="/live" class="btn btn-primary">Watch Live</a>
                </div>

                <div class="floating-card text-center">
                    <div style="font-size: 3rem; margin-bottom: var(--spacing-sm);">🎵</div>
                    <h3>Radio Shows</h3>
                    <p>Caribbean music and talk shows</p>
                    <a href="/radio" class="btn btn-primary">Listen Now</a>
                </div>

                <div class="floating-card text-center">
                    <div style="font-size: 3rem; margin-bottom: var(--spacing-sm);">🎮</div>
                    <h3>Gaming Hub</h3>
                    <p>Interactive games and tournaments</p>
                    <a href="/gamezone" class="btn btn-primary">Play Games</a>
                </div>

                <div class="floating-card text-center">
                    <div style="font-size: 3rem; margin-bottom: var(--spacing-sm);">📅</div>
                    <h3>Events</h3>
                    <p>Upcoming shows and special events</p>
                    <a href="/events" class="btn btn-primary">View Events</a>
                </div>
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section class="section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">About Channel 99-9</h2>
                <p class="section-subtitle">Modern Caribbean entertainment that connects communities through digital innovation and cultural celebration.</p>
            </div>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: var(--spacing-xl); align-items: center;">
                <div>
                    <h3>Our Mission</h3>
                    <p>Caribbean Advantage TV Channel 99-9 is dedicated to providing modern, accessible, and engaging entertainment that celebrates Caribbean culture while embracing digital innovation.</p>

                    <h3 style="margin-top: var(--spacing-lg);">What We Offer</h3>
                    <ul style="list-style: none; padding: 0;">
                        <li style="margin-bottom: var(--spacing-xs);">📺 Live TV Broadcasting</li>
                        <li style="margin-bottom: var(--spacing-xs);">🎵 24/7 Radio Programming</li>
                        <li style="margin-bottom: var(--spacing-xs);">🎮 Interactive Gaming Content</li>
                        <li style="margin-bottom: var(--spacing-xs);">📱 Mobile-First Experience</li>
                        <li style="margin-bottom: var(--spacing-xs);">🌐 Community Engagement</li>
                    </ul>
                </div>

                <div class="floating-card">
                    <h3>Channel Information</h3>
                    <div style="margin: var(--spacing-md) 0;">
                        <strong>Channel:</strong> 99-9<br>
                        <strong>Location:</strong> North Coast Puerto Rico<br>
                        <strong>Broadcasting:</strong> 24/7 Live<br>
                        <strong>Quality:</strong> HD Digital<br>
                        <strong>Languages:</strong> English, Spanish, French, Kiswahili
                    </div>
                    <a href="/contact" class="btn btn-primary w-full justify-center">Contact Us</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>Caribbean Advantage TV</h4>
                    <p>Modern Caribbean entertainment on Channel 99-9. Broadcasting live from North Coast Puerto Rico with digital-forward content and community focus.</p>
                </div>

                <div class="footer-section">
                    <h4>Quick Links</h4>
                    <p><a href="/live">Live TV</a></p>
                    <p><a href="/radio">Radio</a></p>
                    <p><a href="/gamezone">GameZone</a></p>
                    <p><a href="/events">Events</a></p>
                </div>

                <div class="footer-section">
                    <h4>Contact</h4>
                    <p>Channel 99-9</p>
                    <p>North Coast Puerto Rico</p>
                    <p><EMAIL></p>
                </div>

                <div class="footer-section">
                    <h4>Follow Us</h4>
                    <p><a href="#">Facebook</a></p>
                    <p><a href="#">Twitter</a></p>
                    <p><a href="#">Instagram</a></p>
                    <p><a href="#">YouTube</a></p>
                </div>
            </div>

            <div class="footer-bottom">
                <p>&copy; 2023 Caribbean Advantage TV. All rights reserved. Modern Caribbean Entertainment.</p>
            </div>
        </div>
    </footer>

    <!-- Now Playing Bar -->
    <div class="now-playing" id="now-playing">
        <div class="now-playing-content">
            <div class="now-playing-info">
                <div class="now-playing-artwork">🎵</div>
                <div class="now-playing-text">
                    <h4 id="now-playing-title">Caribbean Vibes Radio</h4>
                    <p id="now-playing-artist">Live from Channel 99-9</p>
                </div>
            </div>

            <div class="now-playing-controls">
                <button class="control-btn" id="prev-btn">⏮️</button>
                <button class="control-btn" id="play-pause-btn">▶️</button>
                <button class="control-btn" id="next-btn">⏭️</button>
                <button class="control-btn" id="close-player">✕</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/js/main.js"></script>
    <script>
        // Modern navigation functionality
        document.addEventListener('DOMContentLoaded', function() {
            const navbar = document.getElementById('navbar');
            const navToggle = document.getElementById('nav-toggle');
            const navMenu = document.getElementById('nav-menu');
            const nowPlaying = document.getElementById('now-playing');

            // Navbar scroll effect
            window.addEventListener('scroll', function() {
                if (window.scrollY > 50) {
                    navbar.classList.add('scrolled');
                } else {
                    navbar.classList.remove('scrolled');
                }
            });

            // Mobile menu toggle
            navToggle.addEventListener('click', function() {
                navToggle.classList.toggle('active');
                navMenu.classList.toggle('active');
            });

            // Show now playing bar after 3 seconds
            setTimeout(() => {
                nowPlaying.classList.add('active');
            }, 3000);

            // Close now playing
            document.getElementById('close-player').addEventListener('click', function() {
                nowPlaying.classList.remove('active');
            });

            // Play/pause functionality
            document.getElementById('play-pause-btn').addEventListener('click', function() {
                const btn = this;
                if (btn.textContent === '▶️') {
                    btn.textContent = '⏸️';
                } else {
                    btn.textContent = '▶️';
                }
            });
        });
    </script>
</body>
</html>