<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-i18n="home.title">Caribbean Advantage - Luxury Media Experience | Channel 99-9</title>
    <meta name="description" content="Caribbean Advantage Channel 99-9 - Premium Caribbean entertainment with luxury gaming, online radio, and live TV. Experience the finest in tropical media elegance.">

    <!-- Stylesheets -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/css/styles.css">

    <!-- Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'deep-gold': '#D4AF37',
                        'rich-gold': '#FFD700',
                        'warm-black': '#0A0A0A',
                        'charcoal': '#1A1A1A',
                        'soft-ivory': '#FFF8DC',
                        'pearl-white': '#F8F6F0',
                        'coral': '#FF6B6B',
                        'teal': '#4ECDC4',
                        'emerald': '#50E3C2'
                    },
                    fontFamily: {
                        'luxury': ['Inter', 'SF Pro Display', '-apple-system', 'BlinkMacSystemFont', 'sans-serif']
                    }
                }
            }
        }
    </script>
</head>
<body class="font-luxury">
    <!-- Luxury Header -->
    <header class="fixed top-0 left-0 right-0 z-50 glass-card border-0 border-b border-deep-gold/20">
        <div class="container mx-auto">
            <div class="flex justify-between items-center py-4">
                <!-- Logo -->
                <div class="flex items-center space-x-4">
                    <div class="w-14 h-14 bg-gradient-to-br from-deep-gold to-rich-gold rounded-2xl flex items-center justify-center shadow-lg">
                        <span class="text-warm-black font-black text-xl">CA</span>
                    </div>
                    <div class="text-2xl font-black">
                        <span class="text-soft-ivory">Caribbean</span>
                        <span class="text-deep-gold">Advantage</span>
                    </div>
                </div>

                <!-- Desktop Navigation -->
                <nav class="hidden md:flex space-x-8">
                    <a href="/" class="text-deep-gold font-bold text-sm uppercase tracking-wider" data-i18n="nav.home">Home</a>
                    <a href="/live" class="text-soft-ivory hover:text-deep-gold transition-colors font-medium text-sm uppercase tracking-wider" data-i18n="nav.online-tv">Live TV</a>
                    <a href="/gamezone" class="text-soft-ivory hover:text-deep-gold transition-colors font-medium text-sm uppercase tracking-wider" data-i18n="nav.gamezone">GameZone</a>
                    <a href="/radio" class="text-soft-ivory hover:text-deep-gold transition-colors font-medium text-sm uppercase tracking-wider" data-i18n="nav.radio">Radio</a>
                </nav>

                <!-- Right Actions -->
                <div class="hidden md:flex items-center space-x-4">
                    <!-- Language Switcher -->
                    <div class="relative">
                        <button id="language-toggle" class="flex items-center space-x-2 text-soft-ivory/90 hover:text-soft-ivory transition-colors">
                            <span class="text-lg">🌍</span>
                            <span id="current-language" class="text-sm font-medium">English</span>
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div id="language-dropdown" class="absolute right-0 mt-2 w-48 bg-charcoal rounded-xl shadow-2xl border border-deep-gold/30 hidden z-50">
                            <a href="#" onclick="changeLanguage('en')" class="flex items-center px-4 py-3 text-sm text-soft-ivory hover:bg-deep-gold/20 rounded-t-xl">
                                <span class="mr-3">🇺🇸</span> English
                            </a>
                            <a href="#" onclick="changeLanguage('es')" class="flex items-center px-4 py-3 text-sm text-soft-ivory hover:bg-deep-gold/20">
                                <span class="mr-3">🇪🇸</span> Español
                            </a>
                            <a href="#" onclick="changeLanguage('fr')" class="flex items-center px-4 py-3 text-sm text-soft-ivory hover:bg-deep-gold/20">
                                <span class="mr-3">🇫🇷</span> Français
                            </a>
                            <a href="#" onclick="changeLanguage('sw')" class="flex items-center px-4 py-3 text-sm text-soft-ivory hover:bg-deep-gold/20 rounded-b-xl">
                                <span class="mr-3">🌍</span> Kiswahili
                            </a>
                        </div>
                    </div>

                    <a href="/live" class="btn-luxury">
                        <span>📺</span> Watch Live
                    </a>
                </div>

                <!-- Mobile Menu Button -->
                <div class="md:hidden">
                    <button id="menu-toggle" class="text-soft-ivory focus:outline-none">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Mobile Menu -->
            <div id="mobile-menu" class="md:hidden hidden pb-4">
                <div class="flex flex-col space-y-4">
                    <a href="/" class="text-deep-gold font-bold text-sm uppercase tracking-wider" data-i18n="nav.home">Home</a>
                    <a href="/live" class="text-soft-ivory hover:text-deep-gold transition-colors font-medium text-sm uppercase tracking-wider" data-i18n="nav.online-tv">Live TV</a>
                    <a href="/gamezone" class="text-soft-ivory hover:text-deep-gold transition-colors font-medium text-sm uppercase tracking-wider" data-i18n="nav.gamezone">GameZone</a>
                    <a href="/radio" class="text-soft-ivory hover:text-deep-gold transition-colors font-medium text-sm uppercase tracking-wider" data-i18n="nav.radio">Radio</a>
                    <div class="border-t border-deep-gold/20 pt-4 mt-4">
                        <a href="/events" class="text-soft-ivory/80 hover:text-soft-ivory transition-colors font-medium block mb-3 text-sm uppercase tracking-wider" data-i18n="nav.events">Events</a>
                        <a href="/contact" class="text-soft-ivory/80 hover:text-soft-ivory transition-colors font-medium block mb-4 text-sm uppercase tracking-wider" data-i18n="nav.contact">Contact</a>
                        <a href="/live" class="btn-luxury w-full justify-center">📺 Watch Live</a>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Cinematic Hero Section -->
    <section class="hero-cinematic relative">
        <!-- Background Video Placeholder -->
        <div class="absolute inset-0 bg-gradient-to-br from-warm-black via-charcoal to-warm-black">
            <div class="absolute inset-0 bg-[url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="luxury-grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="0.5" fill="white" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23luxury-grain)"/></svg>')] opacity-50"></div>
        </div>

        <div class="container mx-auto relative z-10 pt-32 pb-20">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <!-- Hero Content -->
                <div class="space-y-8">
                    <div class="space-y-4">
                        <div class="inline-flex items-center space-x-2 bg-deep-gold/20 backdrop-blur-sm px-4 py-2 rounded-full border border-deep-gold/30">
                            <span class="w-2 h-2 bg-deep-gold rounded-full animate-pulse"></span>
                            <span class="text-deep-gold text-sm font-semibold uppercase tracking-wider">Live Broadcasting</span>
                        </div>

                        <h1 class="text-6xl lg:text-8xl font-black leading-none">
                            <span class="text-soft-ivory">Caribbean</span><br>
                            <span class="bg-gradient-to-r from-deep-gold via-rich-gold to-deep-gold bg-clip-text text-transparent">Advantage</span>
                        </h1>

                        <p class="text-xl text-soft-ivory/80 max-w-lg leading-relaxed">
                            Experience premium Caribbean entertainment with luxury gaming, sophisticated radio, and cinematic live TV on Channel 99-9.
                        </p>
                    </div>

                    <div class="flex flex-wrap gap-4">
                        <a href="/live" class="btn-luxury">
                            <span>🎬</span> Watch Live
                        </a>
                        <a href="/radio" class="btn-glass">
                            <span>🎵</span> Listen Now
                        </a>
                    </div>

                    <!-- Feature Tags -->
                    <div class="flex flex-wrap gap-3">
                        <div class="floating-card px-4 py-2">
                            <span class="text-coral">🎮</span> Premium Gaming
                        </div>
                        <div class="floating-card px-4 py-2">
                            <span class="text-teal">📻</span> Luxury Radio
                        </div>
                        <div class="floating-card px-4 py-2">
                            <span class="text-emerald">📺</span> Live TV
                        </div>
                    </div>
                </div>

                <!-- Hero Media Preview -->
                <div class="relative">
                    <div class="glass-card p-8 text-center">
                        <div class="text-8xl mb-6">📺</div>
                        <h3 class="text-3xl font-bold text-soft-ivory mb-4">Channel 99-9</h3>
                        <p class="text-soft-ivory/70 mb-6">North Coast Puerto Rico</p>

                        <div class="flex justify-center space-x-3 mb-6">
                            <div class="bg-coral text-warm-black px-4 py-2 rounded-full text-sm font-bold animate-pulse">
                                🔴 LIVE
                            </div>
                            <div class="bg-deep-gold text-warm-black px-4 py-2 rounded-full text-sm font-bold">
                                4K HD
                            </div>
                        </div>

                        <div class="text-sm text-soft-ivory/60 space-y-2">
                            <div>🎮 Premium Gaming • 🎵 Luxury Radio • 📺 Cinematic TV</div>
                            <div>Supporting Caribbean Artists</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Media Section -->
    <section class="py-24 relative">
        <div class="container mx-auto">
            <!-- Section Header -->
            <div class="text-center mb-16">
                <h2 class="text-5xl font-black text-soft-ivory mb-6">
                    Premium <span class="text-deep-gold">Entertainment</span>
                </h2>
                <p class="text-xl text-soft-ivory/70 max-w-3xl mx-auto">
                    Immerse yourself in our curated collection of luxury gaming, sophisticated radio, and cinematic television experiences.
                </p>
            </div>

            <!-- Media Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Live TV Card -->
                <div class="media-card group">
                    <div class="relative h-64 bg-gradient-to-br from-coral to-teal rounded-t-2xl overflow-hidden">
                        <div class="absolute inset-0 flex items-center justify-center">
                            <span class="text-6xl">📺</span>
                        </div>
                        <div class="absolute top-4 left-4">
                            <div class="bg-coral text-warm-black px-3 py-1 rounded-full text-xs font-bold animate-pulse">
                                🔴 LIVE
                            </div>
                        </div>
                        <div class="absolute inset-0 bg-gradient-to-t from-warm-black/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-2xl font-bold text-soft-ivory mb-3">Cinematic TV</h3>
                        <p class="text-soft-ivory/70 mb-4">Premium live television broadcasting with 4K quality and immersive Caribbean content.</p>
                        <a href="/live" class="btn-glass text-sm">
                            <span>🎬</span> Watch Now
                        </a>
                    </div>
                </div>

                <!-- GameZone Card -->
                <div class="game-zone-card group">
                    <div class="relative h-64 bg-gradient-to-br from-coral via-teal to-emerald rounded-t-2xl overflow-hidden">
                        <div class="absolute inset-0 flex items-center justify-center">
                            <span class="text-6xl">🎮</span>
                        </div>
                        <div class="absolute top-4 left-4">
                            <div class="bg-emerald text-warm-black px-3 py-1 rounded-full text-xs font-bold">
                                PREMIUM
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-2xl font-bold text-soft-ivory mb-3">Luxury Gaming</h3>
                        <p class="text-soft-ivory/70 mb-4">Exclusive gaming experiences with tournaments, streams, and interactive entertainment.</p>
                        <a href="/gamezone" class="btn-glass text-sm">
                            <span>🎮</span> Play Now
                        </a>
                    </div>
                </div>

                <!-- Radio Card -->
                <div class="media-card group">
                    <div class="relative h-64 bg-gradient-to-br from-deep-gold to-rich-gold rounded-t-2xl overflow-hidden">
                        <div class="absolute inset-0 flex items-center justify-center">
                            <span class="text-6xl">🎵</span>
                        </div>
                        <div class="absolute top-4 left-4">
                            <div class="bg-deep-gold text-warm-black px-3 py-1 rounded-full text-xs font-bold">
                                24/7
                            </div>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-2xl font-bold text-soft-ivory mb-3">Sophisticated Radio</h3>
                        <p class="text-soft-ivory/70 mb-4">Curated Caribbean music, local artists, and premium audio experiences.</p>
                        <a href="/radio" class="btn-glass text-sm">
                            <span>🎵</span> Listen Live
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Social Media Integration -->
    <section class="py-20 bg-gradient-to-br from-charcoal to-warm-black">
        <div class="container mx-auto">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-black text-soft-ivory mb-4">
                    Connect with <span class="text-deep-gold">Caribbean Advantage</span>
                </h2>
                <p class="text-soft-ivory/70 text-lg">Follow us across all platforms for the latest updates and exclusive content.</p>
            </div>

            <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                <!-- Instagram -->
                <a href="https://instagram.com/caribbeanadvantage" target="_blank" class="floating-card text-center group">
                    <div class="text-4xl mb-4">📸</div>
                    <h4 class="text-soft-ivory font-semibold mb-2">Instagram</h4>
                    <p class="text-soft-ivory/60 text-sm">Visual stories & behind the scenes</p>
                </a>

                <!-- TikTok -->
                <a href="https://tiktok.com/@caribbeanadvantage" target="_blank" class="floating-card text-center group">
                    <div class="text-4xl mb-4">🎵</div>
                    <h4 class="text-soft-ivory font-semibold mb-2">TikTok</h4>
                    <p class="text-soft-ivory/60 text-sm">Short-form entertainment</p>
                </a>

                <!-- Twitter -->
                <a href="https://twitter.com/caribbeanadvantage" target="_blank" class="floating-card text-center group">
                    <div class="text-4xl mb-4">🐦</div>
                    <h4 class="text-soft-ivory font-semibold mb-2">Twitter</h4>
                    <p class="text-soft-ivory/60 text-sm">Live updates & news</p>
                </a>

                <!-- YouTube -->
                <a href="https://youtube.com/caribbeanadvantage" target="_blank" class="floating-card text-center group">
                    <div class="text-4xl mb-4">📺</div>
                    <h4 class="text-soft-ivory font-semibold mb-2">YouTube</h4>
                    <p class="text-soft-ivory/60 text-sm">Full episodes & highlights</p>
                </a>
            </div>
        </div>
    </section>

    <!-- Luxury Footer -->
    <footer class="bg-gradient-to-br from-warm-black to-charcoal py-16 border-t border-deep-gold/20">
        <div class="container mx-auto">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-12">
                <!-- Brand Section -->
                <div class="md:col-span-2">
                    <div class="flex items-center space-x-4 mb-6">
                        <div class="w-16 h-16 bg-gradient-to-br from-deep-gold to-rich-gold rounded-2xl flex items-center justify-center">
                            <span class="text-warm-black font-black text-2xl">CA</span>
                        </div>
                        <div class="text-3xl font-black">
                            <span class="text-soft-ivory">Caribbean</span>
                            <span class="text-deep-gold">Advantage</span>
                        </div>
                    </div>
                    <p class="text-soft-ivory/70 mb-6 text-lg leading-relaxed">
                        Channel 99-9 - Premium Caribbean entertainment featuring luxury gaming, sophisticated radio, and cinematic television. Supporting local artists with world-class production.
                    </p>
                    <div class="flex space-x-6">
                        <a href="https://facebook.com/caribbeanadvantage" target="_blank" class="text-soft-ivory/60 hover:text-deep-gold transition-colors text-lg">Facebook</a>
                        <a href="https://twitter.com/caribbeanadvantage" target="_blank" class="text-soft-ivory/60 hover:text-deep-gold transition-colors text-lg">Twitter</a>
                        <a href="https://instagram.com/caribbeanadvantage" target="_blank" class="text-soft-ivory/60 hover:text-deep-gold transition-colors text-lg">Instagram</a>
                        <a href="https://youtube.com/caribbeanadvantage" target="_blank" class="text-soft-ivory/60 hover:text-deep-gold transition-colors text-lg">YouTube</a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="text-xl font-bold text-soft-ivory mb-6" data-i18n="footer.quick-links">Premium Content</h4>
                    <ul class="space-y-3">
                        <li><a href="/live" class="text-soft-ivory/70 hover:text-deep-gold transition-colors font-medium" data-i18n="nav.online-tv">Cinematic TV</a></li>
                        <li><a href="/gamezone" class="text-soft-ivory/70 hover:text-deep-gold transition-colors font-medium" data-i18n="nav.gamezone">Luxury Gaming</a></li>
                        <li><a href="/radio" class="text-soft-ivory/70 hover:text-deep-gold transition-colors font-medium" data-i18n="nav.radio">Sophisticated Radio</a></li>
                        <li><a href="/events" class="text-soft-ivory/70 hover:text-deep-gold transition-colors font-medium" data-i18n="nav.events">Exclusive Events</a></li>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div>
                    <h4 class="text-xl font-bold text-soft-ivory mb-6" data-i18n="footer.contact">Contact</h4>
                    <ul class="space-y-3 text-soft-ivory/70">
                        <li class="flex items-center space-x-2">
                            <span>📺</span>
                            <span data-i18n="nav.channel">Channel 99-9</span>
                        </li>
                        <li class="flex items-center space-x-2">
                            <span>📍</span>
                            <span data-i18n="nav.location">North Coast Puerto Rico</span>
                        </li>
                        <li class="flex items-center space-x-2">
                            <span>📧</span>
                            <span><EMAIL></span>
                        </li>
                        <li class="flex items-center space-x-2">
                            <span>🌐</span>
                            <span>caribbeanadvantage.com</span>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-deep-gold/20 mt-12 pt-8 text-center">
                <p class="text-soft-ivory/60" data-i18n="footer.copyright">© 2023 Caribbean Advantage TV. All rights reserved. Premium Caribbean Entertainment.</p>
            </div>
        </div>
    </footer>

    <!-- Now Playing Bar -->
    <div id="now-playing-bar" class="now-playing-bar">
        <div class="now-playing-content">
            <div class="now-playing-info">
                <div class="now-playing-artwork">
                    🎵
                </div>
                <div class="now-playing-text">
                    <h4 id="now-playing-title">Caribbean Vibes Radio</h4>
                    <p id="now-playing-artist">Live from Channel 99-9</p>
                </div>
            </div>

            <div class="now-playing-controls">
                <button class="control-btn" id="prev-btn">⏮️</button>
                <button class="control-btn" id="play-pause-btn">▶️</button>
                <button class="control-btn" id="next-btn">⏭️</button>
                <button class="control-btn" id="close-player">✕</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="/js/i18n.js"></script>
    <script src="/js/main.js"></script>

    <!-- Luxury Interactions Script -->
    <script>
        // Now Playing Bar functionality
        function showNowPlaying(title, artist) {
            document.getElementById('now-playing-title').textContent = title;
            document.getElementById('now-playing-artist').textContent = artist;
            document.getElementById('now-playing-bar').classList.add('active');
        }

        function hideNowPlaying() {
            document.getElementById('now-playing-bar').classList.remove('active');
        }

        // Auto-show now playing when radio/TV is accessed
        document.addEventListener('DOMContentLoaded', function() {
            // Simulate now playing for demo
            setTimeout(() => {
                showNowPlaying('Caribbean Vibes Radio', 'Live from Channel 99-9');
            }, 2000);
        });

        // Close player
        document.getElementById('close-player').addEventListener('click', hideNowPlaying);

        // Mobile menu toggle
        document.getElementById('menu-toggle').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenu.classList.toggle('hidden');
        });

        // Language dropdown
        document.getElementById('language-toggle').addEventListener('click', function() {
            const dropdown = document.getElementById('language-dropdown');
            dropdown.classList.toggle('hidden');
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('language-dropdown');
            const toggle = document.getElementById('language-toggle');

            if (!toggle.contains(event.target) && !dropdown.contains(event.target)) {
                dropdown.classList.add('hidden');
            }
        });
    </script>
</body>
</html>