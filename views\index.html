

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Caribbean Advantage TV</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#EFF6FF',
                            100: '#DBEAFE',
                            200: '#BFDBFE',
                            300: '#93C5FD',
                            400: '#60A5FA',
                            500: '#3B82F6',
                            600: '#2563EB',
                            700: '#1D4ED8',
                            800: '#1E40AF',
                            900: '#1E3A8A',
                        },
                        secondary: {
                            100: '#F1F5F9',
                            200: '#E2E8F0',
                            300: '#CBD5E1',
                            400: '#94A3B8',
                            500: '#64748B',
                            600: '#475569',
                            700: '#334155',
                            800: '#1E293B',
                            900: '#0F172A',
                        },
                        accent: {
                            100: '#E0F2FE',
                            200: '#BAE6FD',
                            300: '#7DD3FC',
                            400: '#38BDF8',
                            500: '#0EA5E9',
                            600: '#0284C7',
                            700: '#0369A1',
                            800: '#075985',
                            900: '#0C4A6E',
                        }
                    },
                    fontFamily: {
                        sans: ['Inter', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            scroll-behavior: smooth;
            background: linear-gradient(135deg, #003d6b 0%, #0066cc 50%, #4a90e2 100%);
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        /* Animated Background */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
            animation: backgroundFloat 20s ease-in-out infinite;
            z-index: -1;
        }

        @keyframes backgroundFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-20px) rotate(1deg); }
            66% { transform: translateY(10px) rotate(-1deg); }
        }

        /* Floating Animation Elements */
        .floating-element {
            position: fixed;
            font-size: 2rem;
            opacity: 0.1;
            animation: float 15s ease-in-out infinite;
            z-index: -1;
            pointer-events: none;
        }

        .floating-element:nth-child(1) { top: 10%; left: 10%; animation-delay: 0s; }
        .floating-element:nth-child(2) { top: 20%; right: 15%; animation-delay: 3s; }
        .floating-element:nth-child(3) { top: 60%; left: 20%; animation-delay: 6s; }
        .floating-element:nth-child(4) { bottom: 20%; right: 25%; animation-delay: 9s; }
        .floating-element:nth-child(5) { top: 40%; left: 50%; animation-delay: 12s; }

        @keyframes float {
            0%, 100% { transform: translateY(0px) translateX(0px); }
            25% { transform: translateY(-20px) translateX(10px); }
            50% { transform: translateY(10px) translateX(-5px); }
            75% { transform: translateY(-15px) translateX(15px); }
        }

        .card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .nav-link {
            position: relative;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -4px;
            left: 0;
            background-color: #2563EB;
            transition: width 0.3s ease;
        }

        .nav-link:hover::after {
            width: 100%;
        }

        .active::after {
            width: 100%;
        }

        .dropdown {
            opacity: 0;
            visibility: hidden;
            transform: translateY(10px);
            transition: opacity 0.3s ease, transform 0.3s ease, visibility 0.3s;
        }

        .dropdown-container:hover .dropdown {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .now-playing-bar {
            transform: translateY(100%);
            transition: transform 0.5s ease;
        }

        .now-playing-bar.active {
            transform: translateY(0);
        }

        .wave {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100px;
            background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%232563EB' fill-opacity='0.5' d='M0,192L48,176C96,160,192,128,288,122.7C384,117,480,139,576,165.3C672,192,768,224,864,224C960,224,1056,192,1152,176C1248,160,1344,160,1392,160L1440,160L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E");
            background-size: cover;
            background-repeat: no-repeat;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.5;
            }
        }

        .live-indicator {
            animation: pulse 2s infinite;
        }

        .calendar-day {
            transition: all 0.3s ease;
        }

        .calendar-day:hover {
            background-color: #DBEAFE;
            color: #1D4ED8;
        }

        .calendar-day.has-event {
            background-color: #3B82F6;
            color: white;
        }

        .game-card:hover .play-overlay {
            opacity: 1;
        }

        .hero-gradient {
            background: linear-gradient(135deg, #1E40AF 0%, #3B82F6 100%);
        }

        .feature-gradient {
            background: linear-gradient(135deg, #1E3A8A 0%, #1D4ED8 100%);
        }

        .blur-card {
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
        }

        .radio-wave {
            position: relative;
        }

        .radio-wave::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 150%;
            height: 150%;
            background: radial-gradient(circle, rgba(59, 130, 246, 0.2) 0%, rgba(59, 130, 246, 0) 70%);
            transform: translate(-50%, -50%);
            border-radius: 50%;
            z-index: -1;
            animation: pulse 2s infinite;
        }

        .game-card {
            overflow: hidden;
        }

        .game-card .play-overlay {
            opacity: 0;
            transition: opacity 0.3s ease;
            background: rgba(30, 64, 175, 0.7);
        }

        .event-date {
            position: relative;
            overflow: hidden;
        }

        .event-date::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(29, 78, 216, 0.4) 100%);
            z-index: -1;
        }
    </style>
</head>
<body class="text-white">
    <script>
        // Language Management
        let currentLanguage = localStorage.getItem('language') || 'en';

        const translations = {
            en: {
                'nav.home': 'Home',
                'nav.tv': 'Online TV',
                'nav.gamezone': 'GameZone',
                'nav.radio': 'Online Radio',
                'nav.events': 'Events',
                'nav.contact': 'Contact',
                'common.watch_live': '📺 Watch Live',
                'hero.title': 'Your Caribbean Entertainment Destination',
                'hero.subtitle': 'Experience the best of Caribbean TV, radio, and interactive entertainment all in one place.',
                'hero.watch_live': 'Watch Live',
                'hero.listen_radio': 'Listen Radio',
                'hero.play_games': 'Play Games'
            },
            es: {
                'nav.home': 'Inicio',
                'nav.tv': 'TV en Línea',
                'nav.gamezone': 'Zona de Juegos',
                'nav.radio': 'Radio en Línea',
                'nav.events': 'Eventos',
                'nav.contact': 'Contacto',
                'common.watch_live': '📺 Ver en Vivo',
                'hero.title': 'Tu Destino de Entretenimiento Caribeño',
                'hero.subtitle': 'Experimenta lo mejor de la TV, radio y entretenimiento interactivo caribeño en un solo lugar.',
                'hero.watch_live': 'Ver en Vivo',
                'hero.listen_radio': 'Escuchar Radio',
                'hero.play_games': 'Jugar'
            }
        };

        function changeLanguage(lang) {
            currentLanguage = lang;
            localStorage.setItem('language', lang);
            document.getElementById('current-lang').textContent = lang.toUpperCase();

            // Update all translatable elements
            document.querySelectorAll('[data-translate]').forEach(element => {
                const key = element.getAttribute('data-translate');
                if (translations[lang] && translations[lang][key]) {
                    element.textContent = translations[lang][key];
                }
            });
        }

        // Initialize language on page load
        document.addEventListener('DOMContentLoaded', function() {
            changeLanguage(currentLanguage);
        });
    </script>
    <!-- Enhanced Navigation with Blue Theme -->
    <header class="bg-gradient-to-r from-primary-800/95 to-primary-700/95 backdrop-blur-md shadow-lg fixed w-full top-0 z-50 border-b border-white/20">
        <div class="container mx-auto px-4 py-3">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-r from-primary-400 to-accent-500 rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold text-lg">CA</span>
                    </div>
                    <span class="font-bold text-xl text-white">Caribbean Advantage</span>
                </div>

                <nav class="hidden md:flex items-center space-x-8">
                    <a href="/" class="nav-link active text-white font-medium hover:text-accent-300 transition-colors" data-translate="nav.home">Home</a>
                    <a href="/live" class="nav-link text-white/90 hover:text-white font-medium transition-colors" data-translate="nav.tv">Online TV</a>
                    <a href="/gamezone" class="nav-link text-white/90 hover:text-white font-medium transition-colors" data-translate="nav.gamezone">GameZone</a>
                    <a href="/radio" class="nav-link text-white/90 hover:text-white font-medium transition-colors" data-translate="nav.radio">Online Radio</a>
                </nav>

                <div class="flex items-center space-x-4">
                    <a href="/live" class="bg-accent-500 hover:bg-accent-600 text-white px-4 py-2 rounded-lg font-medium transition-colors" data-translate="common.watch_live">
                        📺 Watch Live
                    </a>

                    <!-- Language Selector -->
                    <div class="dropdown-container relative">
                        <button class="flex items-center space-x-2 text-white/90 hover:text-white transition-colors">
                            <span class="text-lg">🌐</span>
                            <span id="current-lang" class="font-medium">EN</span>
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </button>
                        <div class="dropdown absolute top-full right-0 mt-2 w-40 bg-white rounded-md shadow-lg py-1 z-10">
                            <a href="#" onclick="changeLanguage('en')" class="language-option flex items-center px-4 py-2 text-sm text-secondary-700 hover:bg-primary-100">
                                🇺🇸 English
                            </a>
                            <a href="#" onclick="changeLanguage('es')" class="language-option flex items-center px-4 py-2 text-sm text-secondary-700 hover:bg-primary-100">
                                🇪🇸 Español
                            </a>
                        </div>
                    </div>

                    <button id="mobile-menu-btn" class="md:hidden text-white hover:text-accent-300">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div id="mobile-menu" class="hidden md:hidden bg-primary-800/95 backdrop-blur-md border-t border-white/20">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="/" class="block px-3 py-2 rounded-md text-base font-medium text-white bg-white/10" data-translate="nav.home">Home</a>
                <a href="/live" class="block px-3 py-2 rounded-md text-base font-medium text-white/90 hover:text-white hover:bg-white/10" data-translate="nav.tv">Online TV</a>
                <a href="/gamezone" class="block px-3 py-2 rounded-md text-base font-medium text-white/90 hover:text-white hover:bg-white/10" data-translate="nav.gamezone">GameZone</a>
                <a href="/radio" class="block px-3 py-2 rounded-md text-base font-medium text-white/90 hover:text-white hover:bg-white/10" data-translate="nav.radio">Online Radio</a>
            </div>
        </div>
    </header>

    <!-- Floating Animation Elements -->
    <div class="floating-element">🌊</div>
    <div class="floating-element">🏝️</div>
    <div class="floating-element">🎵</div>
    <div class="floating-element">📺</div>
    <div class="floating-element">🎮</div>

    <!-- Now Playing Bar -->
    <div id="now-playing-bar" class="now-playing-bar fixed bottom-0 left-0 w-full bg-primary-700 text-white z-40 shadow-lg active">
        <div class="container mx-auto px-4 py-3">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center">
                        <div class="w-2 h-2 bg-white rounded-full mr-2 live-indicator"></div>
                        <span class="text-xs font-semibold uppercase">Live Now</span>
                    </div>
                    <div>
                        <h4 class="font-medium">Caribbean Morning Show</h4>
                        <p class="text-xs text-primary-100">with DJ Marcus</p>
                    </div>
                </div>

                <div class="flex items-center space-x-4">
                    <button class="p-2 rounded-full bg-white/10 hover:bg-white/20">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                        </svg>
                    </button>

                    <button id="close-now-playing" class="p-2 rounded-full bg-white/10 hover:bg-white/20">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main class="pt-16">
        <!-- HOME PAGE -->
        <section id="home" class="min-h-screen">
            <!-- Hero Section -->
            <div class="relative h-[70vh] hero-gradient overflow-hidden">
                <div class="absolute inset-0 bg-gradient-to-r from-primary-900/80 to-primary-700/60 z-10"></div>
                <div class="wave z-20"></div>
                <div class="container mx-auto px-4 h-full flex items-center relative z-30">
                    <div class="max-w-2xl text-white">
                        <h1 class="text-4xl md:text-5xl font-bold mb-4">Your Caribbean Entertainment Destination</h1>
                        <p class="text-lg mb-8 text-white/90">Experience the best of Caribbean TV, radio, and interactive entertainment all in one place.</p>

                        <div class="flex flex-wrap gap-4">
                            <a href="#tv" class="bg-white text-primary-700 hover:bg-primary-50 px-6 py-3 rounded-full font-medium flex items-center shadow-lg">
                                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                                </svg>
                                Watch Live
                            </a>

                            <a href="#radio" class="bg-primary-600 text-white hover:bg-primary-500 border border-white/30 px-6 py-3 rounded-full font-medium flex items-center shadow-lg">
                                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM14.657 2.929a1 1 0 011.414 0A9.972 9.972 0 0119 10a9.972 9.972 0 01-2.929 7.071 1 1 0 01-1.414-1.414A7.971 7.971 0 0017 10c0-2.21-.894-4.208-2.343-5.657a1 1 0 010-1.414zm-2.829 2.828a1 1 0 011.415 0A5.983 5.983 0 0115 10a5.984 5.984 0 01-1.757 4.243 1 1 0 01-1.415-1.415A3.984 3.984 0 0013 10a3.983 3.983 0 00-1.172-2.828 1 1 0 010-1.415z" clip-rule="evenodd"></path>
                                </svg>
                                Listen Radio
                            </a>

                            <a href="#games" class="bg-primary-600 text-white hover:bg-primary-500 border border-white/30 px-6 py-3 rounded-full font-medium flex items-center shadow-lg">
                                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M11 17a1 1 0 001.447.894l4-2A1 1 0 0017 15V9.236a1 1 0 00-1.447-.894l-4 2a1 1 0 00-.553.894V17zM15.211 6.276a1 1 0 000-1.788l-4.764-2.382a1 1 0 00-.894 0L4.789 4.488a1 1 0 000 1.788l4.764 2.382a1 1 0 00.894 0l4.764-2.382zM4.447 8.342A1 1 0 003 9.236V15a1 1 0 00.553.894l4 2A1 1 0 009 17v-5.764a1 1 0 00-.553-.894l-4-2z"></path>
                                </svg>
                                Play Games
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Featured Content -->
            <div class="container mx-auto px-4 py-16">
                <div class="mb-12">
                    <h2 class="text-3xl font-bold text-secondary-800 mb-2">Featured Shows</h2>
                    <p class="text-secondary-500">Discover our most popular programs</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Featured Show 1 -->
                    <div class="card bg-white rounded-xl shadow-md overflow-hidden">
                        <div class="relative h-48 bg-gradient-to-br from-primary-400 to-primary-700">
                            <div class="absolute inset-0 flex items-center justify-center">
                                <svg class="w-16 h-16 text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div class="absolute top-2 left-2 bg-primary-600 text-white text-xs font-bold px-2 py-1 rounded">FEATURED</div>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-semibold mb-2">Island Vibes</h3>
                            <p class="text-secondary-500 mb-4">Join host Maria for the best music and interviews with Caribbean artists.</p>
                            <div class="flex justify-between items-center">
                                <span class="text-xs text-secondary-400">Weekdays • 8PM</span>
                                <button class="text-primary-600 hover:text-primary-700 font-medium">Watch Now</button>
                            </div>
                        </div>
                    </div>

                    <!-- Featured Show 2 -->
                    <div class="card bg-white rounded-xl shadow-md overflow-hidden">
                        <div class="relative h-48 bg-gradient-to-br from-primary-400 to-primary-700">
                            <div class="absolute inset-0 flex items-center justify-center">
                                <svg class="w-16 h-16 text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-semibold mb-2">Caribbean Cuisine</h3>
                            <p class="text-secondary-500 mb-4">Explore the rich flavors of the islands with Chef Thomas.</p>
                            <div class="flex justify-between items-center">
                                <span class="text-xs text-secondary-400">Saturdays • 6PM</span>
                                <button class="text-primary-600 hover:text-primary-700 font-medium">Watch Now</button>
                            </div>
                        </div>
                    </div>

                    <!-- Featured Show 3 -->
                    <div class="card bg-white rounded-xl shadow-md overflow-hidden">
                        <div class="relative h-48 bg-gradient-to-br from-primary-400 to-primary-700">
                            <div class="absolute inset-0 flex items-center justify-center">
                                <svg class="w-16 h-16 text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div class="absolute top-2 left-2 bg-accent-600 text-white text-xs font-bold px-2 py-1 rounded flex items-center">
                                <span class="w-2 h-2 bg-white rounded-full mr-1 live-indicator"></span>
                                LIVE
                            </div>
                        </div>
                        <div class="p-6">
                            <h3 class="text-xl font-semibold mb-2">Caribbean News Today</h3>
                            <p class="text-secondary-500 mb-4">Stay updated with the latest news and events from across the region.</p>
                            <div class="flex justify-between items-center">
                                <span class="text-xs text-secondary-400">Daily • 7AM & 7PM</span>
                                <button class="text-primary-600 hover:text-primary-700 font-medium">Watch Now</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Links Section -->
            <div class="feature-gradient py-16">
                <div class="container mx-auto px-4">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                        <!-- TV Quick Link -->
                        <div class="bg-white/10 backdrop-blur-sm rounded-xl p-8 text-white blur-card">
                            <div class="mb-4">
                                <svg class="w-12 h-12" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <h3 class="text-xl font-bold mb-2">Online TV</h3>
                            <p class="mb-4 text-primary-100">Watch your favorite shows live or on-demand. Caribbean entertainment at your fingertips.</p>
                            <a href="#tv" class="inline-flex items-center text-white font-medium">
                                Explore TV
                                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                                </svg>
                            </a>
                        </div>

                        <!-- Radio Quick Link -->
                        <div class="bg-white/10 backdrop-blur-sm rounded-xl p-8 text-white blur-card">
                            <div class="mb-4 radio-wave">
                                <svg class="w-12 h-12" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.707.707L4.586 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.586l3.707-3.707a1 1 0 011.09-.217zM14.657 2.929a1 1 0 011.414 0A9.972 9.972 0 0119 10a9.972 9.972 0 01-2.929 7.071a1 1 0 01-1.414-1.414A7.971 7.971 0 0017 10c0-2.21-.894-4.208-2.343-5.657a1 1 0 010-1.414zm-2.829 2.828a1 1 0 011.415 0A5.983 5.983 0 0115 10a5.984 5.984 0 01-1.757 4.243a1 1 0 01-1.415-1.415A3.984 3.984 0 0013 10a3.983 3.983 0 00-1.172-2.828a1 1 0 010-1.415z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <h3 class="text-xl font-bold mb-2">Online Radio</h3>
                            <p class="mb-4 text-primary-100">Listen to the rhythm of the Caribbean with our 24/7 radio stations and podcasts.</p>
                            <a href="#radio" class="inline-flex items-center text-white font-medium">
                                Tune In
                                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                                </svg>
                            </a>
                        </div>

                        <!-- Games Quick Link -->
                        <div class="bg-white/10 backdrop-blur-sm rounded-xl p-8 text-white blur-card">
                            <div class="mb-4">
                                <svg class="w-12 h-12" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M11 17a1 1 0 001.447.894l4-2A1 1 0 0017 15V9.236a1 1 0 00-1.447-.894l-4 2a1 1 0 00-.553.894V17zM15.211 6.276a1 1 0 000-1.788l-4.764-2.382a1 1 0 00-.894 0L4.789 4.488a1 1 0 000 1.788l4.764 2.382a1 1 0 00.894 0l4.764-2.382zM4.447 8.342A1 1 0 003 9.236V15a1 1 0 00.553.894l4 2A1 1 0 009 17v-5.764a1 1 0 00-.553-.894l-4-2z"></path>
                                </svg>
                            </div>
                            <h3 class="text-xl font-bold mb-2">Game Zone</h3>
                            <p class="mb-4 text-primary-100">Challenge yourself with fun games inspired by Caribbean culture and entertainment.</p>
                            <a href="#games" class="inline-flex items-center text-white font-medium">
                                Play Now
                                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Upcoming Events -->
            <div class="container mx-auto px-4 py-16">
                <div class="mb-12">
                    <h2 class="text-3xl font-bold text-secondary-800 mb-2">Upcoming Events</h2>
                    <p class="text-secondary-500">Don't miss these exciting events</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Event 1 -->
                    <div class="card bg-white rounded-xl shadow-md overflow-hidden">
                        <div class="relative h-48 bg-gradient-to-br from-primary-400 to-primary-700 event-date">
                            <div class="absolute inset-0 flex items-center justify-center">
                                <div class="text-center text-white">
                                    <span class="block text-3xl font-bold">15</span>
                                    <span class="block text-sm font-medium">AUG</span>
                                </div>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="flex justify-between items-center mb-4">
                                <span class="text-primary-600 font-semibold">Aug 15, 2023</span>
                                <span class="bg-primary-100 text-primary-700 text-xs px-2 py-1 rounded-full">Virtual</span>
                            </div>
                            <h3 class="text-xl font-semibold mb-2">Caribbean Music Awards</h3>
                            <p class="text-secondary-500 mb-4">Join us for a night celebrating the best in Caribbean music.</p>
                            <button class="w-full bg-primary-600 hover:bg-primary-700 text-white py-2 rounded-lg font-medium">RSVP Now</button>
                        </div>
                    </div>

                    <!-- Event 2 -->
                    <div class="card bg-white rounded-xl shadow-md overflow-hidden">
                        <div class="relative h-48 bg-gradient-to-br from-primary-400 to-primary-700 event-date">
                            <div class="absolute inset-0 flex items-center justify-center">
                                <div class="text-center text-white">
                                    <span class="block text-3xl font-bold">03</span>
                                    <span class="block text-sm font-medium">SEP</span>
                                </div>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="flex justify-between items-center mb-4">
                                <span class="text-primary-600 font-semibold">Sep 3, 2023</span>
                                <span class="bg-primary-100 text-primary-700 text-xs px-2 py-1 rounded-full">In-Person</span>
                            </div>
                            <h3 class="text-xl font-semibold mb-2">Caribbean Food Festival</h3>
                            <p class="text-secondary-500 mb-4">Experience the flavors of the Caribbean at our annual food festival.</p>
                            <button class="w-full bg-primary-600 hover:bg-primary-700 text-white py-2 rounded-lg font-medium">Get Tickets</button>
                        </div>
                    </div>

                    <!-- Event 3 -->
                    <div class="card bg-white rounded-xl shadow-md overflow-hidden">
                        <div class="relative h-48 bg-gradient-to-br from-primary-400 to-primary-700 event-date">
                            <div class="absolute inset-0 flex items-center justify-center">
                                <div class="text-center text-white">
                                    <span class="block text-3xl font-bold">12</span>
                                    <span class="block text-sm font-medium">OCT</span>
                                </div>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="flex justify-between items-center mb-4">
                                <span class="text-primary-600 font-semibold">Oct 12, 2023</span>
                                <span class="bg-primary-100 text-primary-700 text-xs px-2 py-1 rounded-full">Virtual</span>
                            </div>
                            <h3 class="text-xl font-semibold mb-2">Caribbean Film Festival</h3>
                            <p class="text-secondary-500 mb-4">Watch the best films from Caribbean filmmakers around the world.</p>
                            <button class="w-full bg-primary-600 hover:bg-primary-700 text-white py-2 rounded-lg font-medium">Learn More</button>
                        </div>
                    </div>
                </div>

                <div class="mt-8 text-center">
                    <a href="#events" class="inline-flex items-center text-primary-600 hover:text-primary-700 font-medium">
                        View All Events
                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                        </svg>
                    </a>
                </div>
            </div>

            <!-- Newsletter -->
            <div class="bg-secondary-800 py-16">
                <div class="container mx-auto px-4">
                    <div class="max-w-2xl mx-auto text-center">
                        <h2 class="text-3xl font-bold text-white mb-4">Stay Connected</h2>
                        <p class="text-secondary-300 mb-8">Subscribe to our newsletter for the latest updates on shows, events, and exclusive content.</p>

                        <form class="flex flex-col md:flex-row gap-4">
                            <input type="email" placeholder="Your email address" class="flex-grow px-4 py-3 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500" required>
                            <button type="submit" class="bg-primary-600 hover:bg-primary-500 text-white px-6 py-3 rounded-lg font-medium">Subscribe</button>
                        </form>
                    </div>
                </div>
            </div>
        </section>

        <!-- TV PAGE -->
        <section id="tv" class="min-h-screen py-16 bg-secondary-100">
            <div class="container mx-auto px-4">
                <div class="mb-12">
                    <h1 class="text-4xl font-bold text-secondary-800 mb-2">Online TV</h1>
                    <p class="text-secondary-500">Watch your favorite Caribbean shows anytime, anywhere</p>
                </div>

                <!-- TV Tabs -->
                <div class="mb-8">
                    <div class="flex border-b border-secondary-200">
                        <button class="tv-tab active px-6 py-3 font-medium text-primary-700 border-b-2 border-primary-600" data-tab="live">Live TV</button>
                        <button class="tv-tab px-6 py-3 font-medium text-secondary-500 hover:text-primary-700" data-tab="shows">TV Shows</button>
                        <button class="tv-tab px-6 py-3 font-medium text-secondary-500 hover:text-primary-700" data-tab="schedule">Schedule</button>
                    </div>
                </div>

                <!-- Live TV Tab Content -->
                <div id="live-tab" class="tab-content active">
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                        <!-- Live Player -->
                        <div class="lg:col-span-2">
                            <div class="bg-secondary-800 rounded-xl overflow-hidden shadow-lg">
                                <div class="relative aspect-video bg-secondary-900 flex items-center justify-center">
                                    <svg class="w-20 h-20 text-primary-500" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                                    </svg>
                                    <div class="absolute bottom-4 left-4 flex items-center">
                                        <div class="w-3 h-3 bg-white rounded-full mr-2 live-indicator"></div>
                                        <span class="text-white font-medium">LIVE</span>
                                    </div>
                                </div>
                                <div class="p-6">
                                    <h2 class="text-2xl font-bold text-white mb-2">Caribbean Morning Show</h2>
                                    <p class="text-secondary-300 mb-4">Start your day with the latest news, music, and entertainment from across the Caribbean.</p>
                                    <div class="flex items-center text-secondary-400">
                                        <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                                        </svg>
                                        <span>Weekdays • 7AM - 10AM</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Live Sidebar -->
                        <div>
                            <div class="bg-white rounded-xl shadow-md p-6 mb-8">
                                <h3 class="text-lg font-semibold mb-4">Coming Up Next</h3>
                                <div class="space-y-4">
                                    <div class="flex items-start">
                                        <div class="w-16 h-16 bg-primary-100 rounded-lg flex-shrink-0 flex items-center justify-center">
                                            <svg class="w-8 h-8 text-primary-600" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                        <div class="ml-4">
                                            <h4 class="font-medium">Caribbean Cuisine</h4>
                                            <p class="text-sm text-secondary-500">10:00 AM - 11:00 AM</p>
                                        </div>
                                    </div>

                                    <div class="flex items-start">
                                        <div class="w-16 h-16 bg-primary-100 rounded-lg flex-shrink-0 flex items-center justify-center">
                                            <svg class="w-8 h-8 text-primary-600" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                        <div class="ml-4">
                                            <h4 class="font-medium">Island Business</h4>
                                            <p class="text-sm text-secondary-500">11:00 AM - 12:00 PM</p>
                                        </div>
                                    </div>

                                    <div class="flex items-start">
                                        <div class="w-16 h-16 bg-primary-100 rounded-lg flex-shrink-0 flex items-center justify-center">
                                            <svg class="w-8 h-8 text-primary-600" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                        <div class="ml-4">
                                            <h4 class="font-medium">Midday News</h4>
                                            <p class="text-sm text-secondary-500">12:00 PM - 1:00 PM</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="bg-white rounded-xl shadow-md p-6">
                                <h3 class="text-lg font-semibold mb-4">Our Channels</h3>
                                <div class="space-y-3">
                                    <button class="w-full flex items-center justify-between p-3 bg-primary-100 text-primary-700 rounded-lg">
                                        <span class="font-medium">Caribbean Advantage Main</span>
                                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                    </button>

                                    <button class="w-full flex items-center justify-between p-3 hover:bg-primary-50 text-secondary-700 rounded-lg">
                                        <span class="font-medium">Caribbean Sports</span>
                                        <svg class="w-5 h-5 text-secondary-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                    </button>

                                    <button class="w-full flex items-center justify-between p-3 hover:bg-primary-50 text-secondary-700 rounded-lg">
                                        <span class="font-medium">Caribbean Music</span>
                                        <svg class="w-5 h-5 text-secondary-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                    </button>

                                    <button class="w-full flex items-center justify-between p-3 hover:bg-primary-50 text-secondary-700 rounded-lg">
                                        <span class="font-medium">Caribbean Kids</span>
                                        <svg class="w-5 h-5 text-secondary-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- TV Shows Tab Content -->
                <div id="shows-tab" class="tab-content">
                    <!-- Filter Bar -->
                    <div class="bg-white rounded-lg shadow-sm p-4 mb-8 flex flex-wrap gap-4">
                        <div class="flex-grow">
                            <input type="text" placeholder="Search shows..." class="w-full px-4 py-2 border border-secondary-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                        </div>

                        <div class="flex space-x-2">
                            <select class="px-4 py-2 border border-secondary-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                                <option>All Genres</option>
                                <option>News</option>
                                <option>Entertainment</option>
                                <option>Cooking</option>
                                <option>Sports</option>
                                <option>Kids</option>
                            </select>

                            <select class="px-4 py-2 border border-secondary-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500">
                                <option>Latest</option>
                                <option>Most Popular</option>
                                <option>A-Z</option>
                            </select>
                        </div>
                    </div>

                    <!-- Shows Grid -->
                    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                        <!-- Show 1 -->
                        <div class="card bg-white rounded-xl shadow-md overflow-hidden">
                            <div class="relative h-40 bg-gradient-to-br from-primary-400 to-primary-700">
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <div class="absolute top-2 right-2 bg-primary-600 text-white text-xs font-bold px-2 py-1 rounded">NEW</div>
                            </div>
                            <div class="p-4">
                                <h3 class="font-semibold mb-1">Island Vibes</h3>
                                <p class="text-sm text-secondary-500 mb-2">Music & Entertainment</p>
                                <div class="flex justify-between items-center">
                                    <span class="text-xs text-secondary-400">24 episodes</span>
                                    <button class="text-primary-600 hover:text-primary-700 text-sm font-medium">Watch</button>
                                </div>
                            </div>
                        </div>

                        <!-- Show 2 -->
                        <div class="card bg-white rounded-xl shadow-md overflow-hidden">
                            <div class="relative h-40 bg-gradient-to-br from-primary-400 to-primary-700">
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="p-4">
                                <h3 class="font-semibold mb-1">Caribbean Cuisine</h3>
                                <p class="text-sm text-secondary-500 mb-2">Cooking</p>
                                <div class="flex justify-between items-center">
                                    <span class="text-xs text-secondary-400">36 episodes</span>
                                    <button class="text-primary-600 hover:text-primary-700 text-sm font-medium">Watch</button>
                                </div>
                            </div>
                        </div>

                        <!-- Show 3 -->
                        <div class="card bg-white rounded-xl shadow-md overflow-hidden">
                            <div class="relative h-40 bg-gradient-to-br from-primary-400 to-primary-700">
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="p-4">
                                <h3 class="font-semibold mb-1">Caribbean News Today</h3>
                                <p class="text-sm text-secondary-500 mb-2">News</p>
                                <div class="flex justify-between items-center">
                                    <span class="text-xs text-secondary-400">Daily</span>
                                    <button class="text-primary-600 hover:text-primary-700 text-sm font-medium">Watch</button>
                                </div>
                            </div>
                        </div>

                        <!-- Show 4 -->
                        <div class="card bg-white rounded-xl shadow-md overflow-hidden">
                            <div class="relative h-40 bg-gradient-to-br from-primary-400 to-primary-700">
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="p-4">
                                <h3 class="font-semibold mb-1">Island Sports</h3>
                                <p class="text-sm text-secondary-500 mb-2">Sports</p>
                                <div class="flex justify-between items-center">
                                    <span class="text-xs text-secondary-400">18 episodes</span>
                                    <button class="text-primary-600 hover:text-primary-700 text-sm font-medium">Watch</button>
                                </div>
                            </div>
                        </div>

                        <!-- Show 5 -->
                        <div class="card bg-white rounded-xl shadow-md overflow-hidden">
                            <div class="relative h-40 bg-gradient-to-br from-primary-400 to-primary-700">
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="p-4">
                                <h3 class="font-semibold mb-1">Caribbean Travels</h3>
                                <p class="text-sm text-secondary-500 mb-2">Travel</p>
                                <div class="flex justify-between items-center">
                                    <span class="text-xs text-secondary-400">12 episodes</span>
                                    <button class="text-primary-600 hover:text-primary-700 text-sm font-medium">Watch</button>
                                </div>
                            </div>
                        </div>

                        <!-- Show 6 -->
                        <div class="card bg-white rounded-xl shadow-md overflow-hidden">
                            <div class="relative h-40 bg-gradient-to-br from-primary-400 to-primary-700">
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="p-4">
                                <h3 class="font-semibold mb-1">Island Business</h3>
                                <p class="text-sm text-secondary-500 mb-2">Business</p>
                                <div class="flex justify-between items-center">
                                    <span class="text-xs text-secondary-400">20 episodes</span>
                                    <button class="text-primary-600 hover:text-primary-700 text-sm font-medium">Watch</button>
                                </div>
                            </div>
                        </div>

                        <!-- Show 7 -->
                        <div class="card bg-white rounded-xl shadow-md overflow-hidden">
                            <div class="relative h-40 bg-gradient-to-br from-primary-400 to-primary-700">
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="p-4">
                                <h3 class="font-semibold mb-1">Caribbean Kids</h3>
                                <p class="text-sm text-secondary-500 mb-2">Kids</p>
                                <div class="flex justify-between items-center">
                                    <span class="text-xs text-secondary-400">42 episodes</span>
                                    <button class="text-primary-600 hover:text-primary-700 text-sm font-medium">Watch</button>
                                </div>
                            </div>
                        </div>

                        <!-- Show 8 -->
                        <div class="card bg-white rounded-xl shadow-md overflow-hidden">
                            <div class="relative h-40 bg-gradient-to-br from-primary-400 to-primary-700">
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <div class="absolute top-2 right-2 bg-primary-600 text-white text-xs font-bold px-2 py-1 rounded">NEW</div>
                            </div>
                            <div class="p-4">
                                <h3 class="font-semibold mb-1">Island Beats</h3>
                                <p class="text-sm text-secondary-500 mb-2">Music</p>
                                <div class="flex justify-between items-center">
                                    <span class="text-xs text-secondary-400">8 episodes</span>
                                    <button class="text-primary-600 hover:text-primary-700 text-sm font-medium">Watch</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Pagination -->
                    <div class="mt-8 flex justify-center">
                        <nav class="flex items-center space-x-2">
                            <button class="px-3 py-1 rounded-md bg-white border border-secondary-200 text-secondary-500 hover:bg-primary-50">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </button>
                            <button class="px-3 py-1 rounded-md bg-primary-600 text-white">1</button>
                            <button class="px-3 py-1 rounded-md bg-white border border-secondary-200 text-secondary-700 hover:bg-primary-50">2</button>
                            <button class="px-3 py-1 rounded-md bg-white border border-secondary-200 text-secondary-700 hover:bg-primary-50">3</button>
                            <span class="text-secondary-500">...</span>
                            <button class="px-3 py-1 rounded-md bg-white border border-secondary-200 text-secondary-700 hover:bg-primary-50">8</button>
                            <button class="px-3 py-1 rounded-md bg-white border border-secondary-200 text-secondary-500 hover:bg-primary-50">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </button>
                        </nav>
                    </div>
                </div>

                <!-- Schedule Tab Content -->
                <div id="schedule-tab" class="tab-content">
                    <div class="bg-white rounded-xl shadow-md p-6">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-xl font-semibold">Today's Schedule</h3>
                            <div class="flex space-x-2">
                                <button class="px-4 py-2 bg-primary-600 text-white rounded-lg">Today</button>
                                <button class="px-4 py-2 bg-white border border-secondary-200 text-secondary-700 rounded-lg hover:bg-primary-50">Tomorrow</button>
                                <button class="px-4 py-2 bg-white border border-secondary-200 text-secondary-700 rounded-lg hover:bg-primary-50">This Week</button>
                            </div>
                        </div>

                        <div class="space-y-4">
                            <!-- Morning Block -->
                            <div>
                                <h4 class="text-lg font-medium text-primary-700 mb-3">Morning</h4>
                                <div class="space-y-3">
                                    <div class="flex items-center p-3 bg-primary-50 rounded-lg">
                                        <div class="w-16 text-center">
                                            <span class="text-secondary-500 font-medium">7:00 AM</span>
                                        </div>
                                        <div class="ml-4 flex-grow">
                                            <h5 class="font-medium">Caribbean Morning Show</h5>
                                            <p class="text-sm text-secondary-500">Start your day with the latest news and entertainment</p>
                                        </div>
                                        <div class="flex items-center">
                                            <div class="w-2 h-2 bg-primary-600 rounded-full mr-2 live-indicator"></div>
                                            <span class="text-xs font-semibold text-primary-600">LIVE NOW</span>
                                        </div>
                                    </div>

                                    <div class="flex items-center p-3 hover:bg-primary-50 rounded-lg">
                                        <div class="w-16 text-center">
                                            <span class="text-secondary-500 font-medium">10:00 AM</span>
                                        </div>
                                        <div class="ml-4 flex-grow">
                                            <h5 class="font-medium">Caribbean Cuisine</h5>
                                            <p class="text-sm text-secondary-500">Explore the rich flavors of the islands</p>
                                        </div>
                                        <button class="text-primary-600 hover:text-primary-700">
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                                            </svg>
                                        </button>
                                    </div>

                                    <div class="flex items-center p-3 hover:bg-primary-50 rounded-lg">
                                        <div class="w-16 text-center">
                                            <span class="text-secondary-500 font-medium">11:00 AM</span>
                                        </div>
                                        <div class="ml-4 flex-grow">
                                            <h5 class="font-medium">Island Business</h5>
                                            <p class="text-sm text-secondary-500">Business news and insights from across the Caribbean</p>
                                        </div>
                                        <button class="text-primary-600 hover:text-primary-700">
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Afternoon Block -->
                            <div>
                                <h4 class="text-lg font-medium text-primary-700 mb-3">Afternoon</h4>
                                <div class="space-y-3">
                                    <div class="flex items-center p-3 hover:bg-primary-50 rounded-lg">
                                        <div class="w-16 text-center">
                                            <span class="text-secondary-500 font-medium">12:00 PM</span>
                                        </div>
                                        <div class="ml-4 flex-grow">
                                            <h5 class="font-medium">Midday News</h5>
                                            <p class="text-sm text-secondary-500">The latest headlines from around the region</p>
                                        </div>
                                        <button class="text-primary-600 hover:text-primary-700">
                                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                                            </svg>
                                        </button>
                                    </div>

                                    <div class="flex items-center p-3 hover:bg-primary-50 rounded-lg">
                                        <div class="w-16 text-center">
                                            <span class="text-secondary-500 font-medium">1:00 PM</span>
                                        </div>
                                        <div class="ml-4 flex-grow">
                                            <script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'947a9640f4d3a052',t:'MTc0ODU2OTg5Mi4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script>